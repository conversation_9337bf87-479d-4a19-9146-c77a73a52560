package com.phonecheck.mqtt.subscriber;

import com.phonecheck.model.mqtt.messages.MqttTopicMessage;
import io.netty.handler.codec.mqtt.MqttQoS;
import org.eclipse.paho.client.mqttv3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Objects;

/**
 * {@link MqttTopicSubscriber} that defines a unique ID and implements hashCode() and equals()
 */
public abstract class AbstractMqttTopicSubscriber implements MqttTopicSubscriber, MqttCallback {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractMqttTopicSubscriber.class);
    private final String id = getClass().getName() + System.currentTimeMillis();

    @Override
    public String getId() {
        return id;
    }

    /**
     * Adapter method to convert the Paho client's message event into an uncoupled generic event
     *
     * @param topic   name of the topic on which the message was published
     * @param message the actual message
     * @throws Exception
     */
    @Override
    public void messageArrived(final String topic, final MqttMessage message) throws Exception {
        String payload = "";
        if (null != message.getPayload()) {
            payload = new String(message.getPayload(), StandardCharsets.UTF_8);
        }
        // Convert to an MqttTopicMessage that subclasses can consume
        onMessage(new MqttTopicMessage(topic, payload));
    }

    @Override
    public void connectionLost(final Throwable cause) {
        LOGGER.error("Exception occurred while processing mqtt message.", cause);
    }

    @Override
    public void deliveryComplete(final IMqttDeliveryToken token) {
    }

    @Override
    public boolean equals(final Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AbstractMqttTopicSubscriber that = (AbstractMqttTopicSubscriber) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    /**
     * Subscribes to the subclass' specified topics when the Spring "application ready" event is published
     *
     * @param event Spring's "application ready" event
     */
    @EventListener
    public void onApplicationEvent(final ApplicationReadyEvent event) {
        IMqttAsyncClient client = getMqttClient();
        String[] topics = getTopics();

        if (client.isConnected()) {
            subscribe();
        } else {
            LOGGER.debug("Cannot subscribe to topics {}, MQTT client is not connected to the broker.",
                    Arrays.toString(topics));
        }
    }

    /**
     * Subscribes to this subscriber's specified topics upon class instantiation
     */
    protected void subscribe() {
        IMqttAsyncClient client = getMqttClient();
        String[] topics = getTopics();

        try {
            LOGGER.debug("Subscribing as client ID {}, {}", client.getClientId(), getClass().getSimpleName());
            int[] qoses = new int[topics.length];
            Arrays.fill(qoses, MqttQoS.EXACTLY_ONCE.value());
            client.subscribe(topics, qoses);
            client.setCallback(this);
        } catch (MqttException e) {
            LOGGER.error("Exception occurred while subscribing to MQTT Topics: {}", Arrays.toString(topics), e);
        }
    }

    protected abstract IMqttAsyncClient getMqttClient();

    protected void setDeviceIdInMDC(final String deviceId) {
        MDC.clear();
        MDC.put("id", deviceId);
    }

}
