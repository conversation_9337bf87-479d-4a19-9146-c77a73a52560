package com.phonecheck.mqtt;

import io.moquette.BrokerConstants;
import io.moquette.broker.Server;
import io.moquette.broker.config.MemoryConfig;
import org.eclipse.paho.client.mqttv3.IMqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Scope;

import java.io.IOException;
import java.util.Properties;
import java.util.Random;

@Configuration
public class MqttConfiguration {
    private static final Logger LOGGER = LoggerFactory.getLogger(MqttConfiguration.class);
    @Value("${mqtt.scheme}")
    private String scheme;
    @Value("${mqtt.host}")
    private String host;
    @Value("${mqtt.port}")
    private String port;
    @Value("${mqtt.client.id}")
    private String clientId;
    @Value("${mqtt.autoReconnect}")
    private Boolean autoReconnect;
    @Value("${mqtt.maxReconnectDelay}")
    private Integer maxReconnectDelay;
    @Value("${mqtt.connectionTimeout}")
    private Integer connectionTimeout;
    @Value("${mqtt.keepAlive}")
    private Integer keepAlive;
    @Value("${mqtt.maxInFlight}")
    private Integer maxInFlight;
    private final Random random = new Random();

    // fake server bean when there is no server needed
    @Bean ("mqttServer")
    @ConditionalOnProperty(name = "mqtt.server.enabled", havingValue = "false")
    public Server getFakeServer() {
        return null;
    }

    @Bean ("mqttServer")
    @ConditionalOnProperty(name = "mqtt.server.enabled", havingValue = "true")
    public Server getServer() {
        Properties properties = new Properties();
        properties.setProperty(BrokerConstants.NETTY_MAX_BYTES_PROPERTY_NAME, "131072"); // 128KB
        MemoryConfig serverConfig = new MemoryConfig(properties);
        serverConfig.setProperty("host", host);
        serverConfig.setProperty("port", String.valueOf(port));
        Server server = new Server();
        Runtime.getRuntime().addShutdownHook(new Thread(server::stopServer));
        try {
            server.startServer(serverConfig);
            LOGGER.info("Broker now listening at {}:{}", host, port);
        } catch (IOException e) {
            LOGGER.error("Could not start broker", e);
            throw new RuntimeException(e);
        }
        return server;
    }

    @Bean
    @DependsOn("mqttServer")
    @Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
    public IMqttAsyncClient getMqttClient() throws MqttException {
        final IMqttAsyncClient client = new MqttAsyncClient(scheme + host + ":" + port,
                clientId + System.currentTimeMillis() + "-" + random.nextInt(), new MemoryPersistence());
        try {
            // We attempt to immediately connect to the broker so the connection options don't have to be repeated
            // in a bunch of different places
            client.connect(createConnectionOptions()).waitForCompletion();
            LOGGER.info("MQTTClient: {} is now connected.", client.getClientId());
        } catch (MqttException e) {
            LOGGER.error("Could not connect to broker", e);
        }
        return client;
    }

    protected MqttConnectOptions createConnectionOptions() {
        MqttConnectOptions options = new MqttConnectOptions();
        options.setAutomaticReconnect(autoReconnect);
        options.setMaxReconnectDelay(maxReconnectDelay);
        options.setConnectionTimeout(connectionTimeout);
        options.setKeepAliveInterval(keepAlive);
        options.setMaxInflight(maxInFlight);
        return options;
    }
}
