package com.phonecheck.mqtt;

import io.moquette.broker.Server;
import lombok.AllArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

/**
 * Manages the MQTT server startup and shutdown
 */
@Service
@ConditionalOnProperty(value = "mqtt.server.enabled")
@AllArgsConstructor
public class MqttServerService {

    private Server server;
    private MqttStatusPrinter mqttStatusPrinter;

    public void init() {
        server.addInterceptHandler(mqttStatusPrinter);
    }

}
