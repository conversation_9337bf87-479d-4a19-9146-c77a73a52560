package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.cloudapi.ImeiTacResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class ManualEntryImeiTacService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ManualEntryImeiTacService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    public ImeiTacResponse getManualEntryImeiTacResponse(final String imei) {
        ImeiTacResponse response = null;
        try {
            response = phonecheckApiRestClient.getImeiTacResponse(imei);
        } catch (final RestClientException e) {
            LOGGER.error("Error while getting manual entry imei tac response", e);
        }
        return response;
    }
}