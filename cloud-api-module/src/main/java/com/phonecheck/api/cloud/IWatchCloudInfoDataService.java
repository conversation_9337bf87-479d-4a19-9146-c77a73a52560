package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.GetIWatchInfoResponse;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class IWatchCloudInfoDataService {
    private static final Logger LOGGER = LoggerFactory.getLogger(IWatchCloudInfoDataService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Get iWatch info data from cloud
     *
     * @param userToken      user token
     * @param masterToken    master token
     * @param iWatchSerialNo iWatch serial number
     * @return GetIWatchInfoResponse
     */
    public GetIWatchInfoResponse getIWatchInfoData(final String userToken, final String masterToken,
                                                   final String iWatchSerialNo) {
        try {
            GetIWatchInfoResponse response =
                    phonecheckApiRestClient.getIWatchInfoData(userToken, masterToken, iWatchSerialNo);
            LOGGER.info("IWatchInfoData Response from cloud: {}", response);
            if (response != null && response.getData() != null) {
                return response;
            }
        } catch (final RestClientException e) {
            LOGGER.error("Failed to get iWatch info data from cloud", e);
        }
        return null;
    }

    /**
     * Get iWatch host enrollment status from cloud
     *
     * @param udid device udid
     * @return status
     */
    public boolean getIWatchHostEnrollmentStatus(final String udid) {
            boolean status = phonecheckApiRestClient.getIWatchHostEnrollmentStatus(udid);
            LOGGER.info("getIWatchHostEnrollmentStatus Response from cloud: {}", status);
            return status;
    }

    /**
     * Get iWatch erase request status from cloud
     *
     * @param watchUdid device udid
     * @return status
     */
    public boolean requestIWatchEraseFromCloud(final String watchUdid) {
            String response = phonecheckApiRestClient.getIWatchEraseRequestStatus(watchUdid);
            LOGGER.info("getIWatchEraseRequestStatus Response from cloud: {}", response);
            return StringUtils.isNotBlank(response) &&
                    StringUtils.containsIgnoreCase(response, "Device Erasing");
    }
}
