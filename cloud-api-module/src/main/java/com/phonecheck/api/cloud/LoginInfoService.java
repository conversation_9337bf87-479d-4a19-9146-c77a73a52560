package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.CloudPhoneCheckApiRestClient;
import com.phonecheck.model.phonecheckapi.StationLoginInfoResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class LoginInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LoginInfoService.class);
    private final CloudPhoneCheckApiRestClient cloudPhoneCheckApiRestClient;
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get station login response for the given username and password
     *
     * @param username Station login username
     * @param password Station login password
     * @param pcId     Station pcId/uuId
     * @return StationLoginResponse
     */
    public StationLoginInfoResponse getStationLoginInfo(
            final String username, final String password, final String pcId) {
        StationLoginInfoResponse response = null;
        try {
            response = cloudPhoneCheckApiRestClient.getStationLoginInfo(username, password, pcId);
        } catch (final RestClientException e) {
            LOGGER.error("Error getting station login info for user", e);
        }
        return response;
    }

    /**
     * Get tester login response for the given username and password
     *
     * @param username Tester login username
     * @param password Tester login password
     * @param pcId     Station pcId/uuId
     * @return StationLoginResponse
     */
    public StationLoginInfoResponse getTesterLoginInfo(
            final String username, final String password, final String pcId) {
        StationLoginInfoResponse response = null;
        try {
            response = cloudApiRestClient.getTesterLoginInfo(username, password, pcId);
        } catch (final RestClientException e) {
            LOGGER.error("Error getting tester login info for user", e);
        }
        return response;
    }
}
