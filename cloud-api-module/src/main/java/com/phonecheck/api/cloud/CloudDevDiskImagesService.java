package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.DevDiskImagesResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudDevDiskImagesService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudDevDiskImagesService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get Dev Disk Images from cloud
     *
     * @return DevDiskImagesResponse
     */
    public DevDiskImagesResponse getDevDiskImages() {
        try {
            return cloudApiRestClient.getDevDiskImages();
        } catch (final RestClientException e) {
            LOGGER.error("Error calling get dev disk images", e);
            return null;
        }
    }
}
