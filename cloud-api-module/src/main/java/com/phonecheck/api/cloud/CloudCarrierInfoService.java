package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CarrierInfoResponse;
import com.phonecheck.model.service.CarrierInfoDb;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudCarrierInfoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudCarrierInfoService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get the Carrier info with the given version
     *
     * @param version carrierInfo version
     * @return List of CarrierInfo
     */
    public CarrierInfoDb getCarrierInfo(final String version) {
        try {
            final CarrierInfoResponse response = cloudApiRestClient.getLatestCarrierInfo(version);

            return response != null ? CarrierInfoDb.builder()
                    .carrierInfo(response.getCarrierInfoList())
                    .build() : null;
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get latest carrier info", e);
            return null;
        }
    }
}
