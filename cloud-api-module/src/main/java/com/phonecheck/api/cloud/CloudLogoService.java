package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.LogoResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudLogoService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudLogoService.class);

    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get logos for the given master id
     *
     * @param masterId master id
     * @return LogoResponse
     */
    public LogoResponse getLogos(final String masterId) {
        try {
            return cloudApiRestClient.getLogos(masterId);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get logos", e);
            return null;
        }
    }
}
