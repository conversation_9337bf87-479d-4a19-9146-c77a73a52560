package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.cloudapi.AndroidColorInfoResponse;
import com.phonecheck.model.cloudapi.IosColorCodeResponse;
import com.phonecheck.model.cloudapi.IosColorInfoResponse;
import com.phonecheck.model.cloudapi.PendingColorInfoRequest;
import com.phonecheck.model.service.AndroidColorDb;
import com.phonecheck.model.service.IosColorDb;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudColorService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudColorService.class);
    private final CloudApiRestClient cloudApiRestClient;
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Send the pending color info to the cloud db api
     *
     * @param modelNo device model
     * @param model device title
     * @param imei device imei no
     * @param serial device serial no
     * @param colorCode device color code
     * @param userId the user id
     *
     */
    public void sendPendingColorToCloudDb(
            final String modelNo,
            final String model,
            final String imei,
            final String serial,
            final String colorCode,
            final String userId
    ) {
        try {
            PendingColorInfoRequest pendingColorInfoRequest = PendingColorInfoRequest.builder()
                    .modelNo(StringUtils.isNotBlank(modelNo) ? modelNo : StringUtils.EMPTY)
                    .model(StringUtils.isNotBlank(model) ? model : StringUtils.EMPTY)
                    .imei(StringUtils.isNotBlank(imei) ? imei : StringUtils.EMPTY)
                    .serial(StringUtils.isNotBlank(serial) ? serial : StringUtils.EMPTY)
                    .colorCode(StringUtils.isNotBlank(colorCode) ? colorCode : StringUtils.EMPTY)
                    .userId(userId)
                    .build();
            final String response = cloudApiRestClient.syncPendingColorInfoToCloud(pendingColorInfoRequest);
            if (StringUtils.containsIgnoreCase(response, "successfully")) {
                LOGGER.info("Pending color info successfully synced to cloud with response: {}", response);
            } else {
                LOGGER.info("Pending color info sync is not succeed with cloud response: {}", response);
            }
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling pending color info to cloud db", e);
        }
    }

    /**
     * Get the Ios Color Database from the cloud api
     *
     * @param version
     * @return an Ios color info list
     */
    public IosColorDb getIosColorDb(final String version) {
        try {
            final IosColorInfoResponse response = cloudApiRestClient.getLatestIosColorInfo(version);

            return IosColorDb.builder()
                    .iosColorInfos(response.getIosColorInfos())
                    .build();
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get IOS color db", e);
            return null;
        }
    }

    /**
     * Get the Android Color Database from the cloud api
     *
     * @param version
     * @return an Android color info list
     */
    public AndroidColorDb getAndroidColorDb(final String version) {
        try {
            final AndroidColorInfoResponse response = cloudApiRestClient.getLatestAndroidColorInfo(version);

            return AndroidColorDb.builder()
                    .colorInfoList(response.getColorInfoList())
                    .build();
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get android color db", e);
            return null;
        }
    }

    /**
     * Get all ios device color codes from cloud
     *
     * @param userToken user token
     * @param masterToken master token
     * @return IosColorCodeResponse containing all color codes for IOS devices
     */
    public IosColorCodeResponse getAllIosColorCodes(final String userToken, final String masterToken) {
        try {
            return phonecheckApiRestClient.getAllIosColorCodes(userToken, masterToken);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get ios color codes", e);
            return null;
        }
    }
}
