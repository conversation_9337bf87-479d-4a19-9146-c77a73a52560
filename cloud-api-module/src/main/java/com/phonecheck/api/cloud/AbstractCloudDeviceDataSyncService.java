package com.phonecheck.api.cloud;

import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.test.OemStatus;
import com.phonecheck.model.util.TestResultsUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Getter
@Service
@AllArgsConstructor
public abstract class AbstractCloudDeviceDataSyncService {

    private final InMemoryStore inMemoryStore;
    private static final String BH_NOT_SUPPORTED = "BH not supported";

    /**
     * Combine failed test results and failed cosmetics
     *
     * @param deviceTestResult device test results
     * @param failedCosmetics  passed cosmetic results
     * @return String
     */
    public String getAllFailedTestResults(final DeviceTestResult deviceTestResult, final String failedCosmetics) {
        String failedTestResults = null;
        String allFailedTestResults = null;

        if (deviceTestResult != null && deviceTestResult.getTestResults() != null &&
                deviceTestResult.getTestResults().getFailed() != null) {
            failedTestResults = TestResultsUtil.listToCommaSeparatedString(
                    deviceTestResult.getTestResults().getFailed());
        }

        if (StringUtils.isNotBlank(failedCosmetics)) {
            allFailedTestResults = failedCosmetics;
        }
        if (StringUtils.isNotBlank(failedTestResults)) {
            if (allFailedTestResults != null) {
                allFailedTestResults = StringUtils.joinWith(",", allFailedTestResults, failedTestResults);
            } else {
                allFailedTestResults = failedTestResults;
            }
        }
        return allFailedTestResults;
    }

    /**
     * Combine passed test results and passed cosmetics
     *
     * @param deviceTestResult device test results
     * @param passedCosmetics  passed cosmetic results
     * @return String
     */
    public String getAllPassedTestResults(final DeviceTestResult deviceTestResult, final String passedCosmetics) {
        String passedTestResults = null;
        String allPassedTestResults = null;
        if (deviceTestResult != null && deviceTestResult.getTestResults() != null &&
                deviceTestResult.getTestResults().getPassed() != null) {
            passedTestResults = TestResultsUtil.listToCommaSeparatedString(
                    deviceTestResult.getTestResults().getPassed());
        }
        if (StringUtils.isNotBlank(passedCosmetics)) {
            allPassedTestResults = passedCosmetics;
        }
        if (StringUtils.isNotBlank(passedTestResults)) {
            if (allPassedTestResults != null) {
                allPassedTestResults = StringUtils.joinWith(",", allPassedTestResults, passedTestResults);
            } else {
                allPassedTestResults = passedTestResults;
            }
        }
        return allPassedTestResults;
    }

    /**
     * Battery Health will be returned as "-" when the device's Battery OEM status is
     * NOT_GENUINE and the language is Japanese. Else return the actual value.
     *
     * @param device device
     * @return battery health string value
     */
    public String getBatteryHealthValue(final Device device) {
        if (device.getBatteryInfo() != null) {
            if (device instanceof IosDevice iosDevice) {
                if (getInMemoryStore().getCurrentLanguage().equals("Japanese") &&
                        ((OemStatus.NOT_GENUINE.equals((iosDevice.getOemBatteryStatus()))) ||
                                iosDevice.getBatteryInfo().getHealthPercentage() == 0)) {
                    return "-";
                } else {
                    return device.getBatteryInfo().getHealthPercentage() == 0 ? BH_NOT_SUPPORTED :
                            String.valueOf(device.getBatteryInfo().getHealthPercentage());
                }
            } else {
                return device.getBatteryInfo().getHealthPercentage() == 0 ? BH_NOT_SUPPORTED :
                        String.valueOf(device.getBatteryInfo().getHealthPercentage());
            }
        } else {
            return StringUtils.EMPTY;
        }
    }

    /**
     * Method to calculate device eSim supported status for iOS and Android devices
     *
     * @param device target device
     * @return boolean flag that represents eSim supported status
     */
    public boolean getESimSupported(final Device device) {
        // For iWatch devices if it is cellular == true that means it support eSim
        boolean isInIWatchMode = inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST;
        boolean isIWatchCellular = StringUtils.isNotBlank(device.getImei())
                && !StringUtils.equalsIgnoreCase(device.getImei(), "N/A");
        if (isInIWatchMode) {
            return isIWatchCellular;
        }
        // For iOS devices If EID is empty, then the device is not eSim capable. So eSim erase is not
        // applicable hence N/A is returned
        // For Android we check for the iESimSupported flag in device object
        return (device instanceof IosDevice iosDevice && StringUtils.isNotBlank(iosDevice.getEid()))
                || device instanceof AndroidDevice androidDevice && androidDevice.isESimSupported();
    }

    /**
     * Method to calculate device eSim erased status for iOS and Android devices
     *
     * @param device target device
     * @return string representing eSim erase status
     */
    public String getESimErased(final Device device) {
        if (!getESimSupported(device)) {
            return StringUtils.EMPTY;
        }

        String eSimErased = "No";
        if (device.isESimErased() &&
                ((device.getEraseStartTime() != null && device.getEraseStartTime() > 0) ||
                        (device.getRestoreStartTime() != null && device.getRestoreStartTime() > 0))) {
            eSimErased = "Yes";
        }
        return eSimErased;
    }

}
