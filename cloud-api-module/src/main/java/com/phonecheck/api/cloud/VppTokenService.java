package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.VppTokenResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class VppTokenService {
    private static final Logger LOGGER = LoggerFactory.getLogger(VppTokenService.class);

    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Retrieves the vpp token from the cloud
     *
     * @return VppTokenResponse
     */
    public VppTokenResponse getVppToken() {
        try {
            return cloudApiRestClient.getVppToken();
        } catch (final RestClientException e) {
            LOGGER.error("Error calling get vpp token", e);
            return null;
        }
    }
}
