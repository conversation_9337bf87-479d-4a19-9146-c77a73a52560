package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.dao.model.PendingSyncRecord;
import com.phonecheck.dao.service.PendingSyncRecordsDBService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.battery.BatteryInfo;
import com.phonecheck.model.cloudapi.CloudDbSyncRequest;
import com.phonecheck.model.cloudapi.EditTransactionSyncRequest;
import com.phonecheck.model.cloudapi.UpdateByColumnRequest;
import com.phonecheck.model.cloudapi.UpdateDeviceDisconnectRequest;
import com.phonecheck.model.constants.DeviceEraseType;
import com.phonecheck.model.device.*;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.stage.DeviceState;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.mdm.MdmStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;

import static com.phonecheck.model.device.stage.DeviceStage.*;
import static com.phonecheck.model.util.DateFormatUtil.DATE_TIME_FORMAT;
import static com.phonecheck.model.util.DeviceCosmeticResultsUtil.getAllCosmeticTestResults;

@Service
public class CloudDeviceDataSyncService extends AbstractCloudDeviceDataSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudDeviceDataSyncService.class);
    public static final String SUCCESS = "success";
    private static final int LOAD_PENDING_DATA_FROM_DB_INTERVAL = 3 * 60 * 1000; // 3 Minutes
    private static final List<DeviceStage> STAGES_TO_CALL_CLOUD_DB_SYNC = List.of(
            PAIRING_SUCCEEDED,
            ACTIVATION_FAILED,
            READY,
            NOT_READY,
            APP_TESTING_DONE,
            DISCONNECTED,
            EDIT_TRANSACTION,
            MANUAL_ENTRY,
            BATTERY_INFO_COLLECTION_SUCCESS,
            OEM_DATA_COLLECTION_SUCCEEDED,
            ICLOUD_INFO_SUCCEEDED,
            MDM_SUCCEEDED,
            ESN_SUCCESS,
            KNOX_INFO_SUCCEEDED,
            INFO_COLLECTION_SUCCEEDED
    );

    private final PendingSyncRecordsDBService pendingSyncRecordsDbService;
    private final CloudApiRestClient cloudApiRestClient;
    private final Set<Device> deviceSetToSync;
    private final OsChecker osChecker;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final LocalizationService localizationService;
    private final InMemoryStore inMemoryStore;

    public CloudDeviceDataSyncService(final CloudApiRestClient cloudApiRestClient,
                                      final PendingSyncRecordsDBService pendingSyncRecordsDbService,
                                      final InMemoryStore inMemoryStore,
                                      final OsChecker osChecker,
                                      final DeviceConnectionTracker deviceConnectionTracker,
                                      final LocalizationService localizationService,
                                      final InMemoryStore inMemoryStore1) {
        super(inMemoryStore);
        this.cloudApiRestClient = cloudApiRestClient;
        this.pendingSyncRecordsDbService = pendingSyncRecordsDbService;
        this.osChecker = osChecker;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.localizationService = localizationService;
        this.inMemoryStore = inMemoryStore1;

        deviceSetToSync = Collections.synchronizedSet(new LinkedHashSet<>());
    }

    /**
     * Method to sync device data instantly to cloud without adding it to queue
     *
     * @param transaction Current transaction
     * @param device      device to sync on cloud
     * @param source      calling source
     * @return response
     */
    public String syncDeviceRecordOnCloud(final Transaction transaction, final Device device, final Object source) {
        // Sync device Data to the cloud
        CloudDbSyncRequest cloudDbSyncRequest = buildCloudSyncRequest(transaction, device);
        LOGGER.info("{} is making immediate CloudDbSync request: {}", source, cloudDbSyncRequest);
        return cloudApiRestClient.syncDataToCloudDb(cloudDbSyncRequest);
    }

    /**
     * Method to sync device data instantly to cloud without adding it to queue
     *
     * @param device device to sync on cloud
     * @param source calling source
     * @return response
     */
    public String syncDeviceDisconnectOnCloud(final Device device, final Object source) {
        // Sync device Data to the cloud
        UpdateDeviceDisconnectRequest updateDeviceDisconnectRequest =
                buildUpdateDeviceDisconnectRequest(device);
        LOGGER.info("{} is making device disconnect time sync request: {}", source, updateDeviceDisconnectRequest);
        return cloudApiRestClient.syncDeviceDisconnectToCloudDb(updateDeviceDisconnectRequest);
    }

    /**
     * Method to load all pending devices records to be synchronized from
     * local database.
     */
    @Scheduled(fixedRate = LOAD_PENDING_DATA_FROM_DB_INTERVAL, initialDelay = 60000)
    private void loadPendingDevicesRecordsToSyncFromDB() {
        // Thread to load device records pending to be synced from database
        try {
            List<SyncableDevice> devicesToSync = pendingSyncRecordsDbService.getDeviceRecordsToBeSync();
            if (devicesToSync != null && !devicesToSync.isEmpty()) {
                for (SyncableDevice syncableDevice : devicesToSync) {

                    LOGGER.info("Found pending device record in DB to be synced to cloud: {}", syncableDevice);
                    Device deviceInTracker = deviceConnectionTracker.getDevice(syncableDevice.getDevice().getId());
                    final Transaction transactionFromDB = syncableDevice.getTransaction();
                    if (deviceInTracker != null &&
                            transactionFromDB.getTransactionId() ==
                                    getInMemoryStore().getTransaction().getTransactionId()) {
                        LOGGER.info("Device is already present in the connection tracker with same transactionId. "
                                        + "No need to sync it separately Device Id: {} , Transaction Id :{}",
                                deviceInTracker.getId(), transactionFromDB.getTransactionId());
                        continue;
                    }
                    syncDevicesRecordsOnCloud(syncableDevice.getDevice(), transactionFromDB);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while processing pending syncable records from local database.", e);
        }
    }

    /**
     * Method to sync all pending devices records to the cloud.
     *
     * @param device      device to be synced
     * @param transaction transaction of the device to be synced
     */
    private void syncDevicesRecordsOnCloud(final Device device, final Transaction transaction) {
        // Thread to sync pending device records to cloud via CloudDbSync or UpdateByColumn
        MDC.put("id", device.getId());
        try {
            String response;

            if (device.getStage() == null) {
                device.setStage(DISCONNECTED);
            }

            prependInitialDefectsToTestResults(device);

            boolean shouldCallCloudSyncRequest = STAGES_TO_CALL_CLOUD_DB_SYNC.contains(device.getStage());

            LOGGER.info("Device stage while syncing data on cloud: {}, shouldCallCloudSyncRequest: {}",
                    device.getStage(), shouldCallCloudSyncRequest);

            if (shouldCallCloudSyncRequest) {
                CloudDbSyncRequest cloudDbSyncRequest = buildCloudSyncRequest(transaction, device);
                LOGGER.info("CloudDbSync API request: {}", cloudDbSyncRequest);
                response = cloudApiRestClient.syncDataToCloudDb(cloudDbSyncRequest);
                LOGGER.info("CloudDBSync API received response: {}", response);
            } else {
                UpdateByColumnRequest updateByColumnRequest = buildUpdateByColumnRequest(device);
                LOGGER.info("UpdateByColumn API request: {}", updateByColumnRequest);
                response = cloudApiRestClient.syncColumnInfoToCloudDb(updateByColumnRequest);
                LOGGER.info("UpdateByColumn API response: {}", response);

                // If the response is not successful, call syncDataToCloudDb as a fallback method
                if (!StringUtils.containsIgnoreCase(response, SUCCESS)) {
                    CloudDbSyncRequest cloudDbSyncRequest =
                            buildCloudSyncRequest(getInMemoryStore().getTransaction(), device);
                    LOGGER.info("Fallback to CloudDbSync API request: {}", cloudDbSyncRequest);
                    response = cloudApiRestClient.syncDataToCloudDb(cloudDbSyncRequest);
                    LOGGER.info("CloudDBSync API received response: {}", response);
                }
            }

            if (!StringUtils.containsIgnoreCase(response, SUCCESS)) {
                // if sync failed then put the record back into the Queue for reprocessing later
                pendingSyncRecordsDbService.insertPendingSyncRecord(
                        PendingSyncRecord
                                .builder()
                                .transactionId(transaction.getTransactionId())
                                .licenseId(getInMemoryStore().getTransaction().getLicenseId())
                                .deviceSerial(device.getSerial())
                                .deviceStage(device.getStage())
                                .createdTimestamp(new Timestamp(System.currentTimeMillis()))
                                .build()
                );
            }
        } catch (Exception e) {
            LOGGER.error("Exception occurred while synchronizing devices data to cloud", e);
        }
        MDC.clear();
    }

    // ==================================================================
    //          Methods related to CloudDbSync request building
    // ==================================================================

    private CloudDbSyncRequest buildCloudSyncRequest(final Transaction transaction, final Device device) {
        CloudDbSyncRequest.CloudDbSyncRequestBuilder builder = CloudDbSyncRequest.builder();

        builder.errorCode(getInMemoryStore().getAssignedCloudCustomization().getName());

        builder.transactionId(transaction.getTransactionId())
                .licenseId(String.valueOf(transaction.getLicenseId()))
                .warehouse(getInMemoryStore().getWarehouseName())
                .testerName(getInMemoryStore().getTesterName());

        mapDeviceToCloudSyncRequest(builder, device);
        mapVendorToCloudSyncRequest(builder, transaction);
        if (device.getDeviceTestResult() != null) {
            mapTestResultToCloudDbSyncRequest(builder, device);
        }

        return builder.build();
    }

    /**
     * Create cloud sync db request from device and test results.
     * We currently don't have all the fields in the device objects, so they are set to ""
     * <p>
     * This structure may change soon after the cloud team has revised it.
     *
     * @param builder CloudDbSyncRequest.CloudDbSyncRequestBuilder
     * @param device  target syncable device
     */
    private void mapDeviceToCloudSyncRequest(final CloudDbSyncRequest.CloudDbSyncRequestBuilder builder,
                                             final Device device) {

        boolean isDeviceMicResultsAvailable = device.getDeviceTestResult() != null &&
                device.getDeviceTestResult().getMicrophoneResults() != null;

        String batteryChargeStart = StringUtils.EMPTY;
        String batteryChargeEnd = StringUtils.EMPTY;
        String totalDischarge = StringUtils.EMPTY;
        String batteryDrainType = StringUtils.EMPTY;

        if (device.getDeviceTestResult() != null && device.getDeviceTestResult().getBatteryResults() != null &&
                device.getDeviceTestResult().getBatteryResults().getBatteryDrain() != null) {
            if (StringUtils.isNotEmpty(device.getDeviceTestResult()
                    .getBatteryResults().getBatteryDrain().getStartBattery())) {
                batteryChargeStart = device.getDeviceTestResult().getBatteryResults().getBatteryDrain()
                        .getStartBattery();
            }
            if (StringUtils.isNotEmpty(device.getDeviceTestResult().
                    getBatteryResults().getBatteryDrain().getEndBattery())) {
                batteryChargeEnd = device.getDeviceTestResult().getBatteryResults().getBatteryDrain().getEndBattery();
            }
            if (StringUtils.isNotEmpty(device.getDeviceTestResult().
                    getBatteryResults().getBatteryDrain().getTotalDischarge())) {
                totalDischarge = device.getDeviceTestResult().getBatteryResults().getBatteryDrain()
                        .getTotalDischarge();
            }
            if (StringUtils.isNotEmpty(device.getDeviceTestResult().
                    getBatteryResults().getBatteryDrain().getBatteryDrainType())) {
                batteryDrainType = device.getDeviceTestResult().getBatteryResults().getBatteryDrain()
                        .getBatteryDrainType();
            }
        }

        String workingStatus;
        if (device.isManualEntry()) {
            workingStatus = device.getManualEntryWorkingStatus() != null ?
                    device.getManualEntryWorkingStatus() : StringUtils.EMPTY;
        } else {
            String functionalityStatus =
                    FunctionalityStatusUtil.getFunctionalityStatus(device, false,
                            getInMemoryStore().getAssignedCloudCustomization());
            if (FunctionalityStatusUtil.FULLY_FUNCTIONAL.equals(functionalityStatus)) {
                workingStatus = "Yes";
            } else if (FunctionalityStatusUtil.SEE_NOTES.equals(functionalityStatus)) {
                workingStatus = "No";
            } else if (FunctionalityStatusUtil.PENDING.equals(functionalityStatus)) {
                workingStatus = "Pending";
            } else {
                workingStatus = StringUtils.EMPTY;
            }
        }

        String manualEntryStatus = device.isManualEntry() ? "Yes" : "No";

        String simLockStatus = Boolean.TRUE.equals(device.getSimLock()) ? "Locked" :
                (Boolean.FALSE.equals(device.getSimLock()) ? "Unlocked" : StringUtils.EMPTY);

        String unlockStatus = StringUtils.isNotBlank(device.getUnlockStatus()) ?
                device.getUnlockStatus() : ("Locked".equalsIgnoreCase(simLockStatus) ?
                "LK" : "Unlocked".equalsIgnoreCase(simLockStatus) ? "UNLK" : StringUtils.EMPTY);

        if (device instanceof IosDevice iosDevice) {
            final MdmStatus deviceMdmStatus = iosDevice.getMdmInfo() == null ? null :
                    iosDevice.getMdmInfo().getMdmStatus();
            builder.modelNo(iosDevice.getModelNo() != null ? (iosDevice.getRegionInfo() != null ?
                            iosDevice.getModelNo() + iosDevice.getRegionInfo() :
                            iosDevice.getModelNo()) :
                            StringUtils.EMPTY)
                    .version(iosDevice.getProductVersion())
                    .regulatoryModelNo(iosDevice.getRegulatoryModelNumber())
                    .batterySerial(iosDevice.getOemPartsCurrent() == null
                            ? StringUtils.EMPTY : iosDevice.getOemPartsCurrent().getBatterySerial())
                    .sim1Mcc(iosDevice.getSimCarrierBundleInfo() == null ? StringUtils.EMPTY :
                            iosDevice.getSimCarrierBundleInfo().getMcc())
                    .sim1Mnc(iosDevice.getSimCarrierBundleInfo() == null ? StringUtils.EMPTY :
                            iosDevice.getSimCarrierBundleInfo().getMnc())
                    .network(iosDevice.getSimCarrierBundleInfo() == null ? StringUtils.EMPTY :
                            iosDevice.getSimCarrierBundleInfo().getProvider())
                    .appleId(iosDevice.getCurrentAppleId())
                    .mdm(deviceMdmStatus == null ? StringUtils.EMPTY : deviceMdmStatus.getKey())
                    .mdmResponse(iosDevice.getMdmRawResponse() == null ?
                            StringUtils.EMPTY : iosDevice.getMdmRawResponse())
                    .oemParts(iosDevice.getOemPartsJson())
                    .appVersion(getInMemoryStore().getIosAppVersion())
                    .fMic(isDeviceMicResultsAvailable ?
                            String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getFmAmplitude()) : "")
                    .bMic(isDeviceMicResultsAvailable ?
                            String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getBmAmplitude()) : "")
                    .vMic(isDeviceMicResultsAvailable ?
                            String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getRmAmplitude())
                            : StringUtils.EMPTY);
        } else if (device instanceof AndroidDevice androidDevice) {
            boolean isVideoMicResultAvailable = isDeviceMicResultsAvailable && StringUtils.isNotBlank(device.
                    getDeviceTestResult().getMicrophoneResults().getVideoMicrophone())
                    && !"null".equalsIgnoreCase(device.getDeviceTestResult().
                    getMicrophoneResults().getVideoMicrophone());
            boolean isMicrophoneResultAvailable = isDeviceMicResultsAvailable && StringUtils.isNotBlank(device.
                    getDeviceTestResult().getMicrophoneResults().getMicrophone())
                    && !"null".equalsIgnoreCase(device.getDeviceTestResult().
                    getMicrophoneResults().getVideoMicrophone());

            builder.modelNo(androidDevice.getModelNo() != null ? androidDevice.getModelNo() : StringUtils.EMPTY)
                    .version(String.valueOf(androidDevice.getOsMajorVersion() > 0 ?
                            androidDevice.getOsMajorVersion() : ""))
                    .appVersion(getInMemoryStore().getAndroidAppVersion())
                    .erasedSD(androidDevice.getEraseStartTime() != null
                            && androidDevice.getEraseStartTime() > 0 ? "Yes" : "No")
                    .bMic(isMicrophoneResultAvailable ?
                            "LS: " + device.getDeviceTestResult().getMicrophoneResults().getMicrophone() :
                            StringUtils.EMPTY)
                    .vMic(isVideoMicResultAvailable ?
                            "LS: " + device.getDeviceTestResult().getMicrophoneResults().getVideoMicrophone() :
                            StringUtils.EMPTY)
                    .network(androidDevice.getSimNetwork());
            builder.knox(androidDevice.getKnox() != null ? androidDevice.getKnox().getKey() : StringUtils.EMPTY);
            builder.mdm(androidDevice.getMdmStatus() != null ? androidDevice.getMdmStatus().getKey() :
                    StringUtils.EMPTY);
        }

        builder.model(device.getModel())
                .portNumber(device.getPortNumber() == null || device.getPortNumber() == -1 ? StringUtils.EMPTY :
                        Integer.valueOf(device.getPortNumber() + 1).toString())
                .imei(device.getImei())
                .imei2(device.getImei2())
                .ram(device.getRam())
                .eid(device.getEid())
                .shutdown(device.getFccid())
                .carrier(device.getCarrier())
                .deviceState(device.getDeviceState() != null ? device.getDeviceState().getText() :
                        DeviceState.HELLO.getText())
                .carrierLockResponse(device.getCarrierLockRawResponse() == null ? StringUtils.EMPTY :
                        device.getCarrierLockRawResponse())
                .serial(device.getSerial())
                .udid(device.getId())
                .os(device.getOperatingSystem())
                .licenseIdentifier(device.getLicenseIdentifier())
                .deviceLock(device.getDeviceLock() == null ? StringUtils.EMPTY : device.getDeviceLock().getKey())
                .rooted(device.getRooted() == null ? StringUtils.EMPTY : device.getRooted().getKey())
                .color(device.getColor())
                .colorCode(device.getColorCode())
                .memory(device.getDiskSize() == null ? StringUtils.EMPTY : device.getDiskSize().toString())
                .make(device.getMake())
                .simTechnology(device.getSimTechnology())
                .simLock(simLockStatus)
                .unlockStatus(unlockStatus)
                .simLockResponse(device.getSimLockRawResponse() == null ?
                        StringUtils.EMPTY : device.getSimLockRawResponse())
                .testPlanName(device.getTestPlan())
                .wifiMacAddress(device.getWifiAddress())
                .esn(device.getEsnStatus())
                .esnResponse(device.getEsnRawResponse())
                .meid(device.getMeid())
                .meid2(device.getMeid2() == null ? StringUtils.EMPTY : device.getMeid2())
                .decimalMeid(device.getMeidDecimal())
                .decimalMeid2(device.getMeidDecimal2())
                .pesn(device.getPesn())
                .pesn2(device.getPesn2())
                .simSerial(device.getSimSerial())
                .simSerial2(device.getSimSerial2())
                .countryOfRegion(device.getCountryOfOrigin())
                .eraseStartTime(getEraseStartTime(device))
                .eraseEndTime(getEraseEndTime(device))
                .erasedNotes(device.getErasedNotes())
                .eraseType(getEraseType(device))
                .restoreCode(device.getRestoreCode())
                .erased(getErased(device))
                .simErased(getSimErased(device))
                .eSimErased(getESimErased(device))
                .eSimPresent(getESimSupported(device) ? (device.isEsimActive() ? "Yes" : "No") : StringUtils.EMPTY)
                .grade(device.getGrade())
                .buildNo(getInMemoryStore().getBuildNo())
                .platformName(osChecker.getName())
                .firmware(device.getFirmware())
                .batteryChargeStart(batteryChargeStart)
                .batteryChargeEnd(batteryChargeEnd)
                .batteryDrain(totalDischarge)
                .batteryDrainType(batteryDrainType)
                .batteryHealthPercentage(getBatteryHealthValue(device))
                .batteryPercentage(String.valueOf(device.getBatteryPercentage()))
                .batteryCurrentMaxCapacity(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getCurrentCapacity()) : StringUtils.EMPTY)
                .batteryCocoCapacity(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getCocoCurrentCapacity()) : StringUtils.EMPTY)
                .batteryDesignMaxCapacity(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getDesignedCapacity()) : StringUtils.EMPTY)
                .batterySource((device.getBatteryInfo() != null &&
                        device.getBatteryInfo().getSource() != null) ?
                        device.getBatteryInfo().getSource().getKey() : StringUtils.EMPTY)
                .batteryTemperature(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getTemperature()) : StringUtils.EMPTY)
                .batteryCycle(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getCycle()) : StringUtils.EMPTY)
                .batteryModel(device.getBatteryInfo() != null ?
                        device.getBatteryInfo().getModel() : StringUtils.EMPTY)
                .batteryResistance(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getBatteryResistance()) : StringUtils.EMPTY)
                .oemBatteryHealth(device.getBatteryInfo() != null ?
                        String.valueOf(device.getBatteryInfo().getOemHealthPercentage()) : StringUtils.EMPTY)
                .working(workingStatus)
                .defectCodes(device.getDefectsCode())
                .lpn(device.getLpn())
                .customField1(device.getCustom1())
                .notes(device.getNotes())
                .manualEntry(manualEntryStatus)
                .skuCode(device.getSkuCode() != null ? device.getSkuCode() : "")
                .productCode(device.getProductCode())
                .dataVerification(device.getDataVerification())
                .iCloudInfo((device.getSourceApiResponse1() != null ? device.getSourceApiResponse1() + "\n" : "") +
                        (device.getSourceApiResponse2() != null ? device.getSourceApiResponse2() : ""))
                .ebayRefurbished(device.getEbayRefurbished())
                .ebayRejection(device.getEbayRejection())
                .swappaQualified(device.getSwappaQualified())
                .swappaRejection(device.getSwappaRejection())
                .amazonRenewed(device.getAmazonRenewed())
                .amazonRenewedRejection(device.getAmazonRenewedRejection())
                .backMarketQualified(device.getBackMarketQualified())
                .backMarketRejection(device.getBackMarketRejection())
                .ebayGradeId(device.getEbayGradeId())
                .AmazonGradeId(device.getAmazonGradeId())
                .BackMarketGradeId(device.getBackmarketGradeId())
                .SwappaGradeId(device.getSwappaGradeId());

        // TODO: There are more fields in CloudDbSyncRequest that needs to be synced when implemented
    }

    /**
     * Populate Transaction/Vendor data in cloudDbSync request
     *
     * @param builder     CloudDbSyncRequest.CloudDbSyncRequestBuilder
     * @param transaction object
     */
    private void mapVendorToCloudSyncRequest(final CloudDbSyncRequest.CloudDbSyncRequestBuilder builder,
                                             final Transaction transaction) {
        builder.transactionDate(transaction.getTransactionDate())
                .invoiceNo(transaction.getInvoiceNo())
                .vendorName(transaction.getVendorName())
                .qty(transaction.getQty())
                .boxNo(transaction.getBoxNo());
    }

    /**
     * Populate Test results data in cloudDbSync request
     * <p>
     * This structure may change soon after the cloud team has revised it.
     *
     * @param builder CloudDbSyncRequest.CloudDbSyncRequestBuilder
     * @param device  target device
     */
    private void mapTestResultToCloudDbSyncRequest(final CloudDbSyncRequest.CloudDbSyncRequestBuilder builder,
                                                   final Device device) {
        DeviceTestResult deviceTestResult = device.getDeviceTestResult();

        String batteryDrainDuration = StringUtils.EMPTY;
        String batteryChargeStart = StringUtils.EMPTY;
        String batteryChargeEnd = StringUtils.EMPTY;
        String batteryDrainType = StringUtils.EMPTY;

        if (deviceTestResult.getBatteryResults() != null &&
                deviceTestResult.getBatteryResults().getBatteryDrain() != null) {
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration())) {
                batteryDrainDuration = deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery())) {
                batteryChargeStart = deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery())) {
                batteryChargeEnd = deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType())) {
                batteryDrainType = deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType();
            }
        }

        // Triple <cosmetic Result, Failed cosmetic result, Passed cosmetic result>
        Triple<String, String, String> cosmeticFailedPassedResult =
                getAllCosmeticTestResults(deviceTestResult, getInMemoryStore().getAssignedCloudCustomization());
        String cosmeticResults = null;
        String failedCosmeticTestResults = null;
        String passedCosmeticTestResults = null;
        if (cosmeticFailedPassedResult != null) {
            cosmeticResults = cosmeticFailedPassedResult.getLeft();
            failedCosmeticTestResults = cosmeticFailedPassedResult.getMiddle();
            passedCosmeticTestResults = cosmeticFailedPassedResult.getRight();
        }
        builder.failed(StringUtils.defaultString(getAllFailedTestResults(deviceTestResult, failedCosmeticTestResults),
                        StringUtils.EMPTY))
                .passed(StringUtils.defaultString(getAllPassedTestResults(deviceTestResult, passedCosmeticTestResults),
                        StringUtils.EMPTY))
                .pending(deviceTestResult.getTestResults() == null
                        || deviceTestResult.getTestResults().getPending() == null ? StringUtils.EMPTY :
                        TestResultsUtil.listToCommaSeparatedString(deviceTestResult.getTestResults().getPending()))
                .cosmetics(StringUtils.defaultString(cosmeticResults,
                        StringUtils.EMPTY));

        builder.grade(device.getGrade())
                .batteryDrainDuration(batteryDrainDuration)
                .batteryChargeStart(batteryChargeStart)
                .batteryChargeEnd(batteryChargeEnd)
                .batteryDrainType(batteryDrainType)
                .testingCompleted(deviceTestResult.getTestResults() == null
                        || deviceTestResult.getTestResults().getTestingCompleted() == null ? StringUtils.EMPTY :
                        deviceTestResult.getTestResults().getTestingCompleted().toString());
    }

    // ==================================================================
    //          Methods related to UpdateByColumn request building
    // ==================================================================

    /**
     * Build the UpdateDeviceDisconnectRequest
     *
     * @param device Device
     * @return UpdateDeviceDisconnectRequest
     */
    private UpdateDeviceDisconnectRequest buildUpdateDeviceDisconnectRequest(final Device device) {
        UpdateDeviceDisconnectRequest.UpdateDeviceDisconnectRequestBuilder builder =
                UpdateDeviceDisconnectRequest.builder();

        builder.transactionId(getInMemoryStore().getTransaction().getTransactionId())
                .licenseId(String.valueOf(getInMemoryStore().getTransaction().getLicenseId()))
                .serial(device.getSerial())
                .deviceDisconnect(DateFormatUtil.millisToUTCDateTime(System.currentTimeMillis(), DATE_TIME_FORMAT));

        return builder.build();
    }

    /**
     * Build the UpdateByColumnRequest
     *
     * @param device Device
     * @return UpdateByColumnRequest
     */
    private UpdateByColumnRequest buildUpdateByColumnRequest(final Device device) {
        UpdateByColumnRequest.UpdateByColumnRequestBuilder builder = UpdateByColumnRequest.builder();

        builder.transactionId(getInMemoryStore().getTransaction().getTransactionId())
                .licenseId(String.valueOf(getInMemoryStore().getTransaction().getLicenseId()));

        mapDeviceToUpdateByColumnRequest(builder, device);
        if (device.getDeviceTestResult() != null) {
            mapTestResultToUpdateByColumnRequest(builder, device);
        }

        return builder.build();
    }

    /**
     * Create UpdateByColumn Request from IPhoneDevice
     *
     * @param builder UpdateByColumnRequest.UpdateByColumnRequestBuilder
     * @param device  Device to be sync with cloud
     */
    private void mapDeviceToUpdateByColumnRequest(final UpdateByColumnRequest.UpdateByColumnRequestBuilder builder,
                                                  final Device device) {
        final String eraseNotes = device.getErasedNotes();
        String workingStatus;
        String functionalityStatus =
                FunctionalityStatusUtil.getFunctionalityStatus(device, false,
                        getInMemoryStore().getAssignedCloudCustomization());
        if (FunctionalityStatusUtil.FULLY_FUNCTIONAL.equals(functionalityStatus)) {
            workingStatus = "Yes";
        } else if (FunctionalityStatusUtil.SEE_NOTES.equals(functionalityStatus)) {
            workingStatus = "No";
        } else if (FunctionalityStatusUtil.PENDING.equals(functionalityStatus)) {
            workingStatus = "Pending";
        } else {
            workingStatus = StringUtils.EMPTY;
        }

        if (device instanceof IosDevice iosDevice) {
            builder.version(iosDevice.getProductVersion());
        } else {
            AndroidDevice androidDevice = (AndroidDevice) device;
            builder.version(String.valueOf(androidDevice.getOsMajorVersion() > 0 ?
                            androidDevice.getOsMajorVersion() : ""))
                    .knox(androidDevice.getKnox() != null ? androidDevice.getKnox().getKey() : "")
                    .erasedSD(androidDevice.getEraseStartTime() != null
                            && androidDevice.getEraseStartTime() > 0 ? "Yes" : "No");
        }

        builder.buildNo(getInMemoryStore().getBuildNo())
                .carrier(device.getCarrier())
                .grade(device.getGrade())
                .color(device.getColor())
                .deviceState(device.getDeviceState() != null ? device.getDeviceState().getText() :
                        DeviceState.HELLO.getText())
                .eraseStartTime(getEraseStartTime(device))
                .eraseEndTime(getEraseEndTime(device))
                .erasedNotes(eraseNotes)
                .restoreCode(device.getRestoreCode())
                .shutdown(device.getFccid())
                .eid(device.getEid())
                .erased(getErased(device))
                .simErased(getSimErased(device))
                .eSimErased(getESimErased(device))
                .eSimPresent(getESimSupported(device) ? (device.isEsimActive() ? "Yes" : "No") : "N/A")
                .imei(device.getImei())
                .imei2(device.getImei2())
                .meid(device.getMeid())
                .meid2(device.getMeid2() == null ? StringUtils.EMPTY : device.getMeid2())
                .decimalMeid(device.getMeidDecimal())
                .decimalMeid2(device.getMeidDecimal2())
                .pesn(device.getPesn())
                .pesn2(device.getPesn2())
                .countryOfRegion(device.getCountryOfOrigin())
                .firmware(device.getFirmware())
                .simSerial(device.getSimSerial())
                .lpn(device.getLpn())
                .customField1(device.getCustom1())
                .simSerial2(device.getSimSerial2())
                .wifiMacAddress(device.getWifiAddress())
                .licenseIdentifier(device.getLicenseIdentifier())
                .portNumber(device.getPortNumber() == null || device.getPortNumber() == -1 ? StringUtils.EMPTY :
                        Integer.valueOf(device.getPortNumber() + 1).toString())
                .serial(device.getSerial())
                .eraseType(getEraseType(device))
                .working(workingStatus)
                .productCode(device.getProductCode())
                .skuCode(device.getSkuCode())
                .notes(device.getNotes())
                .dataVerification(device.getDataVerification())
                .ebayRefurbished(device.getEbayRefurbished())
                .ebayRejection(device.getEbayRejection())
                .swappaQualified(device.getSwappaQualified())
                .swappaRejection(device.getSwappaRejection())
                .amazonRenewed(device.getAmazonRenewed())
                .amazonRenewedRejection(device.getAmazonRenewedRejection())
                .backMarketQualified(device.getBackMarketQualified())
                .backMarketRejection(device.getBackMarketRejection());
//                .ebayGradeId(device.getEbayGradeId())
//                .AmazonGradeId(device.getAmazonGradeId())
//                .BackMarketGradeId(device.getBackmarketGradeId())
//                .SwappaGradeId(device.getSwappaGradeId());
        // TODO: There are more fields in UpdateByColumnRequest that needs to be synced when implemented
    }

    /**
     * Populate test results data in updateByColumn request
     * <p>
     * This structure may change soon after the cloud team has revised it.
     *
     * @param builder UpdateByColumnRequest.UpdateByColumnRequestBuilder
     * @param device  target device
     */
    private void mapTestResultToUpdateByColumnRequest(final UpdateByColumnRequest.UpdateByColumnRequestBuilder builder,
                                                      final Device device) {

        DeviceTestResult deviceTestResult = device.getDeviceTestResult();
        String batteryDrainDuration = StringUtils.EMPTY;
        String batteryChargeStart = StringUtils.EMPTY;
        String batteryChargeEnd = StringUtils.EMPTY;
        String batteryDrainType = StringUtils.EMPTY;

        if (deviceTestResult.getBatteryResults() != null &&
                deviceTestResult.getBatteryResults().getBatteryDrain() != null) {
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration())) {
                batteryDrainDuration = deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery())) {
                batteryChargeStart = deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery())) {
                batteryChargeEnd = deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType())) {
                batteryDrainType = deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType();
            }
        }

        // Triple <cosmetic Result, Failed cosmetic result, Passed cosmetic result>
        Triple<String, String, String> cosmeticFailedPassedResult =
                getAllCosmeticTestResults(deviceTestResult, getInMemoryStore().getAssignedCloudCustomization());
        String failedCosmeticTestResults = null;
        String passedCosmeticTestResults = null;
        String cosmeticResults = null;
        if (cosmeticFailedPassedResult != null) {
            cosmeticResults = cosmeticFailedPassedResult.getLeft();
            failedCosmeticTestResults = cosmeticFailedPassedResult.getMiddle();
            passedCosmeticTestResults = cosmeticFailedPassedResult.getRight();
        }

        builder.failed(getAllFailedTestResults(deviceTestResult, failedCosmeticTestResults))
                .passed(getAllPassedTestResults(deviceTestResult, passedCosmeticTestResults))
                .cosmetics(cosmeticResults);

        builder.grade(device.getGrade())
                .batteryDrainDuration(batteryDrainDuration)
                .batteryChargeStart(batteryChargeStart)
                .batteryChargeEnd(batteryChargeEnd)
                .batteryDrainType(batteryDrainType)
                .pending(deviceTestResult.getTestResults() == null
                        || deviceTestResult.getTestResults().getPending() == null ? StringUtils.EMPTY :
                        TestResultsUtil.listToCommaSeparatedString(deviceTestResult.getTestResults().getPending()));
    }


    private String getEraseType(final Device device) {
        final String androidEraseType = DeviceEraseType.ANDROID_ERASE_TYPE.getValue();
        final String iosEraseType = DeviceEraseType.IOS_ERASE_TYPE.getValue();
        final String iosRestoreType = DeviceEraseType.IOS_RESTORE_TYPE.getValue();

        String eraseType = StringUtils.EMPTY;
        if (device instanceof AndroidDevice) {
            if (device.getEraseStartTime() != null) {
                eraseType = androidEraseType;
            }
        } else {
            IosDevice iosDevice = (IosDevice) device;
            if (device.getEraseStartTime() != null) {
                if (StringUtils.isNotBlank(iosDevice.getEraserType())) {
                    eraseType = iosDevice.getEraserType();
                } else {
                    eraseType = iosEraseType;
                }
            } else if (device.getRestoreStartTime() != null) {
                if (StringUtils.isNotBlank(iosDevice.getRestoreType())) {
                    eraseType = iosDevice.getRestoreType();
                } else {
                    eraseType = iosRestoreType;
                }
            }
        }
        return eraseType;
    }

    private String getEraseStartTime(final Device device) {
        String eraseStartTime;
        if (device.getEraseStartTime() != null) {
            eraseStartTime = DateFormatUtil.millisToUTCDateTime(device.getEraseStartTime(), DATE_TIME_FORMAT);
        } else if (device.getRestoreStartTime() != null) {
            eraseStartTime = DateFormatUtil.millisToUTCDateTime(device.getRestoreStartTime(), DATE_TIME_FORMAT);
        } else {
            eraseStartTime = " ";
        }
        return eraseStartTime;
    }

    private String getEraseEndTime(final Device device) {
        String eraseEndTime;
        if (device.getEraseEndTime() != null) {
            eraseEndTime = DateFormatUtil.millisToUTCDateTime(device.getEraseEndTime(), DATE_TIME_FORMAT);
        } else if (device.getRestoreEndTime() != null) {
            eraseEndTime = DateFormatUtil.millisToUTCDateTime(device.getRestoreEndTime(), DATE_TIME_FORMAT);
        } else {
            eraseEndTime = " ";
        }
        return eraseEndTime;
    }

    private String getErased(final Device device) {
        String erased = "No";
        if ((device.getEraseStartTime() != null && device.getEraseStartTime() > 0
                && device.getEraseEndTime() != null && device.getEraseEndTime() > 0) ||
                (device.getRestoreStartTime() != null && device.getRestoreStartTime() > 0
                        && device.getRestoreEndTime() != null && device.getRestoreEndTime() > 0)) {
            erased = "Yes";
        }
        return erased;
    }

    private String getSimErased(final Device device) {
        // For iWatch devices check if it is iWatch mode and device support sim
        boolean isInIWatchMode = inMemoryStore.getDeviceConnectionMode() == DeviceConnectionMode.IWATCH_HOST;
        boolean isIWatchCellular = StringUtils.isNotBlank(device.getImei())
                && !StringUtils.equalsIgnoreCase(device.getImei(), "N/A");
        if (isInIWatchMode && !isIWatchCellular) {
            return "N/A";
        }
        String simErased = "No";
        if (device.getEraseStartTime() == null && device.getRestoreStartTime() == null) {
            simErased = StringUtils.EMPTY;
        } else if ((device.getEraseStartTime() != null && device.getEraseStartTime() > 0) ||
                (device.getRestoreStartTime() != null && device.getRestoreStartTime() > 0)) {
            simErased = "Yes";
        }
        return simErased;
    }

    private void prependInitialDefectsToTestResults(final Device device) {
        TestResultsUtil.prependInitialDefectsToAppResults(device,
                getInMemoryStore().getAssignedCloudCustomization(), localizationService);
        LOGGER.info("Device initially defect test results have been prepared");
    }

    /**
     * This method will add the device to the set for the cloud DB sync
     * As of now this method is being called from PairSuccessListner(for both android/IOS)
     *
     * @param device : Device to be sync
     */
    public void addDeviceToDataSyncSet(final Device device) {
        if (device.getStage() != null || (
                device instanceof AndroidDevice && ((AndroidDevice) device)
                        .getAndroidConnectionMode().equals(AndroidConnectionMode.AT))) {
            boolean addStatus = deviceSetToSync.add(device);
            LOGGER.info("Adding device with id :{} to the sync set. Add status: {}", device.getId(), addStatus);
        }
    }

    /**
     * This Scheduler runs every 20 seconds and sync all devices in the set to
     * the cloud DB
     */
    @Scheduled(fixedDelay = 20000, initialDelay = 60000)
    private void cloudDBSyncScheduler() {
        LOGGER.info("Scheduling device sync start");
        synchronized (deviceSetToSync) {
            List<Device> devicesToRemove = new ArrayList<>();
            for (Device device : deviceSetToSync) {
                final Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());

                if (deviceInTracker == null) {
                    // If for some reason the device was removed abruptly, and it got removed from the tracker,
                    // then we need to remove the device from deviceSetToSync set as well
                    // The cloudDB would have been force updated with latest device object from DisconnectedListener
                    devicesToRemove.add(device);
                } else {
                    if (DISCONNECTED.equals(deviceInTracker.getStage())) {
                        devicesToRemove.add(device);
                    }
                    syncDevicesRecordsOnCloud(deviceInTracker, getInMemoryStore().getTransaction());
                }
            }

            List<Device> devicesToAdd = new ArrayList<>();
            // once sync has happened, and we know a device was disconnected, remove from the set to sync
            devicesToRemove.forEach(device -> {
                // remove a disconnected device, but swap it with the new device in tracker object if it got reconnected
                boolean removed = deviceSetToSync.remove(device);
                LOGGER.info("Removed device with id: {} from the sync set. Remove status = {}",
                        device.getId(), removed);
                if (deviceConnectionTracker.getDevice(device.getId()) != null) {
                    LOGGER.info("Device: {} got reconnected, so swapping the device in the sync set.", device.getId());
                    devicesToAdd.add(deviceConnectionTracker.getDevice(device.getId()));
                }
            });
            deviceSetToSync.addAll(devicesToAdd);
        }
        LOGGER.info("Scheduling device sync end");
    }

    /**
     * This method will directly sync the device to the cloud.
     *
     * @param device      Device to synced
     * @param transaction Transaction details of the device to be synced
     */
    public void syncDeviceRecordToCloud(final Device device, final Transaction transaction) {
        LOGGER.info("Request to sync device to the cloud is received. Device:{}", device);
        syncDevicesRecordsOnCloud(device, transaction);
    }

    /**
     * This method is to be called when a cloud transaction is edited and needs to be synced
     *
     * @param device      device to sync
     * @param transaction transaction info
     */
    public void updateTransactionOnCloud(final Device device, final Transaction transaction) {
        LOGGER.info("Request to edit device info in the cloud is received. Device:{}", device);
        editTransactionInCloud(device, transaction);
    }

    /**
     * Call the cloud api to update edit transaction specific columns
     *
     * @param device
     * @param transaction
     */
    private void editTransactionInCloud(final Device device, final Transaction transaction) {
        // Thread to sync pending device records to cloud via CloudDbSync or UpdateByColumn
        MDC.put("id", device.getId());
        try {
            String response;
            final DeviceStage stage = device.getStage() != null ?
                    device.getStage() : DISCONNECTED;
            device.setStage(stage);

            EditTransactionSyncRequest editTransactionSyncRequest = buildEditRequest(device, transaction);
            LOGGER.info("EditTransaction API request: {}", editTransactionSyncRequest);
            response = cloudApiRestClient.editTransactionInCloudDb(editTransactionSyncRequest);
            LOGGER.info("EditTransaction API response: {}", response);

        } catch (Exception e) {
            LOGGER.error("Exception occurred while synchronizing devices data to cloud", e);
        }
        MDC.clear();
    }

    /**
     * Build the EditTransactionSyncRequest
     *
     * @param device      Device
     * @param transaction Transaction info
     * @return EditTransactionSyncRequest
     */
    private EditTransactionSyncRequest buildEditRequest(final Device device, final Transaction transaction) {
        EditTransactionSyncRequest.EditTransactionSyncRequestBuilder builder = EditTransactionSyncRequest.builder();

        builder.transactionId(transaction.getTransactionId())
                .licenseId(String.valueOf(transaction.getLicenseId()));

        mapDeviceToEditTransactionRequest(builder, device);

        return builder.build();
    }

    /**
     * Map editable column info to the sync request
     *
     * @param builder EditTransactionSyncRequestBuilder
     * @param device  device
     */
    private void mapDeviceToEditTransactionRequest(
            final EditTransactionSyncRequest.EditTransactionSyncRequestBuilder builder, final Device device) {

        builder.carrier(device.getCarrier())
                .color(device.getColor())
                .grade(device.getGrade())
                .serial(device.getSerial())
                .unlockStatus(device.getUnlockStatus())
                .customField1(device.getCustom1())
                .lpn(device.getLpn())
                .notes(device.getNotes())
                .udid(device.getId());
    }

    /**
     * This method will make cloud DB call for devices post restore
     * For recovery this would be first cloud call so we use CloudDBSync API and for
     * other cases we make call to updateByColumn API
     *
     * @param device      Device to synced
     * @param transaction Current transaction
     */
    public void syncDeviceRecordPostRestore(final IosDevice device, final Transaction transaction) {
        try {
            MDC.clear();
            MDC.put("id", device.getId());
            LOGGER.info("Request to sync device post restore to the cloud is received. Device state: {}", device);

            String response;

            if (device.isConnectedInRecovery()) {

                CloudDbSyncRequest cloudDbSyncRequest =
                        buildCloudSyncRequest(transaction, device);
                LOGGER.info("CloudDbSyncRequest API request post restore: {}", cloudDbSyncRequest);
                response = cloudApiRestClient.syncDataToCloudDb(cloudDbSyncRequest);
                LOGGER.info("CloudDBSync API received response post restore: {}", response);

            } else {

                UpdateByColumnRequest updateByColumnRequest = buildUpdateByColumnRequest(device);
                LOGGER.info("updateByColumnRequest API request post restore: {}",
                        updateByColumnRequest);
                response = cloudApiRestClient.syncColumnInfoToCloudDb(updateByColumnRequest);
                LOGGER.info("UpdateByColumn API post restore response: {}", response);

                if (!StringUtils.containsIgnoreCase(response, SUCCESS)) {
                    CloudDbSyncRequest cloudDbSyncRequest = buildCloudSyncRequest(transaction, device);
                    response = cloudApiRestClient.syncDataToCloudDb(cloudDbSyncRequest);
                    LOGGER.info("Fallback to CloudDBSync API post restores received response: {}", response);
                }
            }
        } catch (Exception ee) {
            LOGGER.error("Error while calling cloud DB post restore", ee);
        }
    }

    /**
     * Converts an IosDevice object into a Device object,
     * using information from the associated IWatchInfo.
     *
     * @param iosDevice the iOS device containing iWatch information.
     * @return a Device object populated with details from the given IosDevice.
     */
    public Device getDeviceFromIWatchForSyncing(final IosDevice iosDevice) {
        final IosDevice device = new IosDevice();
        final IWatchInfo iWatchInfo = iosDevice.getIWatchInfo() != null ? iosDevice.getIWatchInfo() : null;
        if (iWatchInfo != null) {
            device.setId(iWatchInfo.getSerial());
            device.setSerial(iWatchInfo.getSerial());
            if (iWatchInfo.getImei() != null) {
                device.setImei(iWatchInfo.getImei());
            } else {
                device.setImei("N/A");
            }
            device.setProductCode(iWatchInfo.getProductType());
            device.setMake(iWatchInfo.getName());
            device.setModelNo(iWatchInfo.getModelNumber());
            String title = IWatchFieldsMapperUtil.getWatchTitle(iWatchInfo.getProductType(), iWatchInfo.getImei());
            device.setModel(title);
            device.setOperatingSystem(iWatchInfo.getProductVersion());
            device.setRegionInfo(iWatchInfo.getRegionInfo());
            device.setColorCode(iWatchInfo.getColorCode());
            device.setColor(iWatchInfo.getColor());
            device.setAppInstalled(iosDevice.isAppInstalled());
            if (iosDevice.getBatteryInfo() != null) {
                device.setBatteryInfo(iosDevice.getBatteryInfo());
            } else {
                BatteryInfo batteryInfo = BatteryInfo.builder()
                        .batteryPercentage(iWatchInfo.getBatteryCapacity() != null ?
                                Integer.parseInt(iWatchInfo.getBatteryCapacity()) : 0)
                        .isCharging(iWatchInfo.getChargingStatus() != null &&
                                iWatchInfo.getChargingStatus().equals("true"))
                        .build();
                device.setBatteryInfo(batteryInfo);
            }
            device.setBatteryPercentage(iWatchInfo.getBatteryCapacity() != null ?
                    Integer.parseInt(iWatchInfo.getBatteryCapacity()) : 0);
            device.setPortNumber(iosDevice.getPortNumber());
            device.setIsErasePerformed(iosDevice.getIsErasePerformed());
            device.setESimErased(iosDevice.getIsErasePerformed() != null && iosDevice.getIsErasePerformed()
                    && iWatchInfo.getImei() != null);
            device.setDeviceState(DeviceState.HOME);
            device.setSkuCode(iosDevice.getSkuCode());
            device.setDeviceTestResult(iosDevice.getDeviceTestResult());
            if (iosDevice.getDeviceTestResult() != null) {
                device.setGrade(iosDevice.getDeviceTestResult().getGradeResults());
            }
            return device;
        }

        return null;
    }

    /**
     * Method to check if the device is added to the sync set or not.
     *
     * @param device
     * @return true - if device is present in the sync set.
     * false, otherwise.
     */
    public boolean isDeviceInDataSyncSet(final Device device) {
        return deviceSetToSync.contains(device);
    }

}
