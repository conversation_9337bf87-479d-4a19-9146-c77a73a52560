package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.cloudapi.AndroidNetworkUnlockCodesResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class GetAndroidNetworkCodesService {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetAndroidNetworkCodesService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    public AndroidNetworkUnlockCodesResponse getAndroidNetworkUnlockCodes(final String userToken) {
        return phonecheckApiRestClient.getAndroidUnlockCodesResponse(userToken);
    }
}