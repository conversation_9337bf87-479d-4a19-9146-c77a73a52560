package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.IosModelResponse;
import com.phonecheck.model.service.IosModelDb;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudIosModelService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudIosModelService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get Ios Models for the given version
     *
     * @param version IOS model version
     * @return IosModel
     */
    public IosModelDb getIosModels(final String version) {
        try {
            IosModelResponse response = cloudApiRestClient.getLatestIosModels(version);
            return IosModelDb.builder()
                    .iosModels(response.getIosModels())
                    .build();
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get IOS models", e);
            return null;
        }
    }
}
