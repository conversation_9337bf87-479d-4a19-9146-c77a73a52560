package com.phonecheck.api.cloud;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.api.client.TwoWayApiIntegrationClient;
import com.phonecheck.model.twowayapi.LabelApiRequestModel;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * This class handles all the external call for 2-way API
 * */
@Service
@AllArgsConstructor
public class TwoWayApiCloudCallService {
    private static final Logger LOGGER = LoggerFactory.getLogger(TwoWayApiCloudCallService.class);
    private final TwoWayApiIntegrationClient twoWayApiIntegrationClient;
    private final ObjectMapper objectMapper;

    /**
     * Get the Source Api Integration Response
     *
     * @param sourceRequestObject Request object
     * @return String
     */
    public ObjectNode getSourceApiIntegrationResponse(final ObjectNode sourceRequestObject) {
        return twoWayApiIntegrationClient.getSourceApiData(sourceRequestObject);
    }

    /**
     * Get the Results Api Integration Response
     *
     * @param resultsRequestObject Request object
     * @return String
     */
    public ObjectNode getResultsApiIntegrationResponse(final ObjectNode resultsRequestObject) {
        ObjectNode resultsApiResponse = null;
        try {
            resultsApiResponse = twoWayApiIntegrationClient.getResultsApiData(resultsRequestObject);
        } catch (Exception e) {
            LOGGER.error("Couldn't get Results api data", e);
        }
        return resultsApiResponse;
    }

    /**
     * Get the Label Api Integration Response
     *
     * @param labelRequestObject Request object
     * @return String
     */
    public ObjectNode getLabelApiIntegrationResponse(final LabelApiRequestModel labelRequestObject) {
        ObjectNode labelApiDataResponse = null;
        try {
            labelApiDataResponse = twoWayApiIntegrationClient.getLabelApiData(labelRequestObject);
        } catch (Exception e) {
            LOGGER.error("Couldn't get Label api data", e);
        }
        return labelApiDataResponse;
    }

    public ObjectNode getLabelApiIntegrationResponse(final ObjectNode labelRequestObject) {
        ObjectNode labelApiDataResponse = null;
        try {
            labelApiDataResponse = twoWayApiIntegrationClient.getLabelApiData(labelRequestObject);
        } catch (Exception e) {
            LOGGER.error("Couldn't get Label api data", e);
        }
        return labelApiDataResponse;
    }

    /**
     * This method create a NodeObject with all nested values on root
     * @param root Source object
     * @return ObjectNode
     */
    public ObjectNode flattenObjectNode(final ObjectNode root) {
        ObjectNode result = objectMapper.createObjectNode();
        root.fields().forEachRemaining(entry -> {
            String key = entry.getKey();
            JsonNode value = entry.getValue();
            if (value instanceof ObjectNode) {
                ObjectNode flattened = flattenObjectNode((ObjectNode) value);
                flattened.fields().forEachRemaining(nestedEntry ->
                        result.set(nestedEntry.getKey(), nestedEntry.getValue()));
            } else {
                result.set(key, value);
            }
        });
        return result;
    }
}