package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CloudTransactionResponse;
import com.phonecheck.model.cloudapi.TransactionDevicesRequest;
import com.phonecheck.model.cloudapi.TransactionResponse;
import com.phonecheck.model.transaction.Transaction;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudTransactionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudTransactionService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Creates transaction from cloud api
     *
     * @param transaction object
     * @return Transaction
     */
    public Transaction createTransactionFromCloud(final Transaction transaction) {
        try {
            CloudTransactionResponse response = cloudApiRestClient.createTransactionFromCloud(transaction);
            LOGGER.info("New transaction cloud response: {}", response);
            return Transaction.builder().
                    transactionId(Integer.parseInt(response.getTransactionId())).
                    vendorName(response.getVendorName()).
                    invoiceNo(response.getInvoiceNo()).
                    boxNo(response.getBoxNo()).
                    qty(response.getQty()).
                    transactionDate(response.getTransactionsDate()).
                    licenseId(Integer.parseInt(response.getLicenseID())).
                    stationId(response.getStationID()).
                    isCreatedByCloud(response.getCreatedByCloud().equals("0")).build();
        } catch (final RestClientException e) {
            LOGGER.error("Error while creating transaction in cloud", e);
            return null;
        }
    }

    /**
     * Retrieves previous transaction device records from cloud
     *
     * @param transactionDevicesRequest request object to retrieve devices data by transaction
     * @return Transaction
     */
    public TransactionResponse getTransactionDevices(
            final TransactionDevicesRequest transactionDevicesRequest
    ) {
        try {
            return cloudApiRestClient.getTransactionDevices(
                    transactionDevicesRequest
            );
        } catch (final RestClientException e) {
            LOGGER.error("Error loading previous transaction data from cloud", e);
            return null;
        }
    }

    /**
     * Retrieves cloud lookup device records from cloud
     *
     * @param licenseId  license id
     * @param lookupType look up type
     * @param value      value
     * @return response
     */
    public TransactionResponse getCloudDeviceLookup(
            final String licenseId, final String lookupType, final String value) {
        try {
            return cloudApiRestClient.getCloudDeviceLookup(licenseId, lookupType, value);
        } catch (final RestClientException e) {
            LOGGER.error("Error loading device lookup data from cloud", e);
            return null;
        }
    }


    /**
     * Retrieves cloud lookup device records from cloud
     *
     * @param licenseId  license id
     * @param serial device serial
     * @param oldTransactionId old transaction id of the record
     * @param transaction selected transaction
     * @return String represents the cloud response
     */
    public String changeTransactionForTheRecords(final int licenseId,
                                               final String serial,
                                               final String oldTransactionId,
                                               final Transaction transaction) {
        try {
            final String response = cloudApiRestClient.updateTransactionDetails(licenseId,
                    serial,
                    oldTransactionId,
                    transaction);
            LOGGER.info("Transaction updated for serial: {} from: {} to transaction: {} successfully with response :{}",
                    serial, oldTransactionId, transaction.getTransactionId(), response);
            return response;
        } catch (final RestClientException e) {
            LOGGER.error("Error updating transaction in the cloud", e);
            return null;
        }

    }
}
