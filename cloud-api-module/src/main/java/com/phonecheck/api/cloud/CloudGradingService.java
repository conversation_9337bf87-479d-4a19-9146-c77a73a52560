package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.CloudShopfloorApiRestClient;
import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.grading.GradingAnswers;
import com.phonecheck.model.grading.VendorGradingCriteria;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

/**
 * Service class that interacts with the grading API in the cloud.
 * This class handles operations such as fetching active grading methods,
 * retrieving final grades, setting grading system answers, and fetching grading responses.
 */
@Service
@AllArgsConstructor
public class CloudGradingService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudGradingService.class);
    private final CloudShopfloorApiRestClient cloudShopfloorApiRestClient;
    private final CloudApiRestClient cloudApiRestClient;
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Retrieves the active grading method for the given profile ID.
     *
     * @param profileId the ID of the profile
     * @param masterId  master id
     * @return the active grading method or null if an error occurs
     */
    public String getActiveGradingMethod(final String profileId, final String masterId) {
        try {
            return cloudShopfloorApiRestClient.getActiveGradingResponse(profileId, masterId);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get active grading", e);
            return null;
        }
    }

    /**
     * Retrieves the final grade using the specified method name, grade hierarchy, and sub-grades.
     *
     * @param methodName     the name of the grading method
     * @param gradeHierarchy the hierarchy of grades
     * @param subGrades      the sub-grades
     * @return the final grade or null if an error occurs
     */
    public String getFinalGrade(final String methodName,
                                final String gradeHierarchy,
                                final String subGrades) {
        try {
            return cloudShopfloorApiRestClient.getFinalGrade(methodName, gradeHierarchy, subGrades);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get final grading", e);
            return null;
        }
    }

    /**
     * Sets the grading system answers based on the provided grading answers object.
     *
     * @param gradingAnswers the grading answers to set
     */
    public void sendGradingAnswersToCloud(final GradingAnswers gradingAnswers) {
        try {
            cloudShopfloorApiRestClient.sendGradingAnswers(gradingAnswers);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling grading system answers", e);
        }
    }

    /**
     * Retrieves the grading response for the given profile ID and locale.
     *
     * @param profileId the ID of the profile
     * @param locale    the locale for the response
     * @param masterId  Master id
     * @return String response
     */
    public String getGradingResponse(final String profileId, final String locale,
                                     final String masterId) {
        try {
            return cloudShopfloorApiRestClient.getGradingSystemApiResponse(profileId, locale, masterId);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get grading response", e);
            return null;
        }
    }

    /**
     * Retrieves the gradeCriteria response for the given masterToken.
     *
     * @param masterToken The authentication token of the master
     * @return String response
     */
    public VendorGradingCriteria getGradeCriteriaResponse(final String masterToken) {
        try {
            return phonecheckApiRestClient.getMarketplaceGradingCriteria(masterToken);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling grading system criteria", e);
        }
        return null;
    }
}