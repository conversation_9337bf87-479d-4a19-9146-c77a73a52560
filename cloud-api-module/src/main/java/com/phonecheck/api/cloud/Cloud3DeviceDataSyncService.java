package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.cloudapi.Cloud3DbSyncRequest;
import com.phonecheck.model.cloudapi.Cloud3DbSyncResponse;
import com.phonecheck.model.constants.DeviceEraseType;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.SyncableDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.status.RootedStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.DeviceTestResult;
import com.phonecheck.model.transaction.Transaction;
import com.phonecheck.model.util.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import static com.phonecheck.model.device.stage.DeviceStage.*;
import static com.phonecheck.model.util.DateFormatUtil.DATE_TIME_FORMAT;
import static com.phonecheck.model.util.DeviceCosmeticResultsUtil.getAllCosmeticTestResults;

@Service
public class Cloud3DeviceDataSyncService extends AbstractCloudDeviceDataSyncService {
    private static final Logger LOGGER = LoggerFactory.getLogger(Cloud3DeviceDataSyncService.class);

    private static final List<DeviceStage> STAGES_TO_CALL_CLOUD_3_DB_SYNC = List.of(
            READY,
            APP_TESTING_DONE,
            ERASE_SUCCESS,
            DISCONNECTED
    );

    private final PhonecheckApiRestClient phonecheckApiRestClient;
    private final BlockingQueue<SyncableDevice> devicesDataSyncRequestsQueue;
    private final OsChecker osChecker;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final LocalizationService localizationService;
    private final String applicationVersion;
    private final boolean keepSynchronizingData = true;

    public Cloud3DeviceDataSyncService(final PhonecheckApiRestClient phonecheckApiRestClient,
                                       final InMemoryStore inMemoryStore,
                                       final OsChecker osChecker,
                                       final DeviceConnectionTracker deviceConnectionTracker,
                                       @Qualifier("applicationVersion") final String applicationVersion,
                                       final LocalizationService localizationService) {
        super(inMemoryStore);
        this.phonecheckApiRestClient = phonecheckApiRestClient;
        this.osChecker = osChecker;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.applicationVersion = applicationVersion;
        this.localizationService = localizationService;

        devicesDataSyncRequestsQueue = new LinkedBlockingQueue<>();
    }

    // ==============================================================================
    //          Methods to manage device data sync requests queue for 3.0 cloud
    // ==============================================================================

    /**
     * Method to enqueue device data sync request
     *
     * @param syncableDevice device to be synced
     */
    public void enqueueDeviceDataSyncRequest(final SyncableDevice syncableDevice) {
        //TODO: uncomment when we are ready to call the new sync endpoint
        Device device = syncableDevice.getDevice();
//        if (device.getStage() != null || (
//                device instanceof AndroidDevice && ((AndroidDevice) device)
//                        .getAndroidConnectionMode().equals(AndroidConnectionMode.AT))) {
//
//            devicesDataSyncRequestsQueue.add(syncableDevice);
//        }
    }

    /**
     * Method to start Processing devices sync requests queue
     */
    public void startProcessingDevicesDataSyncRequests() {
        syncPendingDevicesRecordsOnCloud();
    }

    /**
     * Method to sync all pending devices records to the cloud.
     */
    private void syncPendingDevicesRecordsOnCloud() {
        // Thread to sync pending device records to cloud via NewCloudDbSync
        new Thread(() -> {
            while (keepSynchronizingData) {
                try {
                    SyncableDevice deviceToSync = devicesDataSyncRequestsQueue.take();
                    MDC.clear();
                    MDC.put("id", deviceToSync.getDevice().getId());

                    final DeviceStage stage = deviceToSync.getDevice().getStage() != null ?
                            deviceToSync.getDevice().getStage() : DISCONNECTED;
                    deviceToSync.getDevice().setStage(stage);

                    prependInitialDefectsToTestResults(deviceToSync.getDevice());

                    boolean shouldCallCloud3SyncRequest = STAGES_TO_CALL_CLOUD_3_DB_SYNC.contains(stage);
                    final Device deviceInTracker = deviceConnectionTracker.getDevice(deviceToSync.getDevice().getId());

                    LOGGER.info("Device stage while syncing data on 3.0 cloud: {}", stage);

                    if (shouldCallCloud3SyncRequest) {
                        Cloud3DbSyncRequest cloud3DbSyncRequest =
                                buildNewCloudSyncRequest(deviceToSync.getTransaction(),
                                        deviceInTracker != null ? deviceInTracker : deviceToSync.getDevice(),
                                        deviceToSync.getDevice().getErasedNotes());

                        LOGGER.info("{} is making cloud 3.0 DB sync request on a new thread: {}",
                                deviceToSync.getSource(), cloud3DbSyncRequest);
                        Cloud3DbSyncResponse cloud3DbSyncResponse = phonecheckApiRestClient
                                .syncDeviceDataWithCloud3(cloud3DbSyncRequest, getInMemoryStore().getUserToken());
                        LOGGER.info("Response received from cloud 3.0 DB sync: {}", cloud3DbSyncResponse);
                    }
                } catch (Exception e) {
                    LOGGER.error("Exception occurred while synchronizing devices data to cloud 3", e);
                }
            }
        }).start();
    }

    // ==================================================================
    //          Methods related to CloudDbSync request building
    // ==================================================================


    private Cloud3DbSyncRequest buildNewCloudSyncRequest(final Transaction transaction, final Device device,
                                                         final String erasedNotes) {
        Cloud3DbSyncRequest.Cloud3DbSyncRequestBuilder cloud3DbSyncRequestBuilder = Cloud3DbSyncRequest.builder();

        boolean isDeviceMicResultsAvailable = device.getDeviceTestResult() != null &&
                device.getDeviceTestResult().getMicrophoneResults() != null;

        final String androidEraseType = DeviceEraseType.ANDROID_ERASE_TYPE.getValue();
        final String iosEraseType = DeviceEraseType.IOS_ERASE_TYPE.getValue();

        Cloud3DbSyncRequest.DeviceResults.DeviceResultsBuilder deviceResultsBuilder =
                Cloud3DbSyncRequest.DeviceResults.builder();

        String workingStatus;
        if (device.isManualEntry()) {
            workingStatus = device.getManualEntryWorkingStatus() != null ?
                    device.getManualEntryWorkingStatus() : StringUtils.EMPTY;
        } else {
            String functionalityStatus =
                    FunctionalityStatusUtil.getFunctionalityStatus(device, false,
                            getInMemoryStore().getAssignedCloudCustomization());
            if (FunctionalityStatusUtil.FULLY_FUNCTIONAL.equals(functionalityStatus)) {
                workingStatus = "Yes";
            } else if (FunctionalityStatusUtil.SEE_NOTES.equals(functionalityStatus)) {
                workingStatus = "No";
            } else if (FunctionalityStatusUtil.PENDING.equals(functionalityStatus)) {
                workingStatus = "Pending";
            } else {
                workingStatus = StringUtils.EMPTY;
            }
        }

        DeviceTestResult deviceTestResult = device.getDeviceTestResult();

        String batteryDrainDuration = StringUtils.EMPTY;
        String batteryChargeStart = StringUtils.EMPTY;
        String batteryChargeEnd = StringUtils.EMPTY;
        String batteryDrainType = StringUtils.EMPTY;
        String totalDischarge = StringUtils.EMPTY;

        if (deviceTestResult.getBatteryResults() != null &&
                deviceTestResult.getBatteryResults().getBatteryDrain() != null) {
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration())) {
                batteryDrainDuration = deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDuration();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery())) {
                batteryChargeStart = deviceTestResult.getBatteryResults().getBatteryDrain().getStartBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery())) {
                batteryChargeEnd = deviceTestResult.getBatteryResults().getBatteryDrain().getEndBattery();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType())) {
                batteryDrainType = deviceTestResult.getBatteryResults().getBatteryDrain().getBatteryDrainType();
            }
            if (StringUtils.isNotEmpty(deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDischarge())) {
                totalDischarge = deviceTestResult.getBatteryResults().getBatteryDrain().getTotalDischarge();
            }
        }


        // Triple <cosmetic Result, Failed cosmetic result, Passed cosmetic result>
        Triple<String, String, String> cosmeticFailedPassedResult =
                getAllCosmeticTestResults(deviceTestResult, getInMemoryStore().getAssignedCloudCustomization());
        String failedCosmeticTestResults = null;
        String passedCosmeticTestResults = null;
        if (cosmeticFailedPassedResult != null) {
            failedCosmeticTestResults = cosmeticFailedPassedResult.getMiddle();
            passedCosmeticTestResults = cosmeticFailedPassedResult.getRight();
        }

        // TODO where this will be mapped?
//        String manualEntryStatus = device.isManualEntry() ? "Yes" : "No";

        String eraseStatus = Boolean.TRUE.equals(device.getIsErasePerformed()) ? "Yes" : "No";

        String simLockStatus = Boolean.TRUE.equals(device.getSimLock()) ? "Locked" :
                (Boolean.FALSE.equals(device.getSimLock()) ? "Unlocked" : StringUtils.EMPTY);

        String unlockStatus = StringUtils.isNotBlank(device.getUnlockStatus()) ?
                device.getUnlockStatus() : ("Locked".equalsIgnoreCase(simLockStatus) ?
                "LK" : "Unlocked".equalsIgnoreCase(simLockStatus) ? "UNLK" : StringUtils.EMPTY);


        Cloud3DbSyncRequest.DeviceResults.EraseInfo.EraseInfoBuilder eraseInfoBuilder =
                Cloud3DbSyncRequest.DeviceResults.EraseInfo
                        .builder()
                        .endTime(device.getEraseEndTime() != null
                                ? DateFormatUtil.millisToUTCDateTime(device.getEraseEndTime(), DATE_TIME_FORMAT)
                                : " ")
                        .erasedSD(device.getEraseStartTime() != null
                                && device.getEraseStartTime() > 0 ? "Yes" : "No")
                        .notes(device.getNotes())
                        .restoreCode(erasedNotes)
                        .serial(device.getSerial())
                        .startTime(device.getEraseStartTime() != null
                                ? DateFormatUtil.millisToUTCDateTime(device.getEraseStartTime(), DATE_TIME_FORMAT)
                                : " ")
                        .status(eraseStatus);

        Cloud3DbSyncRequest.DeviceInfo.DeviceCatalogueInfo.DeviceCatalogueInfoBuilder deviceCatalogueInfoBuilder =
                Cloud3DbSyncRequest.DeviceInfo.DeviceCatalogueInfo
                        .builder()
                        .make(device.getMake())
                        .model(device.getModelNo())
                        .modelName(device.getModel())
                        .os(device.getOperatingSystem())
                        .deviceType(device.getDeviceType().name())
                        .productCode(device.getProductCode())
                        .countryOfOrigin(device.getCountryOfOrigin())
                        .simTech(device.getSimTechnology());

        if (device instanceof IosDevice iosDevice) {
            deviceCatalogueInfoBuilder.productType(iosDevice.getProductType());
            deviceCatalogueInfoBuilder.modelIdentifier(iosDevice.getProductType());
        } else {
            deviceCatalogueInfoBuilder.modelIdentifier("");
        }

        if (device.getDiskSize() != null && device.getDiskSize().getSize() != null) {
            deviceCatalogueInfoBuilder.storage(Integer.parseInt(
                    device.getDiskSize().getSize().toString()));
        }

        Cloud3DbSyncRequest.DeviceResults.CarrierLockResponse.CarrierLockResponseBuilder carrierLockResponseBuilder =
                Cloud3DbSyncRequest.DeviceResults.CarrierLockResponse
                        .builder()
                        .imei(device.getImei())
                        .imei2(device.getImei2())
                        .model(device.getModel())
//                    .policyId()
                        .rawResponse(device.getCarrierLockRawResponse() == null ?
                                StringUtils.EMPTY : device.getCarrierLockRawResponse()).simLock(simLockStatus)
//                    .sn(device.)
                        .status(unlockStatus);

        Cloud3DbSyncRequest.DeviceResults.MdmResponse.MdmResponseBuilder mdmResponseBuilder =
                Cloud3DbSyncRequest.DeviceResults.MdmResponse.builder();

        if (device instanceof IosDevice iosDevice) {
            deviceCatalogueInfoBuilder.regulatoryModelNo(iosDevice.getRegulatoryModelNumber());
//                    .networkType()

            carrierLockResponseBuilder
                    .carrier(iosDevice.getCarrier())
                    .network(iosDevice.getSimCarrierBundleInfo() == null ?
                            StringUtils.EMPTY : iosDevice.getSimCarrierBundleInfo().getProvider());

            mdmResponseBuilder
                    .rawResponse(iosDevice.getMdmRawResponse() == null ?
                            StringUtils.EMPTY : iosDevice.getMdmRawResponse());

            eraseInfoBuilder
                    .type(StringUtils.isNotBlank(iosDevice.getEraserType()) ?
                            iosDevice.getEraserType() : iosEraseType);

            deviceResultsBuilder
                    .oemStatus(iosDevice.getOverallOemStatus() != null ?
                            iosDevice.getOverallOemStatus().getText() : "")
                    .appVersion(getInMemoryStore().getIosAppVersion())
                    .mdmResponse(mdmResponseBuilder.build());

        } else {
            AndroidDevice androidDevice = (AndroidDevice) device;
            try {
                String ramString = androidDevice.getRam(); // "1 GB RAM"
                if (StringUtils.isNotBlank(ramString)) {
                    String[] parts = ramString.split(" ");
                    int ramValue = Integer.parseInt(parts[0]);
                    deviceCatalogueInfoBuilder.memory(ramValue);
                }
            } catch (Exception e) {
                LOGGER.error("Error processing RAM value: {}", e.getMessage());
            }

            deviceResultsBuilder.appVersion(getInMemoryStore().getAndroidAppVersion());

            eraseInfoBuilder.type(androidEraseType);
        }

        Cloud3DbSyncRequest.DeviceResults.EsnResponse.EsnResponseBuilder esnResponseBuilder =
                Cloud3DbSyncRequest.DeviceResults.EsnResponse
                        .builder()
                        .status(String.valueOf(device.getEsnStatus()));
//                .chargeStatus()

        Cloud3DbSyncRequest.DeviceResults.Mics.MicsBuilder micsBuilder =
                Cloud3DbSyncRequest.DeviceResults.Mics.builder()
                        .fMic(isDeviceMicResultsAvailable
                                ? String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getFmAmplitude())
                                : StringUtils.EMPTY)
                        .bMic(isDeviceMicResultsAvailable
                                ? String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getBmAmplitude())
                                : StringUtils.EMPTY)
                        .vMic(isDeviceMicResultsAvailable
                                ? String.valueOf(device.getDeviceTestResult().getMicrophoneResults().getRmAmplitude())
                                : StringUtils.EMPTY);

        Cloud3DbSyncRequest.DeviceResults.ICloudResponse.ICloudResponseBuilder iCloudResponseBuilder =
                Cloud3DbSyncRequest.DeviceResults.ICloudResponse.builder();
//                    .status((device.getSourceApiResponse1() != null ? device.getSourceApiResponse1() : "") + "\n" +
//                            (device.getSourceApiResponse2() != null ? device.getSourceApiResponse2() : ""))

        Cloud3DbSyncRequest.DeviceResults.SimLockResponse.SimLockResponseBuilder simLockResponseBuilder =
                Cloud3DbSyncRequest.DeviceResults.SimLockResponse.builder()
                        .status(simLockStatus)
//                        .code()
                        .source(StringUtils.EMPTY) // TODO : calculate the source for simlock
                        .rawResponse(device.getSimLockRawResponse());

        //TODO How to map OEM data?
        Cloud3DbSyncRequest.DeviceResults.OemParts.OemPartsBuilder oemPartsBuilder =
                Cloud3DbSyncRequest.DeviceResults.OemParts.builder();
//                      .lcd()

        Cloud3DbSyncRequest.DeviceResults.TestInfo testInfo = Cloud3DbSyncRequest.DeviceResults.TestInfo.builder()
                .working(workingStatus)
                .passed(StringUtils.defaultString(getAllPassedTestResults(deviceTestResult, passedCosmeticTestResults),
                        StringUtils.EMPTY))
                .failed(StringUtils.defaultString(getAllFailedTestResults(deviceTestResult, failedCosmeticTestResults),
                        StringUtils.EMPTY))
                .pending(deviceTestResult.getTestResults() == null
                        || deviceTestResult.getTestResults().getPending() == null ? StringUtils.EMPTY :
                        TestResultsUtil.listToCommaSeparatedString(deviceTestResult.getTestResults().getPending()))

//                .cosmeticsWorking()
                .cosmeticsPassed(passedCosmeticTestResults)
                .cosmeticsFailed(failedCosmeticTestResults)
                .cosmeticsPending(deviceTestResult.getCosmeticResults() == null ||
                        deviceTestResult.getCosmeticResults().getPending() == null ? StringUtils.EMPTY :
                        deviceTestResult.getCosmeticResults().getPending())
                .build();

        String batteryStatus = Boolean.TRUE.equals(device.getBatteryDegraded()) ? "Degraded" : "OK";
        Cloud3DbSyncRequest.DeviceResults.BatteryInfo.BatteryInfoBuilder batteryInfoBuilder =
                Cloud3DbSyncRequest.DeviceResults.BatteryInfo.builder()
                        .avgTemperature(String.valueOf(device.getBatteryInfo() != null ?
                                device.getBatteryInfo().getAverageTemperature() :
                                0.0f))
                        .chargeEnd(batteryChargeEnd)
                        .chargeStart(batteryChargeStart)
                        .cocoHealth(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getCocoHealthPercentage()) :
                                StringUtils.EMPTY)
                        .currentMaxCapacity(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getCurrentCapacity()) :
                                StringUtils.EMPTY)
                        .cycle(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getCycle()) : StringUtils.EMPTY)
                        .designMaxCapacity(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getDesignedCapacity()) :
                                StringUtils.EMPTY)
                        .drain(totalDischarge)
                        .drainDuration(batteryDrainDuration)
//                        .drainInfo()
                        .drainType(batteryDrainType)
                        .healthPercentage(getBatteryHealthValue(device))
                        .maxTemperature(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getMaxTemperature()) :
                                StringUtils.EMPTY)
                        .minTemperature(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getMinTemperature()) :
                                StringUtils.EMPTY)
                        .model(device.getBatteryInfo() != null ?
                                device.getBatteryInfo().getModel() :
                                StringUtils.EMPTY)
                        .oemHealth(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getOemHealthPercentage()) :
                                StringUtils.EMPTY)
                        .percentage(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getBatteryPercentage()) :
                                StringUtils.EMPTY)
                        .resistance(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getBatteryResistance()) :
                                StringUtils.EMPTY)
                        .serial(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getSerial()) :
                                StringUtils.EMPTY)
                        .shutdown(device.getFccid())
                        .source((device.getBatteryInfo() != null &&
                                device.getBatteryInfo().getSource() != null) ?
                                device.getBatteryInfo().getSource().getKey() :
                                StringUtils.EMPTY)
                        .status(device.getBatteryInfo() != null ?
                                batteryStatus :
                                StringUtils.EMPTY)
                        .temperature(device.getBatteryInfo() != null ?
                                String.valueOf(device.getBatteryInfo().getTemperature()) :
                                StringUtils.EMPTY);

        Cloud3DbSyncRequest.DeviceInfo.DeviceInfoBuilder deviceInfoBuilder = Cloud3DbSyncRequest.DeviceInfo.builder()
                .deviceCatalogueInfo(deviceCatalogueInfoBuilder.build())
                .carrier(device.getCarrier())
                .decimalMeid(device.getMeidDecimal())
                .decimalMeid2(device.getMeidDecimal2())
                .udid(device.getId())
                .eid(device.getEid())
                .serial(device.getSerial())
                .imei(device.getImei())
                .wifiMacAddress(device.getWifiAddress())
                .imei2(device.getImei2())
                .lpn(device.getLpn())
                .meid(device.getMeid())
                .meid2(device.getMeid2())
                .pesn(device.getPesn())
                .pesn2(device.getPesn2())
                .simSerial(device.getSimSerial())
                .simSerial2(device.getSimSerial2())
                .deviceID(device.getId());

        Cloud3DbSyncRequest.TransactionInfo.TransactionInfoBuilder transactionInfoBuilder =
                Cloud3DbSyncRequest.TransactionInfo.builder()
                        .boxNo(StringUtils.isNotBlank(transaction.getBoxNo()) ?
                                Integer.parseInt(transaction.getBoxNo()) : 0)
                        .buildNo(applicationVersion)
                        .cloudTransaction(0) // cloud transaction indicates if the record was updated from cloud
                        // for Desktop this should always be set to 0
                        .invoiceId(transaction.getInvoiceNo())
                        .platform(osChecker.getName())
                        .qty(StringUtils.isNotBlank(transaction.getQty()) ?
                                Integer.parseInt(transaction.getQty()) : 0)
                        .stationLocalId(Integer.valueOf(transaction.getTransactionId()).toString())
//                        .vendorId(Integer.parseInt(transaction.getVendorName()))
                        .warehouseId(getInMemoryStore().getWarehouseId());

        deviceResultsBuilder
                .batteryInfo(batteryInfoBuilder.build())
                .carrierLockResponse(carrierLockResponseBuilder.build())
                .simLockResponse(simLockResponseBuilder.build())
                .eraseInfo(eraseInfoBuilder.build())
                .esnResponse(esnResponseBuilder.build())
                .iCloudResponse(iCloudResponseBuilder.build())
                .mdmResponse(mdmResponseBuilder.build())
                .mics(micsBuilder.build())
                .portNumber(device.getPortNumber() == null || device.getPortNumber() == -1 ? 0 :
                        device.getPortNumber() + 1)
//                .shopfloorRouteInfo(shopfloorRouteInfo)
//                .apiResponse(apiResponse)
                .testInfo(testInfo)
                .oemParts(oemPartsBuilder.build())
//                .gradingResult("")
//                .iftCodes("")
                .grade(device.getGrade())
                .deviceLock(device.getDeviceLock() != null ? String.valueOf(device.getDeviceLock()) :
                        StringUtils.EMPTY)
                .rooted(RootedStatus.ROOTED.equals(device.getRooted()) ? 1 : 0)
                .eSimErased(getESimErased(device))
                .eSimPresent(getESimSupported(device) ? (device.isEsimActive() ? "Yes" : "No") : "N/A")
                .osVersion(device.getOperatingSystem())
                .firmware(device.getFirmware())
                .customField(device.getCustom1())
                .skuCode(device.getSkuCode() != null ? device.getSkuCode() : "")
                .initialDeviceState(String.valueOf(device.getDeviceState()))
//                .ebayRefurbished()
//                .ebayRejection()
//                .ebayGradeId()
//                .amazonRenewed()
//                .amazonRenewedRejection()
//                .swappaQualified()
//                .swappaRejection()
//                .backmarketQualified()
//                .backmarketRejection()
//                .isMobileCosmetics() This field present in TransactionResponse not in Transaction
//                .labelPrinted()
//                .finalPrice()
                .notes(device.getNotes());


        cloud3DbSyncRequestBuilder.deviceInfo(deviceInfoBuilder.build())
                .deviceResults(deviceResultsBuilder.build())
                .transactionInfo(transactionInfoBuilder.build())
                .testingCompleted(deviceTestResult.getTestResults() == null ||
                        deviceTestResult.getTestResults().getTestingCompleted() == null ? 0 : 1)
                .testPlanName(device.getTestPlan())
                .build();

        return cloud3DbSyncRequestBuilder.build();
    }

    private void prependInitialDefectsToTestResults(final Device device) {
        device.setDeviceTestResult(device.getDeviceTestResult());
        TestResultsUtil.prependInitialDefectsToAppResults(device,
                getInMemoryStore().getAssignedCloudCustomization(), localizationService);
    }
}
