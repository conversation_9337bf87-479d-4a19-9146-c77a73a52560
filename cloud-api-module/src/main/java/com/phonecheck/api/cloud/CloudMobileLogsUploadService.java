package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.GetUploadIdResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.io.File;

@Service
@AllArgsConstructor
public class CloudMobileLogsUploadService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CloudMobileLogsUploadService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Requests an upload ID from the cloud for syncing mobile device logs.
     *
     * @param userToken  The token of the logged-in user.
     * @param osName     Operating system, e.g., "android" or "ios".
     * @param appVersion App version in the format "phonecheck_X_Y_Z".
     * @param fileName   Name of the log file to be uploaded.
     * @return Response containing the upload ID, or null if the request failed.
     */
    public String getLogsUploadId(final String userToken,
                                  final String osName,
                                  final String appVersion,
                                  final String fileName) {
        try {
            GetUploadIdResponse response = phonecheckApiRestClient.getLogsUploadId(
                    userToken, osName, appVersion, fileName);
            LOGGER.info("Upload ID response received for log file [{}]: {}", fileName, response);
            if (response != null && response.getUploadId() != null) {
                return response.getUploadId();
            }
        } catch (final RestClientException e) {
            LOGGER.error("Failed to retrieve upload ID for mobile logs", e);
        }
        return null;
    }

    /**
     * Uploads the zipped mobile log file to the cloud using the given upload ID and user details.
     *
     * @param userToken   The token of the logged-in user.
     * @param uploadId    The upload ID received from the cloud.
     * @param osName      The operating system name (e.g., "android" or "ios").
     * @param appVersion  The app version in format "phonecheck_X_Y_Z".
     * @param file        The ZIP file to upload.
     * @return {@code true} if the upload was successful, {@code false} otherwise.
     */
    public boolean uploadLogZipFile(final String userToken,
                                    final String uploadId,
                                    final String osName,
                                    final String appVersion,
                                    final File file) {
        try {
            return phonecheckApiRestClient.uploadLogZipFile(
                    userToken, uploadId, osName, appVersion, file);
        } catch (final RestClientException e) {
            LOGGER.error("Error uploading log file [{}] with upload ID [{}]", file.getName(), uploadId, e);
            return false;
        }
    }

}
