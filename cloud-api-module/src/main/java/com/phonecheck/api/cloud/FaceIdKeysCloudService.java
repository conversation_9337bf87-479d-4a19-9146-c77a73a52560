package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.FaceIDKeysResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class FaceIdKeysCloudService {
    private static final Logger LOGGER = LoggerFactory.getLogger(FaceIdKeysCloudService.class);

    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Based on the user token, retrieve a list of faceID keys to match in the syslogs.
     *
     * @param userToken user token
     * @return list of faceId keys
     */
    public FaceIDKeysResponse getFaceIDKeys(final String userToken) {
        try {
            return phonecheckApiRestClient.getFaceIDKeys(userToken);
        } catch (final RestClientException e) {
            LOGGER.error("Error while getting faceId keys from cloud", e);
        }
        return null;
    }
}
