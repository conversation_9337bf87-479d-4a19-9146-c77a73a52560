package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CarrierSimLockStatusRequest;
import com.phonecheck.model.cloudapi.CarrierSimLockStatusResponse;
import com.phonecheck.model.cloudapi.SimLockLicenseManagerRequest;
import com.phonecheck.model.cloudapi.SimLockLicenseManagerResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudCarrierAndSimLockService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudCarrierAndSimLockService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get carrier and sim lock status
     *
     * @param request CarrierSimLockStatusRequest
     * @return carrier and sim lock status response
     */
    public CarrierSimLockStatusResponse getCarrierAndSimLockStatus(
            final CarrierSimLockStatusRequest request) {
        CarrierSimLockStatusResponse response = null;
        try {
            response = cloudApiRestClient
                    .getCarrierAndSimLockStatus(request);
        } catch (final RestClientException e) {
            LOGGER.error("Error while getting carrier and sim lock status", e);
        }
        return response;
    }

    /**
     * Get sim lock status
     *
     * @param request SimLockLicenseManagerRequest
     * @return sim lock status response
     */
    public SimLockLicenseManagerResponse getSimLockStatus(
            final SimLockLicenseManagerRequest request) {
        SimLockLicenseManagerResponse response = null;
        try {
            response = cloudApiRestClient
                    .getSimLockLicenseManagerResponse(request);

        } catch (final RestClientException e) {
            LOGGER.error("Error while getting sim lock status", e);
        }

        return response;
    }
}
