package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.SelectPackageResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class SelectPackageService {
    private static final Logger LOGGER = LoggerFactory.getLogger(SelectPackageService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;
    private final CustomizationPackageCacheService customizationPackageCacheService;

    /**
     * Retrieves the assigned warehouse profile list and sets up the package data in memory.
     *
     * @param userToken The master token for authentication.
     * @return A map containing the profile names as keys and corresponding data as values.
     */
    public Map<String, SelectPackageResponse.ProfilesConfiguration> setupPackageData(final String userToken) {
        Map<String, SelectPackageResponse.ProfilesConfiguration> response = null;
        try {
            SelectPackageResponse output = phonecheckApiRestClient.getProfileData(userToken);
            if (output != null && output.getProfilesConfiguration() != null) {
                response = output.getProfilesConfiguration().stream()
                        .collect(Collectors.toMap(
                                SelectPackageResponse.ProfilesConfiguration::getProfileName,
                                data -> data,
                                (existing, replacement) -> existing
                        ));
                customizationPackageCacheService.savePackages(response);
            }
        } catch (Exception e) {
            LOGGER.error("Failed to get assigned profile response from the cloud", e);
            response = customizationPackageCacheService.loadPackages();
        }
        return response;
    }
}
