package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.GetVersionsResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.util.List;

@Service
@AllArgsConstructor
public class ApiVersionsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ApiVersionsService.class);
    private static final String KEY = "Unfj397UHNDjdy6ehjfjbsj78ABDGs";
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Service to perform the cloud request for APIRecordsVersions
     *
     * @return subResultResponse List of GetVersionsSubResultResponse
     */
    public List<GetVersionsResponse.GetVersionsSubResultResponse> getAPIRecordsVersions() {
        try {
            GetVersionsResponse getVersionsResponse =
                    cloudApiRestClient.getApiVersions(KEY);
            List<GetVersionsResponse.GetVersionsSubResultResponse> subResultResponse = null;
            if (getVersionsResponse != null) {
                if (getVersionsResponse.getStatus()) {
                    subResultResponse = getVersionsResponse.getResult();
                }
                return subResultResponse;
            }
        } catch (final RestClientException e) {
            LOGGER.error("Failed to get latest API versions from cloud", e);
        }
        return null;
    }

}
