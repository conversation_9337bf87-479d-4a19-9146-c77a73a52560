package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.ServerApiTokenResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class MasterLoginService {
    private static final Logger LOGGER = LoggerFactory.getLogger(MasterLoginService.class);
    private static final int API_RETRIES = 3;

    private final PhonecheckApiRestClient phonecheckApiRestClient;

    /**
     * Get the Server API token for authentication
     *
     * @param username username
     * @param password password
     * @return token
     */
    public String getServerApiToken(final String username, final String password) {
        int attempt = 0;
        do {
            try {
                ServerApiTokenResponse response = phonecheckApiRestClient.getServerApiToken(username, password);
                if (response != null) {
                    return response.getToken();
                }
            } catch (final RestClientException e) {
                LOGGER.error("Error while getting server api token (attempt: " + attempt + ")", e);
            }

            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                // do nothing
            }
            attempt++;
        } while (attempt < API_RETRIES);

        return null;
    }

}
