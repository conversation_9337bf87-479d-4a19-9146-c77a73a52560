package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudPhoneCheckApiRestClient;
import com.phonecheck.model.phonecheckapi.GetLicenseBuildResponse;
import com.phonecheck.model.util.Os;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class BuildVersionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(BuildVersionService.class);
    private final CloudPhoneCheckApiRestClient cloudPhoneCheckApiRestClient;

    /**
     * Get license build information for my username and operating system
     *
     * @param userName        user name
     * @param operatingSystem os for which we need the update file url
     * @return LicenseBuildInfoForOs
     */
    public GetLicenseBuildResponse.LicenseBuildInfoForOs getLicenseBuildInfoByOs(final String userName,
                                                                                 final Os operatingSystem) {
        try {
            GetLicenseBuildResponse licenseBuildResponse =
                    cloudPhoneCheckApiRestClient.getLicenseBuild(userName);
            if (licenseBuildResponse != null) {
                LOGGER.info("License Build Info response: {}", licenseBuildResponse);

                if (Os.MAC.equals(operatingSystem)) {
                    return licenseBuildResponse.getMacOs();
                } else if (Os.WINDOWS.equals(operatingSystem)) {
                    return licenseBuildResponse.getWindows();
                } else {
                    return licenseBuildResponse.getLinux();
                }
            }
        } catch (final RestClientException e) {
            LOGGER.error("Failed to get license build information from cloud", e);
        }
        return null;
    }

}
