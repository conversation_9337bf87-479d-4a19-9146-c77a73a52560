package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.SkuSchemaResponse;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.status.WorkingStatus;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudSkuSchemaService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudSkuSchemaService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get SKU schema from the cloud
     *
     * @param licenseId license Id from inMemory
     * @return String target device grade
     */
    public SkuSchemaResponse getSKUSchema(final String licenseId) {
        if (StringUtils.isBlank(licenseId)) {
            LOGGER.error("License Id is null or empty. LicenseId: {}", licenseId);
            return null;
        }
        LOGGER.info("Get SKU Schema from cloud for LicenseId:{}",
                licenseId);
        SkuSchemaResponse response = null;
        try {
            response = cloudApiRestClient.getSKUSchema(licenseId);
        } catch (final RestClientException e) {
            LOGGER.error("Error getting SKU Schema", e);
        }

        return response;
    }

    /**
     * Get Inventory SKU schema from the cloud
     *
     * @param licenseId license Id from inMemory
     * @param device    device for sku code
     * @return String Inventory SKU Code
     */
    public String getInventorySKUSchema(final String licenseId, final Device device) {

        if (StringUtils.isBlank(licenseId)) {
            LOGGER.error("License Id is null or empty while checking Inventory SKU. Returning null");
            return null;
        }

        LOGGER.info("Get Inventory SKU Schema from cloud for LicenseId:{}", licenseId);
        String response = null;
        try {
            String modelName = StringUtils.isNotEmpty(device.getModel()) ? device.getModel() : StringUtils.EMPTY;
            String storage = (device.getDiskSize() != null && StringUtils.isNotEmpty(device.getDiskSize().toString()))
                    ? device.getDiskSize().toString() : StringUtils.EMPTY;
            String carrier = StringUtils.isNotEmpty(device.getCarrier()) ? device.getCarrier() : StringUtils.EMPTY;
            String color = StringUtils.isNotEmpty(device.getColor()) ? device.getColor() : StringUtils.EMPTY;
            String grade = StringUtils.isNotEmpty(device.getGrade()) ? device.getGrade() : StringUtils.EMPTY;

            String modelNo = StringUtils.EMPTY;
            String touchID = StringUtils.EMPTY;
            if (device instanceof IosDevice iosDevice) {
                if (StringUtils.isNotEmpty(iosDevice.getRegulatoryModelNumber())) {
                    modelNo = iosDevice.getRegulatoryModelNumber();
                }
                if (iosDevice.getTouchIdSupported() != null && iosDevice.getTouchIdSupported()) {
                    touchID = iosDevice.getTouchIdSensor() != null
                            && iosDevice.getTouchIdSensor() == WorkingStatus.YES
                            ? WorkingStatus.YES.getKey() : WorkingStatus.NO.getKey();
                }
            } else {
                if (StringUtils.isNotEmpty(device.getModelNo())) {
                    modelNo = device.getModelNo();
                }
            }

            LOGGER.info("Info sending to cloud to get inventory SKU licenseId :{}," +
                            "modelName: {},Storage: {},Carrier: {}," +
                            "Color: {},Grade: {},ModelNo: {}, touchId: {}",
                    licenseId, modelName, storage, carrier, color, grade, modelNo, touchID);

            response = cloudApiRestClient.getInventorySKUSchema(
                    licenseId, modelName, storage, carrier, color, grade, modelNo, touchID
            );

            LOGGER.info("Response to get Inverntory SKU received as: {}", response);
        } catch (final RestClientException e) {
            LOGGER.error("Error getting Inventory SKU Schema", e);
        }

        return response;
    }

}
