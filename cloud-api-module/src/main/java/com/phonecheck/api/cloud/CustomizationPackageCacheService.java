package com.phonecheck.api.cloud;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.phonecheck.model.phonecheckapi.SelectPackageResponse;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

import static com.phonecheck.model.constants.FileConstants.CUSTOMIZATION_PACKAGES_FILE_NAME;

/**
 * Service class for managing the caching of customization package data in a local JSON file.
 * Provides methods for saving and loading the customization package data to and from a cache file.
 */
@Service
public class CustomizationPackageCacheService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CustomizationPackageCacheService.class);
    private final FileUtil fileUtil;
    private final Gson gson;
    private final String packagesCacheFilePath;


    public CustomizationPackageCacheService(final SupportFilePath supportFilePath, final FileUtil fileUtil,
                                            final Gson gson) {
        this.fileUtil = fileUtil;
        this.packagesCacheFilePath = String.join("/",
                supportFilePath.getPaths().getRootFolderPath(), CUSTOMIZATION_PACKAGES_FILE_NAME);
        this.gson = gson;
    }

    /**
     * Saves the provided customization package map to a local JSON file.
     *
     * @param packages map of package name to ProfilesConfiguration object.
     */
    public void savePackages(final Map<String, SelectPackageResponse.ProfilesConfiguration> packages) {
        try {
            File file = new File(packagesCacheFilePath);
            fileUtil.writeStringToFile(file, gson.toJson(packages));
        } catch (Exception e) {
            LOGGER.error("Failed to write CustomizationPackages.json", e);
        }
    }

    /**
     * Loads the customization packages from the local JSON file.
     *
     * @return a map of package names to ProfilesConfiguration objects, or an empty map if file is missing or invalid.
     */
    public Map<String, SelectPackageResponse.ProfilesConfiguration> loadPackages() {
        try {
            File file = new File(packagesCacheFilePath);
            if (!file.exists()) {
                LOGGER.warn("CustomizationPackages.json not found");
                return new HashMap<>();
            }
            String json = fileUtil.readFile(file);
            if (json == null || json.trim().isEmpty()) {
                LOGGER.warn("CustomizationPackages.json is empty");
                return new HashMap<>();
            }
            Type type = new TypeToken<Map<String, SelectPackageResponse.ProfilesConfiguration>>() {
            }.getType();
            Map<String, SelectPackageResponse.ProfilesConfiguration> result = gson.fromJson(json, type);
            return result != null ? result : new HashMap<>();
        } catch (Exception e) {
            LOGGER.error("Failed to read CustomizationPackages.json", e);
            return new HashMap<>();
        }
    }
}