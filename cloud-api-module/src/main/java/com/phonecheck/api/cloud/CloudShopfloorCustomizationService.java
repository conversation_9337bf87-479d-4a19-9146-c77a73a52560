package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudShopfloorApiRestClient;
import com.phonecheck.model.cloudapi.FlagDeviceResponse;
import com.phonecheck.model.cloudapi.ShopfloorCustomizationResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudShopfloorCustomizationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudShopfloorCustomizationService.class);
    private final CloudShopfloorApiRestClient cloudShopfloorApiRestClient;

    /**
     * Get the shopfloor customization data from cloud api
     *
     * @param masterId
     * @param warehouseId
     * @param licenseId
     * @return ShopfloorCustomizationDb
     */
    public ShopfloorCustomizationResponse getShopfloorCustomization(final String masterId,
                                                                                           final String warehouseId,
                                                                                           final String licenseId) {
        try {
            final ShopfloorCustomizationResponse response =
                    cloudShopfloorApiRestClient.getShopfloorCustomization(masterId, warehouseId, licenseId);
            LOGGER.info("Shopfloor customization response {}", response);
            return response;
        } catch (final RestClientException e) {
            LOGGER.error("Couldn't get shop floor customization for user even after retries", e);
        }
        return null;
    }

    /**
     * This method fetches flag device data from the Shopfloor portal
     * @param masterId masterid
     * @param warehouseId warehouse id
     * @param stationId station id of the user
     * @return Response of cloud call for flag devices
     * */
    public FlagDeviceResponse getFlagDevicesInfo(final String masterId, final String warehouseId,
                                                 final String stationId) {
        try {
            return cloudShopfloorApiRestClient.getFlagDeviceData(masterId, warehouseId, stationId);
        } catch (final RestClientException e) {
            LOGGER.error("Error while fetching flag devices", e);
        }
        return null;
    }

}
