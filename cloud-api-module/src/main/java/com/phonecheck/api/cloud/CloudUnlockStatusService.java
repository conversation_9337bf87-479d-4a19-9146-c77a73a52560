package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.UnlockStatusBySerialResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudUnlockStatusService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudUnlockStatusService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Get device unlock status for a device
     *
     * @param serial    Device Serial
     * @param licenseId License Id
     * @return unlock status
     */
    public String getDeviceUnlockStatus(final String serial, final Integer licenseId) {
        try {
            final UnlockStatusBySerialResponse response =
                    cloudApiRestClient.getDeviceUnlockStatusBySerial(serial, licenseId);

            if (response != null) {
                return response.getUnlockStatus();
            }
            return null;
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get device info by serial", e);
            return null;
        }
    }
}