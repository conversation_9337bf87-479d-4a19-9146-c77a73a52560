package com.phonecheck.api.cloud;

import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.VendorInvoiceInfoResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class CloudVendorInvoiceService {
    private static final Logger LOGGER = LoggerFactory.getLogger(CloudVendorInvoiceService.class);
    private final CloudApiRestClient cloudApiRestClient;

    /**
     * Retrieves vendor and invoice no. list from cloud
     *
     * @param stationId station name
     * @param masterId  master id
     * @return VendorInvoiceInfoResponse
     */
    public VendorInvoiceInfoResponse getVendorInvoiceListInfo(final String stationId, final String masterId) {
        try {
            return cloudApiRestClient.getVendorInvoiceList(stationId, masterId);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get vendor invoice list", e);
            return null;
        }
    }
}
