package com.phonecheck.api.cloud;

import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.model.phonecheckapi.GetAirpodsVersionResponse;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

@Service
@AllArgsConstructor
public class AirpodsVersionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AirpodsVersionService.class);
    private final PhonecheckApiRestClient phonecheckApiRestClient;


    /**
     * Get airpods SDK version from cloud
     *
     * @param userToken user token
     * @return airpods sdk version
     */
    public String getAirpodsVersionFromCloud(final String userToken) {
        try {
            GetAirpodsVersionResponse response =
                    phonecheckApiRestClient.getAirpodsVersion(userToken);
            if (response != null && response.getData() != null) {
                return response.getData().getAirpods();
            }
        } catch (final RestClientException e) {
            LOGGER.error("Failed to get airpods version from cloud", e);
        }
        return null;
    }
}
