package com.phonecheck.api.client;

import lombok.Getter;
import lombok.Setter;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@Getter
@Setter
@Retryable(
        retryFor = RestClientException.class,
        maxAttempts = 3,
        backoff = @Backoff(delay = 2000, multiplier = 2)
)
public abstract class AbstractCloudClient {

    // TODO: Add these while making calls to cloud api.
    protected static final String BUILD_NO_HEADER = "BuildNo";
    protected static final String PLATFORM_HEADER = "Platform";
    protected static final String USER_HEADER = "User";

    private RestTemplate restTemplate;
    private RestTemplate euRestTemplate;

    AbstractCloudClient(final RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    AbstractCloudClient(final RestTemplate restTemplate, final RestTemplate euRestTemplate) {
        this.restTemplate = restTemplate;
        this.euRestTemplate = euRestTemplate;
    }

    public RestTemplate getRestTemplate() {
        return restTemplate;
    }

    /**
     * Returns rest template having either default url or EU url
     *
     * @param isEuServer whether EU server rest template required
     * @return RestTemplate
     */
    public RestTemplate getRestTemplate(final boolean isEuServer) {
        if (isEuServer && euRestTemplate != null) {
            return euRestTemplate;
        } else {
            return restTemplate;
        }
    }
}
