package com.phonecheck.device.connection.manager;

import com.phonecheck.model.android.SetupAttemptStatus;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceLock;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.device.DeviceConnectionAutomationEvent;
import com.phonecheck.model.event.device.android.AndroidPairingNeededEvent;
import com.phonecheck.model.event.device.android.AndroidPairingSuccessEvent;
import com.phonecheck.model.event.device.ios.*;
import com.phonecheck.model.event.syslog.StartDeviceSysLogEvent;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class DeviceStageManager {
    private static final Logger LOGGER = LoggerFactory.getLogger(DeviceStageManager.class);

    private final ApplicationEventPublisher eventPublisher;

    /**
     * Raise events for an existing device based on its current stage.
     *
     * @param device target device
     */
    public void raiseEventForDeviceInProgress(final Device device) {
        device.setReconnectRequired(false);
        boolean isSetupOnly = device.getAutomationSteps() != null &&
                device.getAutomationSteps().stream().anyMatch(automationStep ->
                CloudCustomizationResponse.AutomationSteps.SETUP.equals(automationStep.getStep()));
        // we check is erasing to handle disconnection during erase
        // when other processes(stage) in background was running
        if (device.isEraseInProgress()) {
            if (device instanceof IosDevice iosDevice) {
                eventPublisher.publishEvent(new IosEraseSuccessEvent(this, iosDevice));
                return;
            }
        }
        if (device instanceof AndroidDevice androidDevice
                && SetupAttemptStatus.WAITING_FOR_ERASE.equals(androidDevice.getSetupAttemptStatus())) {
            LOGGER.info("Device reconnected after erase, continuing with connection automation.");

            androidDevice.setSetupAttemptStatus(SetupAttemptStatus.ERASED);
            eventPublisher.publishEvent(new DeviceConnectionAutomationEvent(this, androidDevice));
            return;
        }

        switch (device.getStage()) {
            case PAIRING_FAILED -> {
                if (device instanceof IosDevice iosDevice) {
                    eventPublisher.publishEvent(new IosPairingNeededEvent(this, iosDevice,
                            false, !isSetupOnly));
                } else if (device instanceof AndroidDevice androidDevice) {
                    eventPublisher.publishEvent(new AndroidPairingNeededEvent(this, androidDevice, !isSetupOnly));
                }
            }

            case PAIRING_SUCCEEDED, INFO_COLLECTION_FAILED, INFO_COLLECTION_SUCCEEDED -> {
                if (device instanceof IosDevice iosDevice) {
                    eventPublisher.publishEvent(
                            new IosPairSuccessEvent(this, iosDevice, false, !isSetupOnly));
                } else if (device instanceof AndroidDevice androidDevice) {
                    eventPublisher.publishEvent(new AndroidPairingSuccessEvent(this, androidDevice, !isSetupOnly));
                }
            }

            case ACTIVATION_SUCCEEDED,
                    MDM_SUCCEEDED,
                    MDM_FAILED -> {
                if (device instanceof IosDevice iosDevice) {
                    eventPublisher.publishEvent(
                            new IosActivationSuccessEvent(this, iosDevice, DeviceLock.NA, !isSetupOnly,
                                    false));
                }
                eventPublisher.publishEvent(new StartDeviceSysLogEvent(this, device));
            }

            case ACTIVATING,
                    ACTIVATION_FAILED -> {
                if (device instanceof IosDevice iosDevice) {
                    LOGGER.info("Publishing IosActivationNeededEvent from device Stage Manager {}",
                            iosDevice.getActivationStatus());
                    eventPublisher.publishEvent(new IosActivationNeededEvent(this, iosDevice, !isSetupOnly,
                            false));
                }
                eventPublisher.publishEvent(new StartDeviceSysLogEvent(this, device));
            }

            // even if dev mount succeeded and the device reconnects with 5 seconds, we will reprocess the peo
            // functionality
            case PEO_INITIALIZED,
                    DEV_IMAGE_MOUNT_SUCCEEDED,
                    ENABLE_DEV_MODE_FAILURE,
                    DEV_IMAGE_MOUNT_FAILED,
                    ENABLE_DEV_MODE_SUCCESS,
                    APP_INSTALL_FAILURE -> {
                if (device instanceof IosDevice iosDevice) {
                    eventPublisher.publishEvent(new IosInitiatePeoEvent(this, iosDevice, !isSetupOnly,
                            true));
                }
                eventPublisher.publishEvent(new StartDeviceSysLogEvent(this, device));
            }

            case ERASE_IN_PROGRESS,
                    ERASE_SUCCESS -> {
                if (device instanceof IosDevice iosDevice) {
                    eventPublisher.publishEvent(new IosEraseSuccessEvent(this, iosDevice));
                }
            }

            case ENABLE_DEV_MODE_IN_PROGRESS -> {
                if (device instanceof IosDevice iosDevice) {
                    eventPublisher.publishEvent(new IosDevModeEvent(this, iosDevice));
                }

                eventPublisher.publishEvent(new StartDeviceSysLogEvent(this, device));
            }

            case RESTORE_IN_PROGRESS, RESTORE_FAILED -> LOGGER.info("Device is in restore.");

            case READY, NOT_READY, APP_TESTING_DONE ->
                    eventPublisher.publishEvent(new StartDeviceSysLogEvent(this, device));

            default -> LOGGER.warn("Illegal Device stage");
        }
    }
}
