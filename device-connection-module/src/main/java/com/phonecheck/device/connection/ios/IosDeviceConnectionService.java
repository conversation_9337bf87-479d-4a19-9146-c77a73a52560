package com.phonecheck.device.connection.ios;

import com.phonecheck.command.device.ios.connection.IosDevicesCommand;
import com.phonecheck.command.device.ios.connection.IosDevicesWithEcidCommand;
import com.phonecheck.command.device.ios.connection.IosMuxDevicesCommand;
import com.phonecheck.command.device.ios.info.IosDfuCommand;
import com.phonecheck.command.system.windows.GetWindowsUsbAppleDevicesCommand;
import com.phonecheck.command.system.windows.IosDfuCommandForWindows;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.device.ios.connection.IosConnectedDevicesParser;
import com.phonecheck.parser.device.ios.connection.IosConnectedMuxDevicesParser;
import com.phonecheck.parser.device.ios.connection.IosDeviceWithEcidParser;
import com.phonecheck.parser.device.ios.info.IosDfuParser;
import com.phonecheck.parser.system.windows.WindowsUsbDevicesParser;
import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

@Service
@AllArgsConstructor
public class IosDeviceConnectionService {

    private final CommandExecutor executor;
    private final IosDfuParser iosDfuParser;
    private final OsChecker osChecker;
    private final IosConnectedMuxDevicesParser iosConnectedMuxDevicesParser;
    private final IosConnectedDevicesParser iosConnectedDevicesParser;
    private final WindowsUsbDevicesParser windowsUsbDevicesParser;
    private final IosDeviceWithEcidParser iosDeviceWithEcidParser;

    /**
     * Creates an {@link IosDevice} for each currently-connected UUID in Mux mode
     *
     * @return all currently-connected mux devices
     * @throws IOException when the output couldn't be read
     */
    public Set<IosDevice> getConnectedMuxDevices() throws IOException {
        final String output = executor.execute(new IosMuxDevicesCommand());
        if (output != null) {
            // Parse devices from output
            return iosConnectedMuxDevicesParser.parse(output);
        } else {
            return null;
        }
    }

    /**
     * Creates an {@link IosDevice} for each currently-connected UUID in
     * Usb and Mux mode based on the operating system
     *
     * @return all currently-connected mux devices
     * @throws IOException when the output couldn't be read
     */
    public Set<IosDevice> getConnectedDevices() throws IOException {
        Set<IosDevice> devices;

        if (osChecker.isMac()) {
            devices = getConnectedDevicesOnMac();
        } else {
            devices = getConnectedDevicesOnWindows();
        }
        Set<IosDevice> devicesInDfuMode = getDevicesInDfuMode();
        if (devicesInDfuMode != null && !devicesInDfuMode.isEmpty()) {
            devices.addAll(devicesInDfuMode);
        }
        return devices;
    }

    /**
     * Creates an {@link IosDevice} for each currently-connected UUID on Mac
     *
     * @return all currently-connected devices
     * @throws IOException when the output couldn't be read
     */
    public Set<IosDevice> getConnectedDevicesOnMac() throws IOException {
        final String output = executor.execute(new IosDevicesCommand());
        if (output != null) {
            // Parse devices from output
            return iosConnectedDevicesParser.parse(output);
        } else {
            return null;
        }
    }

    /**
     * Creates an {@link IosDevice} for each currently-connected UUID on Windows
     *
     * @return all currently-connected devices
     * @throws IOException when the output couldn't be read
     */
    public Set<IosDevice> getConnectedDevicesOnWindows() throws IOException {
        final String output = executor.execute(new GetWindowsUsbAppleDevicesCommand());
        if (output != null) {
            // Parse devices from output
            Set<IosDevice> usbDevices = windowsUsbDevicesParser.parse(output);
            // Retrieve mux mode devices
            Set<IosDevice> muxDevices = getConnectedMuxDevices();
            usbDevices
                    .stream()
                    .filter(muxDevices::contains)
                    .forEach(iosDevice -> iosDevice.setUsbMode(false));

            return usbDevices;
        } else {
            return null;
        }



    }

    /**
     * Method to find all the devices in DFU/Recovery mode
     *
     * @return set of  devices in DFU/Recovery mode
     */
    public Set<IosDevice> getDevicesInDfuMode() throws IOException {
        Set<IosDevice> devicesInDfuMode = null;
        String output = null;
        if (osChecker.isMac()) {
            output = executor.execute(new IosDfuCommand());
        } else if (osChecker.isWindows()) {
            output = executor.execute(new IosDfuCommandForWindows());
        }

        if (output != null) {
            devicesInDfuMode = iosDfuParser.parse(output);
        }
        return devicesInDfuMode;
    }


    /**
     * This method will give list of all devices (UDID - ECID)
     * which are currently connected in normal mode
     *
     * @return Set of IosDevice
     */
    public Set<IosDevice> getAllConnectedDevicesByEcid() throws IOException {
        Set<IosDevice> devices = new HashSet<>();
        final String outputFromEcidCmd = executor.execute(new IosDevicesWithEcidCommand());
        if (StringUtils.isNotBlank(outputFromEcidCmd)) {
            devices.addAll(iosDeviceWithEcidParser.parse(outputFromEcidCmd));
        }
        return devices;
    }


}