package com.phonecheck.device.connection.ios;

import com.phonecheck.command.device.ios.info.IosRecoveryCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.customization.AutomationWorkflow;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.event.FirmwareDownloadRequestEvent;
import com.phonecheck.model.event.device.DeviceConnectionAutomationEvent;
import com.phonecheck.model.event.device.DeviceManualPrintCustomAutomationEvent;
import com.phonecheck.model.event.device.DeviceManualRestoreCustomAutomationEvent;
import com.phonecheck.model.event.device.ios.IosDeviceRestoreResponseEvent;
import com.phonecheck.model.firmware.FirmwareModel;
import com.phonecheck.model.ios.IosProperty;
import com.phonecheck.model.status.DeviceRestoreStatus;
import com.phonecheck.model.status.RestoreResponseCode;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.FileUtil;
import io.micrometer.common.util.StringUtils;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * This is service class for restore process. It has methods to support restore process
 */
@Service
@AllArgsConstructor
public class IosDeviceRestoreService {
    private static final Logger LOGGER = LoggerFactory.getLogger(IosDeviceRestoreService.class);

    private final CommandExecutor executor;

    private final IosDeviceConnectionService iosDeviceConnectionService;

    private final ApplicationEventPublisher eventPublisher;

    private final IosDeviceInfoService iosDeviceInfoService;

    private final FileUtil fileUtil;
    private static final int MAX_RETRIES_FOR_RECOVERY_CHECK = 60;

    private final InMemoryStore inMemoryStore;
    private static final int MAX_CONNECTION_CHECK = 30;

    private final Map<String, Process> deviceProcessMap = new ConcurrentHashMap<>();


    /**
     * This method fetch the device matching input ECID
     * We perform retries as it takes few seconds post restore
     * for the device to get detected by this command
     *
     * @param ecid : ECID of the device
     * @return IosDevice the device with matching ecid
     */
    public IosDevice getDeviceByEcidAfterRestore(final String ecid) throws Exception {
        IosDevice outputDevice = null;
        int attempt = 0;
        while (attempt < MAX_RETRIES_FOR_RECOVERY_CHECK) {
            ++attempt;
            Set<IosDevice> devices = iosDeviceConnectionService.getAllConnectedDevicesByEcid();
            if (!devices.isEmpty()) {
                outputDevice = devices.stream().filter(s ->
                        ecid.equals(s.getEcid())).findFirst().orElse(null);
            }
            if (outputDevice != null) {
                LOGGER.info("Device Found recovered :{}", outputDevice.getId());
                break;
            }
            Thread.sleep(1000);
        }
        return outputDevice;
    }

    /**
     * This method will run command to push device to recovery mode
     * It will make 3 attempt at max to push device into recovery mode
     * It also identifies if device is pushed to recovery or no
     * We are converting ecid id here to match it with the one we get
     * in recovery mode. This method should only be called when device is connected
     * in normal mode.
     *
     * @param deviceId     : device Id
     * @param serialNumber : serial number of the device
     * @param ecid         : ecid of the device
     * @return IosDevice
     */
    public IosDevice pushDeviceToRecoveryMode(final String deviceId,
                                              final String serialNumber,
                                              final String ecid) throws Exception {

        String output = executor.execute(new IosRecoveryCommand(deviceId));
        LOGGER.info("Output from IRecoveryCommand: {}", output);

        String ecidInRecovery = Long.toString(Long.parseLong(ecid), 16).toUpperCase();
        LOGGER.info("Using device serial: {} and ecid: {}, ecid in recovery mode: {}",
                serialNumber, ecid, ecidInRecovery);

        IosDevice outputDevice = null;
        int executionCount = 0;
        while (executionCount <= MAX_RETRIES_FOR_RECOVERY_CHECK) {
            Thread.sleep(2000);
            ++executionCount;
            outputDevice = getDeviceInRecovery(serialNumber, ecidInRecovery);
            LOGGER.info("Post execution of recovery command, is device found in recovery: {}", (outputDevice != null));
            if (outputDevice != null) {
                break;
            }
        }
        return outputDevice;
    }

    /**
     * This method will run command to list all the devices in recovery mode.
     *
     * @param serialNumber : device serial number
     * @param ecid         : device ecid
     * @return IosDevice if found in recovery mode otherwise null
     */
    public IosDevice getDeviceInRecovery(final String serialNumber,
                                         final String ecid) throws IOException {
        Set<IosDevice> devicesInDfuMode = iosDeviceConnectionService.getDevicesInDfuMode();
        if (devicesInDfuMode != null && !devicesInDfuMode.isEmpty()) {
            return devicesInDfuMode.stream()
                    .filter(s -> (StringUtils.isNotBlank(serialNumber) && serialNumber.equals(s.getSerial()))
                            || (StringUtils.isNotBlank(ecid) && ecid.equalsIgnoreCase(s.getEcid())))
                    .findFirst().orElse(null);
        }
        return null;
    }

    /**
     * This method will publish when restore success event
     *
     * @param device          device
     * @param responseCode    response code
     * @param isManualRestore is manually restore
     */
    public void publishSuccessRestoreResponseEvent(final IosDevice device,
                                                   final RestoreResponseCode responseCode,
                                                   final boolean isManualRestore) {
        publishRestoreResponseEvent(device, DeviceRestoreStatus.RESTORE_COMPLETED,
                responseCode, null, isManualRestore);
    }

    /**
     * This method will publish restore response event
     *
     * @param device       : device which needed to be pushed to recovery mode
     * @param state        : state of the device currently
     * @param responseCode : enum for the response message from the restore thread
     */
    public void publishRestoreResponseEvent(final IosDevice device,
                                            final DeviceRestoreStatus state,
                                            final RestoreResponseCode responseCode) {
        publishRestoreResponseEvent(device, state, responseCode, null);
    }

    public void publishRestoreResponseEvent(final IosDevice device,
                                            final DeviceRestoreStatus state,
                                            final RestoreResponseCode responseCode,
                                            final String errorCode) {
        publishRestoreResponseEvent(device, state, responseCode, errorCode, false);
    }


    public void publishRestoreResponseEvent(final IosDevice device,
                                            final DeviceRestoreStatus state,
                                            final RestoreResponseCode responseCode,
                                            final String errorCode,
                                            final boolean isManualRestore) {
        IosDeviceRestoreResponseEvent restoreResponseEvent = new IosDeviceRestoreResponseEvent(this,
                device, state, responseCode, 0L);
        if (StringUtils.isNotBlank(errorCode)) {
            restoreResponseEvent.setErrorCode(errorCode);
        }
        restoreResponseEvent.setManualRestore(isManualRestore);
        eventPublisher.publishEvent(restoreResponseEvent);
    }

    /**
     * This method will publish restore attempt to the response listener
     *
     * @param device       : device which needed to be pushed to recovery mode
     * @param state        : state of the device currently
     * @param responseCode : enum for the response message from the restore thread
     * @param attempt      : attempt no for restore
     */
    public void publishRestoreAttemptOnFailure(final IosDevice device,
                                               final DeviceRestoreStatus state,
                                               final RestoreResponseCode responseCode,
                                               final int attempt) {
        IosDeviceRestoreResponseEvent restoreResponseEvent = new IosDeviceRestoreResponseEvent(this,
                device, state, responseCode, 0L);
        restoreResponseEvent.setAttempt(attempt);
        eventPublisher.publishEvent(restoreResponseEvent);
    }

    /**
     * This method will get the firmware details for the device model
     *
     * @param productType : device product type
     * @return Firmware response mapping for the device model
     */
    public FirmwareModel.FirmwareResponse getFirmwareByProductType(final String productType) {
        FirmwareModel.FirmwareResponse firmwareResponse = null;
        Map<String, FirmwareModel.FirmwareResponse> firmwareResponseMap = inMemoryStore.getFirmwareModels();

        if (firmwareResponseMap != null && !firmwareResponseMap.isEmpty()) {
            LOGGER.info("Fetching firmware info for the productType: {}", productType);
            firmwareResponse = firmwareResponseMap.get(productType);
        }
        return firmwareResponse;
    }

    /**
     * This method will publish firmware event for the device
     *
     * @param device      : device model name
     * @param productType firmware file name
     */
    public void publishFirmwareDownloadEvent(final IosDevice device, final String productType) {
        LOGGER.info("Publishing firmware download event for the device.");
        FirmwareDownloadRequestEvent event = new FirmwareDownloadRequestEvent(this,
                productType, false, device.getId());
        eventPublisher.publishEvent(event);
    }

    /**
     * This method will check if device is connected
     *
     * @param deviceId : device id
     * @return true if restored device found in connected device list
     */
    public boolean isDeviceConnectedPostRestoreSuccess(final String deviceId) throws Exception {
        boolean isConnected = false;
        int attempt = 0;
        while (attempt < MAX_CONNECTION_CHECK && !isConnected) {
            ++attempt;
            Set<IosDevice> connectedDevices = iosDeviceConnectionService.getConnectedDevices();
            if (connectedDevices != null) {
                isConnected = connectedDevices.stream().anyMatch(s -> deviceId.equals(s.getId()));
            }
            Thread.sleep(2000);
        }
        LOGGER.info("Attempting to check if device is connected post restore. isConnected: {}", isConnected);
        return isConnected;
    }

    /**
     * This method will publish DeviceManualRestoreCustomAutomationEvent after restore
     * if Connection workflow steps are not empty
     *
     * @param device : IOS Device
     */
    public void publishConnectionAutomationAfterRestore(final IosDevice device) {
        if (device.getCurrentRunningAutomation() == AutomationWorkflow.CONNECTION) {
            LOGGER.info("Device was restored for connection automation, perform pending steps of the automation");
            eventPublisher.publishEvent(new DeviceConnectionAutomationEvent(this, device));
        }
    }

    /**
     * This method will publish DeviceCustomTriggerAutomation after restore
     *
     * @param device : IOS Device
     */
    public void publishManualRestoreAutomationAfterRestore(final IosDevice device) {
        LOGGER.info("Manual restore custom workflow automation triggered after restore successfully");
        eventPublisher.publishEvent(new DeviceManualRestoreCustomAutomationEvent(this, device));
    }

    /**
     * This method will publish DeviceManualPrintCustomAutomation after restore
     *
     * @param device : IOS Device
     */
    public void publishManualPrintAutomationAfterRestore(final IosDevice device) {
        LOGGER.info("Manual print custom workflow automation triggered, " +
                "perform pending steps of the automation");
        eventPublisher.publishEvent(new DeviceManualPrintCustomAutomationEvent(this, device));
    }


    /**
     * This method will copy all files from source SE folder to destination folder.
     * This method is called in restore failed scenario as for the next retry we
     * again need to copy all firmware files. Firmware files gets corrupted and hence
     * we copy firmware files before each run of restore.
     *
     * @param firmwareFile : firmware file
     */
    public void moveSeFolder(final File firmwareFile) {
        try {
            File backupSeFolder = new File(firmwareFile + "/SE");
            File orgSeFolderParent = new File(firmwareFile + "/Firmware/");
            File orgSeFolder = new File(firmwareFile + "/Firmware/SE");

            boolean missingFiles = false;
            if (orgSeFolder.exists() && backupSeFolder.exists()) {
                missingFiles = Objects.requireNonNull(orgSeFolder.list()).length <
                        Objects.requireNonNull(backupSeFolder.list()).length;
            }

            if (!missingFiles) {
                return;
            }

            if (backupSeFolder.exists()) {
                fileUtil.copyDirectoryToDirectory(backupSeFolder, orgSeFolderParent);
            }
        } catch (Exception e) {
            LOGGER.error("Error while moving files from firmware/SE dir to Firmware/Firmware/SE directory", e);
        }
    }


    /**
     * This method will copy all files from source SE folder to destination folder.
     * This method is called during each transition state in restore process.
     *
     * @param firmwareFile  : firmware file
     */
    public void moveSeFolderToTempDirectory(final File firmwareFile) {
        try {
            String backupSeFolderFilePath = firmwareFile + "/SE";
            String copyFolderDestinationPath = firmwareFile + "/Firmware/";
            String originalSeFolderFilePath = firmwareFile + "/Firmware/SE";
            File backupSeFile = new File(backupSeFolderFilePath);
            File originalSeFile = new File(originalSeFolderFilePath);
            File copyDestinationFile = new File(copyFolderDestinationPath);

            if (backupSeFile.exists()) {
                if (Files.list(originalSeFile.toPath()).count() < Files.list(backupSeFile.toPath()).count()) {
                    fileUtil.copyDirectoryToDirectory(backupSeFile, copyDestinationFile);
                    LOGGER.info("Files from /SE copied to /Firmware/SE");
                }
            }
        } catch (Exception e) {
            LOGGER.error("Error while copying files from /SE dir to /Firmware/SE dir", e);
        }
    }

    public void deleteTempFolderPostRestore(final File tempFolder) {
        LOGGER.info("Deleting folder after restore: {}", tempFolder);
        fileUtil.deleteDirectoryRecursively(tempFolder);
    }

    public String getDeviceProductVersion(final IosDevice device) throws IOException {
        return iosDeviceInfoService.getPropValueByKeyWithRetries(device, IosProperty.PRODUCT_VERSION);
    }

    public void addProcessToTheMap(final String ecid, final Process process) {
        LOGGER.info("Adding/Updating process for ecid:{} to the map", ecid);
        deviceProcessMap.put(ecid, process);
    }


    public void removeProcessForEcidFromTheMap(final String ecid) {
        if (StringUtils.isNotBlank(ecid)) {
            Process restoreProcess = deviceProcessMap.getOrDefault(ecid, null);
            if (restoreProcess != null && restoreProcess.isAlive()) {
                LOGGER.info("Killing restore process for the device with ECID : {}", ecid);
                restoreProcess.destroy();
            }
        }
    }

    /**
     * Delete the firwmare extracted folder for the device with give ecid and product type
     * @param ecid of the device
     * @param productType of the device
     */
    public void deleteExtractedFirmwareFolder(final String ecid, final String productType) {
        FirmwareModel.FirmwareResponse firmware = getFirmwareByProductType(productType);
        if (firmware != null) {
            final String firmwareExtractionFolderPath = new File(getIpswFile(firmware).getAbsolutePath()
                    .replace(".ipsw", "_".concat(ecid))).getAbsolutePath();
            final File extractedFolder = new File(firmwareExtractionFolderPath);
            LOGGER.info("Firmware extracted folder is determined as: {}", firmwareExtractionFolderPath);
            if (extractedFolder.exists()) {
                LOGGER.info("Deleting firmware extracted folder");
                fileUtil.deleteDirectoryRecursively(extractedFolder);
            }
        }
    }

    /**
     * Get ipsw file path
     *
     * @param firmware
     * @return file
     */
    private File getIpswFile(final FirmwareModel.FirmwareResponse firmware) {
        final File firmwarePath = new File(inMemoryStore.getFirmwareDownloadPath());
        LOGGER.info("Firmware path for ipsw files: {}", firmwarePath);
        return new File(firmwarePath.getAbsolutePath(), firmware.getFileName());
    }
}

