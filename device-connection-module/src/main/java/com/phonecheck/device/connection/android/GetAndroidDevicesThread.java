package com.phonecheck.device.connection.android;

import com.phonecheck.device.connection.manager.DeviceStageManager;
import com.phonecheck.info.android.AndroidDeviceInfoService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidConnectedEvent;
import com.phonecheck.model.event.device.android.AndroidDisconnectedEvent;
import com.phonecheck.model.event.device.android.AndroidEraseSuccessEvent;
import com.phonecheck.model.event.device.ios.IosDisconnectedEvent;
import com.phonecheck.model.event.port.PortMapDeviceConnectionEvent;
import com.phonecheck.model.event.port.PortMapUpdateRequestEvent;
import com.phonecheck.model.status.AuthorizationStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.port.PortMappingService;
import com.phonecheck.syslog.SysLogServiceManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

import static com.phonecheck.info.android.AndroidDeviceInfoService.AT_UN_KEY;

/**
 * Polls adb devices for android device connections
 */
@Component
public class GetAndroidDevicesThread extends Thread {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetAndroidDevicesThread.class);

    private final Set<AndroidDevice> previouslyConnectedDevices = Collections.synchronizedSet(new HashSet<>());
    private final AndroidDeviceConnectionService androidDeviceConnectionService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final DeviceStageManager deviceStageManager;
    private final ApplicationEventPublisher eventPublisher;
    private final PortMappingService portMappingService;
    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final SysLogServiceManager sysLogServiceManager;

    private boolean isMappingAvailable;

    private boolean keepGoing = true;
    private boolean shouldPauseDeviceProcessing = false;

    public GetAndroidDevicesThread(final AndroidDeviceConnectionService androidDeviceConnectionService,
                                   final DeviceConnectionTracker deviceConnectionTracker,
                                   final InMemoryStore inMemoryStore,
                                   final DeviceStageManager deviceStageManager,
                                   final ApplicationEventPublisher eventPublisher,
                                   final PortMappingService portMappingService,
                                   final AndroidDeviceInfoService androidDeviceInfoService,
                                   final SysLogServiceManager sysLogServiceManager) {
        this.androidDeviceConnectionService = androidDeviceConnectionService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.deviceStageManager = deviceStageManager;
        this.eventPublisher = eventPublisher;
        this.portMappingService = portMappingService;
        this.androidDeviceInfoService = androidDeviceInfoService;
        this.isMappingAvailable = portMappingService.isMappingAvailable();
        this.sysLogServiceManager = sysLogServiceManager;
    }

    @Override
    public void run() {
        shouldPauseDeviceProcessing = false;
        while (keepGoing) {
            if (shouldPauseDeviceProcessing) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    // do nothing
                }
                continue;
            }
            checkDevices();
        }
    }

    public void checkDevices() {
        try {
            handlePortMapReInitialization();

            final Set<AndroidDevice> currentDevices = androidDeviceConnectionService.getConnectedDevices();
            final Set<AndroidDevice> newlyConnectedDevices = findNewlyConnectedDevices(currentDevices);
            final Set<AndroidDevice> disconnectedDevices = findDisconnectedDevices(currentDevices);

            // Check whether devices marked as 'disconnected' are actually required to be disconnected
            handleDisconnectedDevices(disconnectedDevices, newlyConnectedDevices);

            // Publish device connected/disconnected events
            publishDeviceConnectedEvents(newlyConnectedDevices);

            // Reset previous devices with current devices
            previouslyConnectedDevices.clear();
            previouslyConnectedDevices.addAll(currentDevices);
        } catch (Throwable e) {
            LOGGER.error("Could not get connected devices", e);
        } finally {
            try {
                Thread.sleep(1100);
            } catch (InterruptedException e) {
                // We don't really care if this thread was interrupted
            }
        }
    }

    /**
     * Handles the port map reinitialization to automatically process the connected device
     */
    private void handlePortMapReInitialization() {
        isMappingAvailable = portMappingService.isMappingAvailable();
        if (inMemoryStore.isPortMapReInitialized()) {
            previouslyConnectedDevices.clear();

            // If PortMap reinitialized auto process
            // device as newly connected device
            deviceConnectionTracker.clearConnectedDevices();
            inMemoryStore.setPortMapReInitialized(false);
        }
    }

    /**
     * Method to find the newly connected devices
     *
     * @param currentDevices currently connected devices
     * @return set of newly connected ios devices
     */
    private Set<AndroidDevice> findNewlyConnectedDevices(final Set<AndroidDevice> currentDevices) {
        // newly connected devices = current devices - previous devices
        Set<AndroidDevice> newlyConnectedDevices = new HashSet<>(currentDevices);
        newlyConnectedDevices.removeAll(previouslyConnectedDevices);

        if (!newlyConnectedDevices.isEmpty()) {
            LOGGER.info("Newly connected devices:");
            newlyConnectedDevices.forEach(device -> {

                LOGGER.info("Device: {}, Mode: {}, Auth Status: {}",
                        device.getId(), device.getAndroidConnectionMode(),
                        device.getAuthorizationStatus());

                if (AndroidConnectionMode.AT.equals(device.getAndroidConnectionMode())) {
                    LOGGER.info("AT Device Port Name: {}, Serial Number: {}",
                            device.getAtPort().getAPort().getPortName(),
                            device.getSerial());
                }
            });
        }

        return newlyConnectedDevices;
    }

    /**
     * Method to find recently disconnected devices
     *
     * @param currentDevices currently connected devices
     * @return set of newly connected ios devices
     */
    private Set<AndroidDevice> findDisconnectedDevices(final Set<AndroidDevice> currentDevices) {
        // disconnected devices = previous devices - current devices
        Set<AndroidDevice> disconnectedDevices = new HashSet<>(previouslyConnectedDevices);
        disconnectedDevices.removeAll(currentDevices);
        return disconnectedDevices;
    }

    /**
     * Wait for 5 seconds before disconnecting a device as it can be because of faulty cable
     * After 5 seconds delay, if device is connected back, then don't consider device as disconnected
     * But add them to connected devices. Reconnected devices will be processed from last known stage
     * Also, Don't consider devices as disconnected if their stages are *_IN_PROGRESS
     *
     * @param disconnectedDevices   set of devices that got disconnected from the machine
     * @param newlyConnectedDevices set of device that are newly connected to the machine
     */
    private void handleDisconnectedDevices(final Set<AndroidDevice> disconnectedDevices,
                                           final Set<AndroidDevice> newlyConnectedDevices) {
        if (DeviceConnectionMode.PROCESS.equals(inMemoryStore.getDeviceConnectionMode())) {
            Set<AndroidDevice> scheduledToDisconnect = Set.copyOf(disconnectedDevices);

            scheduledToDisconnect.forEach(device -> {
                if (AndroidConnectionMode.AT.equals(device.getAndroidConnectionMode())
                        || AuthorizationStatus.AUTHORIZED.equals(device.getAuthorizationStatus())) {
                    scheduleDisconnectionTimerForDevice(device, newlyConnectedDevices, disconnectedDevices);
                } else {
                    // if unauthenticated or offline mode device disconnects then don't have to wait 5 seconds.
                    Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
                    if (deviceInTracker != null && !DeviceStage.ERASE_SUCCESS.equals(deviceInTracker.getStage())) {
                        publishDeviceDisconnectedEvents(disconnectedDevices);
                    }
                }
            });
        }
    }

    /**
     * If a device is disconnected when its in MUX mode, we will schedule a 5 seconds timer for disconnection.
     * If devices gets reconnected within 5 seconds, we cancel the timer.
     *
     * @param device                device potentially disconnected
     * @param newlyConnectedDevices set of newly connected devices
     * @param disconnectedDevices   set of disconnected devices
     */
    private void scheduleDisconnectionTimerForDevice(final AndroidDevice device,
                                                     final Set<AndroidDevice> newlyConnectedDevices,
                                                     final Set<AndroidDevice> disconnectedDevices) {
        final Timer disconnectionTimer = new Timer();
        disconnectionTimer.scheduleAtFixedRate(new TimerTask() {
            // the device can get reconnected within 5 seconds to resume processing
            // if not it will get disconnected from the system
            private int timeout = 3; // seconds. Timeout will happen on the 3rd second

            @Override
            public void run() {
                if (timeout > 0) {
                    timeout--;
                    LOGGER.info("Checking if device id: {} got re-connected...", device.getId());

                    // every second check if device got reconnected
                    AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
                    if (deviceInTracker != null) {
                        if (deviceInTracker.isReconnectRequired()) {
                            publishDeviceDisconnectedEvents(disconnectedDevices);
                            //cancel the task once it is completed
                            disconnectionTimer.cancel();
                        } else if (deviceInTracker.isEraseInProgress()
                                || deviceInTracker.isRestartingForUnlockingOrPrepare()
                                || DeviceStage.ERASE_IN_PROGRESS.equals(deviceInTracker.getStage())
                                || DeviceStage.ERASE_SUCCESS.equals(deviceInTracker.getStage())) {
                            disconnectedDevices.remove(device);
                            // stop syslog service on device as it will attempt to restart syslog if there is no
                            // response
                            sysLogServiceManager.stopSysLogServiceForDevice(device);

                            // if a device is erased and hence getting disconnected then show erase success
                            synchronized (deviceInTracker) {
                                if (!deviceInTracker.isRestartingForUnlockingOrPrepare()) {
                                    notifyEraseSuccess(deviceInTracker);
                                }
                            }
                            LOGGER.info("Device id: {} is getting restarted for processing...", device.getId());
                            // since the device is being processed we don't have to continue the timer
                            disconnectionTimer.cancel();
                        } else {
                            try {
                                // Check connected device again to see if there was an intermittent disconnection
                                final Set<AndroidDevice> newCurrentDevices =
                                        androidDeviceConnectionService.getConnectedDevices();
                                // Remove devices with intermittent disconnection issue from disconnectedDevices
                                // and add them back to connected devices and previous devices
                                boolean isDeviceConnectedAgain = newCurrentDevices.stream()
                                        .anyMatch(iosDevice -> Objects.equals(iosDevice.getId(), device.getId()));
                                if (isDeviceConnectedAgain
                                        && !DeviceStage.DISCONNECTED.equals(deviceInTracker.getStage())) {

                                    Integer previousPortNo = null;
                                    Integer newPortNo = null;
                                    if (isMappingAvailable) {
                                        previousPortNo = deviceInTracker.getPortNumber();
                                        newPortNo = portMappingService.getMappedPort(device.getId());
                                    }

                                    if (!isMappingAvailable || (Objects.equals(previousPortNo, newPortNo))) {
                                        disconnectedDevices.remove(device);
                                        newlyConnectedDevices.add(device);
                                        previouslyConnectedDevices.add(device);
                                        LOGGER.info("Device id: {} got re-connected...", device.getId());
                                        // since the device is reconnected we don't have to continue the timer
                                        disconnectionTimer.cancel();
                                    } else {
                                        // device got reconnected on a different port
                                        LOGGER.info("Device id: {} got re-connected but on a different port, " +
                                                        "restart processing",
                                                device.getId());
                                        publishSingleDisconnectedEvent(device);
                                        Thread.sleep(1000);
                                        publishSingleConnectedEvent(device);
                                        previouslyConnectedDevices.add(device);
                                        disconnectionTimer.cancel();
                                    }
                                }
                            } catch (IOException | InterruptedException e) {
                                LOGGER.error("Error occurred while getting connected devices", e);
                            }
                        }
                    } else {
                        LOGGER.warn("Device id: {} not found in the tracker; disconnecting.", device.getId());
                        publishDeviceDisconnectedEvents(disconnectedDevices);
                        //cancel the task once it is completed
                        disconnectionTimer.cancel();
                    }
                } else {
                    publishDeviceDisconnectedEvents(disconnectedDevices);
                    //cancel the task once it is completed
                    disconnectionTimer.cancel();
                }
            }
        }, new Date(), 1000);
    }

    /**
     * notify erase success
     *
     * @param device AndroidDevice
     */
    private void notifyEraseSuccess(final AndroidDevice device) {
        if (AndroidConnectionMode.ADB.equals(device.getAndroidConnectionMode())
                && !Boolean.TRUE.equals(device.getIsErasePerformed())) {
            device.setIsErasePerformed(Boolean.TRUE);
            LOGGER.info("Completed erase, raising erase success event");
            eventPublisher.publishEvent(new AndroidEraseSuccessEvent(this,
                    device));
        }
    }

    /**
     * Publish disconnected event for devices that have been disconnected from the machine.
     *
     * @param disconnectedDevices disconnected devices
     */
    private void publishDeviceDisconnectedEvents(final Set<AndroidDevice> disconnectedDevices) {
        // Publish DeviceDisconnectedEvent for newly disconnected devices
        for (AndroidDevice device : disconnectedDevices) {
            publishSingleDisconnectedEvent(device);
        }
    }

    /**
     * Publish a single device disconnected event
     *
     * @param device device
     */
    private void publishSingleDisconnectedEvent(final AndroidDevice device) {
        if (!isMappingAvailable || deviceConnectionTracker.getDevice(device.getId()) != null) {
            LOGGER.debug("Device disconnected: {}", device.getId());
            eventPublisher.publishEvent(new AndroidDisconnectedEvent(this, device));
        } else {
            LOGGER.warn("No device found in Tracker for id: {}", device.getId());
        }
    }

    /**
     * Publish DeviceConnectedEvent for newly Connected devices
     * Or continue processing from last known stage for reconnected devices
     *
     * @param connectedDevices connected devices
     */
    private void publishDeviceConnectedEvents(final Set<AndroidDevice> connectedDevices) {
        for (AndroidDevice device : connectedDevices) {
            if (DeviceConnectionMode.PORT_MAP.equals(inMemoryStore.getDeviceConnectionMode())) {
                eventPublisher.publishEvent(new PortMapDeviceConnectionEvent(this, device));
            } else {
                publishSingleConnectedEvent(device);
            }
        }
    }

    /**
     * Publish single device connected event
     *
     * @param device iphone
     */
    private void publishSingleConnectedEvent(final AndroidDevice device) {
        int mappedPort = getAndroidDeviceMappedPort(device);

        AndroidDevice deviceInTracker = (AndroidDevice) deviceConnectionTracker.getDevice(device.getId());
        LOGGER.info("Connected Device: {} Mapped port: {} Mapping Available: {} deviceInTrackerIsNull: {}",
                device.getId(), (mappedPort != -1 ? mappedPort + 1 : mappedPort), isMappingAvailable,
                deviceInTracker == null);

        if (!isMappingAvailable || mappedPort != -1) {

            if (deviceInTracker != null && deviceInTracker.getStage() != null) {
                if (deviceInTracker.getPortName() == null) {
                    deviceInTracker.setPortName(StringUtils.isNotBlank(device.getPortName()) ?
                            device.getPortName() : null);
                }
                if (!deviceInTracker.isRestartingForUnlockingOrPrepare()) {
                    // If device is found in existing device, that means it didn't disconnect.
                    // So, the process should continue from tha last known stage
                    LOGGER.info("Resuming processing for device id: {} from stage: {}", deviceInTracker.getId(),
                            deviceInTracker.getStage());

                    new Thread(() -> deviceStageManager.raiseEventForDeviceInProgress(deviceInTracker)).start();
                } else {
                    LOGGER.info("Device: {} is not restarted for network unlocking", deviceInTracker.getId());
                }
            } else {
                LOGGER.info("New Device detected id: {}", device.getId());
                device.setPortNumber(mappedPort);
                // Remove previously connected device from connected device map if port mapping is available
                if (portMappingService.isMappingAvailable()) {
                    removePreviouslyConnectedDeviceOnPort(device.getPortNumber(), device.getId());
                }
                eventPublisher.publishEvent(new AndroidConnectedEvent(this, device));
            }
        } else {
            try {
                boolean deviceConnected = androidDeviceConnectionService.getConnectedDevices()
                        .stream()
                        .anyMatch(connectedDevice -> connectedDevice.getId().equalsIgnoreCase(device.getId()));
                LOGGER.warn("Port number for device {} cannot be retrieved, and device connected {}",
                        device.getId(), deviceConnected);
                if (deviceConnected && deviceInTracker == null) {
                    eventPublisher.publishEvent(new PortMapUpdateRequestEvent(this));
                }
            } catch (Exception e) {
                LOGGER.error("Failed to check port number for device {} due to an error.", device.getId(), e);
            }
        }
    }

    /**
     * Retrieves mapped port for the provided device
     *
     * @param device target device
     * @return mapped port number
     */
    private int getAndroidDeviceMappedPort(final AndroidDevice device) {
        int mappedPort = portMappingService.getMappedPort(device.getId());

        if (mappedPort != -1 || !AndroidConnectionMode.AT.equals(device.getAndroidConnectionMode())) {
            return mappedPort;
        }

        String deviceId;

        // Try getting AT device id using unique number
        if (device.getUniqueNumber() == null) {
            try {
                Map<String, String> props = androidDeviceInfoService.getAtDeviceInfo(device.getPortName());
                if (props != null && props.get(AT_UN_KEY) != null) {
                    device.setUniqueNumber(props.get(AT_UN_KEY).toLowerCase());
                }
            } catch (Exception e) {
                LOGGER.error("Exception occurred while loading device info for AT device", e);
            }
        }

        if (device.getUniqueNumber() != null) {
            deviceId = device.getUniqueNumber();
            mappedPort = portMappingService.getMappedPort(deviceId);
            if (mappedPort != -1) {
                device.setId(deviceId);
                return mappedPort;
            }

            // Fallback: derive device ID from pattern
            try {
                deviceId = device.getUniqueNumber()
                        .replaceFirst("^[^a-zA-Z]*[a-zA-Z]+", "") + deviceId.substring(0, 2);
                mappedPort = portMappingService.getMappedPort(deviceId);
                if (mappedPort != -1) {
                    device.setId(deviceId);
                }
            } catch (Exception e) {
                LOGGER.error("Failed to derive fallback device ID from unique number: {}",
                        device.getUniqueNumber(), e);
            }
        }

        return mappedPort;
    }

    /**
     * Retrieves a connected device by its id
     *
     * @param udid device id
     * @return device associated with `udid` or `null` if no match is found
     */
    public AndroidDevice getDevice(final String udid) {
        synchronized (previouslyConnectedDevices) {
            for (AndroidDevice device : previouslyConnectedDevices) {
                if (device.getId().equalsIgnoreCase(udid)) {
                    return device;
                }
            }
            return null;
        }
    }

    /**
     * Stop the device processing thread
     */
    public void quit() {
        keepGoing = false;
    }

    /**
     * Resume device processing
     */
    public void resumeDeviceProcessing() {
        shouldPauseDeviceProcessing = false;
    }

    /**
     * Pause device processing
     */
    public void pauseDeviceProcessing() {
        shouldPauseDeviceProcessing = true;
    }

    /**
     * Removes a previously connected device from the connected device map
     * if a new device is connected to the same port.
     *
     * @param newPortNo new port number.
     * @param deviceId  device identifier.
     */
    public void removePreviouslyConnectedDeviceOnPort(final int newPortNo, final String deviceId) {
        Map<String, Device> connectedDevices = deviceConnectionTracker.getConnectedDevices();
        for (Map.Entry<String, Device> entry : connectedDevices.entrySet()) {
            Device connectedDevice = entry.getValue();
            if (newPortNo == connectedDevice.getPortNumber()
                    && !connectedDevice.getId().equalsIgnoreCase(deviceId)
                    && connectedDevice instanceof AndroidDevice androidDevice) {
                LOGGER.warn("Found device: {} still connected on the same port: {} as the new device: {}," +
                        " will attempt to remove it", connectedDevice.getId(), newPortNo, deviceId);
                // if a device was replaced on a port where erase or restore was in progress, then
                // set the manual release flag to true, so we can remove the old device from our system
                final AndroidDisconnectedEvent event = new AndroidDisconnectedEvent(this, androidDevice);
                event.setManualRelease(true);
                eventPublisher.publishEvent(event);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    // nothing to do
                }
                break;
            } else if (newPortNo == connectedDevice.getPortNumber()
                    && !connectedDevice.getId().equalsIgnoreCase(deviceId)
                    && connectedDevice instanceof IosDevice iosDevice) {
                LOGGER.warn("Found device: {} still connected on the same port: {} as the new device: {}," +
                        " will attempt to remove it", connectedDevice.getId(), newPortNo, deviceId);
                // if a device was replaced on a port where erase or restore was in progress, then
                // set the manual release flag to true, so we can remove the old device from our system
                final IosDisconnectedEvent event = new IosDisconnectedEvent(this, iosDevice);
                event.setManualRelease(true);
                eventPublisher.publishEvent(event);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    // nothing to do
                }
                break;
            }

        }
    }
}
