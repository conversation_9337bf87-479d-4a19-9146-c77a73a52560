package com.phonecheck.device.connection.android;

import com.phonecheck.command.device.android.connection.AndroidAdbDevicesCommand;
import com.phonecheck.command.device.android.connection.AndroidAtDevicesCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.android.AndroidDeviceInfoService;
import com.phonecheck.info.android.AndroidSerialInfoService;
import com.phonecheck.model.android.AndroidConnectionMode;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.AtPort;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.status.AuthorizationStatus;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.device.android.connection.AndroidAdbDevicesListParser;
import com.phonecheck.parser.device.android.connection.AndroidAtDevicesListParser;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
@AllArgsConstructor
public class AndroidDeviceConnectionService {
    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidDeviceConnectionService.class);

    private final OsChecker osChecker;
    private final CommandExecutor executor;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final AndroidSerialInfoService androidSerialInfoService;
    private final AndroidDeviceInfoService androidDeviceInfoService;
    private final AndroidAdbDevicesListParser androidAdbDevicesListParser;
    private final AndroidAtDevicesListParser androidAtDevicesListParser;

    /**
     * Creates an {@link AndroidDevice} for each currently-connected android device
     * uses AT-Command to detect connected devices
     *
     * @return all currently-connected devices
     * @throws IOException when the output couldn't be read
     */
    public Set<AndroidDevice> getConnectedDevices() throws IOException {
        Set<AndroidDevice> connectedAtDevices = getConnectedDevicesThroughAt();
        Set<AndroidDevice> connectedDevices = getConnectedDevicesThroughAdb();

        Set<AndroidDevice> devicesToRemove = new HashSet<>();

        if (connectedAtDevices != null
                && !connectedAtDevices.isEmpty()) {
            if (connectedDevices != null
                    && !connectedDevices.isEmpty()) {
                for (AndroidDevice atDevice : connectedAtDevices) {
                    if (connectedDevices.contains(atDevice)) {
                        // if device is in adb mode and at mode, remove it from the AT mode
                        devicesToRemove.add(atDevice);
                    }
                }
            } else {
                connectedDevices = new HashSet<>();
            }
        }

        if (connectedAtDevices != null && !connectedAtDevices.isEmpty()) {
            connectedAtDevices.removeAll(devicesToRemove);
            connectedDevices.addAll(connectedAtDevices);
        }

        return connectedDevices;
    }

    /**
     * Creates an {@link AndroidDevice} for each currently-connected android device
     * uses ADB to detect connected devices
     *
     * @return all currently-connected devices
     * @throws IOException when the output couldn't be read
     */
    public Set<AndroidDevice> getConnectedDevicesThroughAdb() throws IOException {
        String output = executor.execute(new AndroidAdbDevicesCommand());
        if (output != null) {
            // Parse devices from output
            Set<AndroidDevice> androidDevices = androidAdbDevicesListParser.parse(output);
            // Get and set serial number for all the devices
            for (AndroidDevice device : androidDevices) {
                device.setSerial(androidSerialInfoService.getDeviceSerial(device));
            }

            return androidDevices;
        } else {
            return null;
        }
    }

    /**
     * Creates an {@link AndroidDevice} for each currently-connected android device
     * uses AT to detect connected devices
     *
     * @return all currently-connected AT devices
     * @throws IOException when the output couldn't be read
     */
    public Set<AndroidDevice> getConnectedDevicesThroughAt() throws IOException {
        String output = executor.execute(new AndroidAtDevicesCommand());
        if (StringUtils.isNotBlank(output)) {
            String sanitizedOutput = output.replace("\\", "\\\\");

            final Set<AndroidDevice> devices = new HashSet<>();

            // Parse connected AT/COM ports from output
            List<AtPort> atPorts = androidAtDevicesListParser.parse(sanitizedOutput);
            for (AtPort atPort : atPorts) {
                AndroidDevice device = new AndroidDevice();
                device.setAuthorizationStatus(AuthorizationStatus.NA);
                device.setAndroidConnectionMode(AndroidConnectionMode.AT);

                device.setSerial(atPort.getAPort().getSerialNumber());

                AndroidDevice deviceInTracker;
                if (osChecker.isMac()) {
                    device.setId(atPort.getAPort().getSerialNumber());
                } else {
                    deviceInTracker = (AndroidDevice) deviceConnectionTracker
                            .getDeviceByAtId(
                                    atPort.getAPort().getDeviceID()
                            );

                    if (deviceInTracker == null || StringUtils.isBlank(deviceInTracker.getId())) {
                        try {
                            final String deviceId = androidDeviceInfoService
                                    .getAtDeviceIdOnWindows(atPort.getAPort().getDeviceID());

                            if (StringUtils.isNotBlank(deviceId)) {
                                device.setId(deviceId);
                            }
                        } catch (Exception e) {
                            LOGGER.error("Exception occurred while retrieving AT device serial", e);
                        }
                    } else {
                        device = deviceInTracker;
                    }
                }

                if (StringUtils.isNotBlank(device.getId())) {
                    // Only set AtPort in the device if null it can be not null if device is present in tracker
                    if (device.getAtPort() == null) {
                        device.setAtPort(atPort);
                        device.setPortName(atPort.getAPort().getPortName());
                    }

                    devices.add(device);
                }
            }

            return devices;
        } else {
            return null;
        }
    }

    /**
     * retrieves the authorization status of a connected device
     *
     * @param deviceIdToFind device id
     * @return AuthorizationStatus
     */
    public AuthorizationStatus getConnectedDeviceAuthorization(final String deviceIdToFind) {
        try {
            Set<AndroidDevice> connectedDevices = getConnectedDevicesThroughAdb();
            for (AndroidDevice device : connectedDevices) {
                if (device.getId().equals(deviceIdToFind)) {
                    return device.getAuthorizationStatus();
                }
            }
        } catch (IOException e) {
            LOGGER.error("Could not find the connected device", e);
        }
        return AuthorizationStatus.DEVICE_DISCONNECTED;
    }
}
