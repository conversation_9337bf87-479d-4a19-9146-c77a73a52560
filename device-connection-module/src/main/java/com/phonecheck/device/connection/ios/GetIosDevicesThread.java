package com.phonecheck.device.connection.ios;

import com.phonecheck.device.connection.manager.DeviceStageManager;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.stage.DeviceState;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.android.AndroidDisconnectedEvent;
import com.phonecheck.model.event.device.ios.IosConnectedEvent;
import com.phonecheck.model.event.device.ios.IosDisconnectedEvent;
import com.phonecheck.model.event.port.PortMapDeviceConnectionEvent;
import com.phonecheck.model.event.port.PortMapUpdateRequestEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.port.PortMappingService;
import com.phonecheck.syslog.SysLogServiceManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Polls idevice_usb for iOS device connections
 */
@Component
public class GetIosDevicesThread extends Thread {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetIosDevicesThread.class);
    private final Set<IosDevice> previouslyConnectedDevices = Collections.synchronizedSet(new HashSet<>());
    private final IosDeviceConnectionService iosDeviceConnectionService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final DeviceStageManager deviceStageManager;
    private final ApplicationEventPublisher eventPublisher;
    private final PortMappingService portMappingService;
    private final SysLogServiceManager sysLogServiceManager;
    private boolean keepGoing = true;
    private boolean shouldPauseDeviceProcessing = false;
    private boolean isMappingAvailable;

    private static final String GARBAGE_ID_PATTERN = "ffffffffff";

    public GetIosDevicesThread(final IosDeviceConnectionService iosDeviceConnectionService,
                               final DeviceConnectionTracker deviceConnectionTracker,
                               final InMemoryStore inMemoryStore,
                               final DeviceStageManager deviceStageManager,
                               final ApplicationEventPublisher eventPublisher,
                               final PortMappingService portMappingService,
                               final SysLogServiceManager sysLogServiceManager) {
        this.iosDeviceConnectionService = iosDeviceConnectionService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.deviceStageManager = deviceStageManager;
        this.eventPublisher = eventPublisher;
        this.portMappingService = portMappingService;
        this.isMappingAvailable = portMappingService.isMappingAvailable();
        this.sysLogServiceManager = sysLogServiceManager;
    }

    @Override
    public void run() {
        shouldPauseDeviceProcessing = false;
        while (keepGoing) {
            if (shouldPauseDeviceProcessing) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    // do nothing
                }
                continue;
            }
            checkDevices();
        }
    }

    public void checkDevices() {
        try {
            handlePortMapReInitialization();

            Set<IosDevice> currentDevices = iosDeviceConnectionService.getConnectedDevices();

            Set<IosDevice> dfuDevices = currentDevices.stream().filter(IosDevice::isRecoveryMode)
                    .collect(Collectors.toSet());

            currentDevices = currentDevices.stream()
                    .filter(s -> StringUtils.isNotBlank(s.getId()) && !s.getId().startsWith(GARBAGE_ID_PATTERN))
                    .collect(Collectors.toSet());

            updateIdForRecoveryModeDevices(dfuDevices);

            final Set<IosDevice> newlyConnectedDevices = findNewlyConnectedDevices(currentDevices);
            final Set<IosDevice> disconnectedDevices = findDisconnectedDevices(currentDevices);

            // Check whether devices marked as 'disconnected' are actually required to be disconnected
            handleDisconnectedDevices(disconnectedDevices, newlyConnectedDevices);

            // Publish device connected/disconnected events
            publishDeviceConnectedEvents(newlyConnectedDevices);

            // Reset previous devices with current devices
            previouslyConnectedDevices.clear(); //id, serial
            previouslyConnectedDevices.addAll(currentDevices);
        } catch (Throwable e) {
            LOGGER.error("Could not get connected devices", e);
        } finally {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                // We don't really care if this thread was interrupted
            }
        }
    }

    /**
     * Method will update device id from tracker if it was already existing device
     * otherwise it will raise restore event for the device
     *
     * @param dfuDevices currently connected devices in DFU mode
     */
    private void updateIdForRecoveryModeDevices(final Set<IosDevice> dfuDevices) {
        for (IosDevice device : dfuDevices) {
            LOGGER.info("Recovery id of device: {}", device.getIdInRecoveryMode());
            String ecidNormalForm = null;
            try {
                ecidNormalForm = Long.valueOf(device.getEcid(), 16).toString();
            } catch (Exception e) {
                LOGGER.error("Error while converting ECID of recovery mode : {}", device.getEcid());
            }
            IosDevice deviceInTracker = StringUtils.isNotEmpty(ecidNormalForm) ?
                    (IosDevice) deviceConnectionTracker.getDeviceByEcid(ecidNormalForm) :
                    (IosDevice) deviceConnectionTracker.getDeviceBySerial(device.getSerial());

            if (deviceInTracker != null) {
                LOGGER.info("Device found in tracker with Ecid:{} and serial:{}",
                        deviceInTracker.getEcid(), deviceInTracker.getSerial());
                device.setId(deviceInTracker.getId());
                device.setStage(deviceInTracker.getStage());
                device.setSerial(deviceInTracker.getSerial());
            } else {
                device.setConnectedInRecovery(true);
                device.setDeviceState(DeviceState.DFU);
                device.setStage(DeviceStage.RESTORE_NEEDED);
            }
        }
    }

    /**
     * Handles the port map reinitialization to automatically process the connected device
     */
    private void handlePortMapReInitialization() {
        isMappingAvailable = portMappingService.isMappingAvailable();
        if (inMemoryStore.isPortMapReInitialized()) {
            previouslyConnectedDevices.clear();

            // If PortMap reinitialized auto process
            // device as newly connected device
            deviceConnectionTracker.clearConnectedDevices();
            inMemoryStore.setPortMapReInitialized(false);
        }
    }

    /**
     * Method to find the newly connected devices
     *
     * @param currentDevices currently connected devices
     * @return set of newly connected ios devices
     */
    private Set<IosDevice> findNewlyConnectedDevices(final Set<IosDevice> currentDevices) {
        // newly connected devices = all the current devices that are not in previously connected devices
        Set<IosDevice> newlyConnectedDevices = new HashSet<>();

        for (IosDevice device : currentDevices) {
            // checking if the current device is in previous devices list
            Optional<IosDevice> iosDevice = previouslyConnectedDevices.stream()
                    .filter(previousDevice ->
                            StringUtils.isNotBlank(previousDevice.getId())
                                    && previousDevice.getId().equals(device.getId()))
                    .findFirst();

            if (iosDevice.isEmpty()) {
                // device is not in the previous devices list, so considering it's a new device
                newlyConnectedDevices.add(device);
            } else {
                // device was previously connected let's update the old device connection state in tracker
                IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
                if (deviceInTracker != null
                        && deviceInTracker.isUsbMode() != device.isUsbMode()) {
                    LOGGER.info("Ios device {} connection state changed to: {}",
                            device.getId(), device.isUsbMode() ? "USB" : "MUX");
                    deviceInTracker.setUsbMode(device.isUsbMode());
                }
            }
        }

        StringBuilder stringBuilder = new StringBuilder();
        newlyConnectedDevices.forEach(device
                -> stringBuilder.append(device.getId()).append(" ")
                .append(device.isUsbMode() ? "USB" : "MUX").append("\n"));

        if (!stringBuilder.isEmpty()) {
            LOGGER.info("Newly connected devices:\n{}", stringBuilder);
        }
        return newlyConnectedDevices;
    }

    /**
     * Method to find recently disconnected devices
     *
     * @param currentDevices currently connected devices
     * @return set of newly connected ios devices
     */
    private Set<IosDevice> findDisconnectedDevices(final Set<IosDevice> currentDevices) {
        // disconnected devices = previous devices - current devices
        Set<IosDevice> disconnectedDevices = new HashSet<>(previouslyConnectedDevices);
        disconnectedDevices.removeAll(currentDevices);
        return disconnectedDevices;
    }

    /**
     * Wait for 5 seconds before disconnecting a device as it can be because of faulty cable
     * After 5 seconds delay, if device is connected back, then don't consider device as disconnected
     * But add them to connected devices. Reconnected devices will be processed from last known stage
     * Also, Don't consider devices as disconnected if their stages are *_IN_PROGRESS
     * Do not consider devices if stage is RESTORE_IN_PROGRESS/RESTORE_FAILED
     *
     * @param disconnectedDevices   set of devices that got disconnected from the machine
     * @param newlyConnectedDevices set of device that are newly connected to the machine
     */
    private void handleDisconnectedDevices(final Set<IosDevice> disconnectedDevices,
                                           final Set<IosDevice> newlyConnectedDevices) {
        if (DeviceConnectionMode.PROCESS.equals(inMemoryStore.getDeviceConnectionMode()) ||
                DeviceConnectionMode.PRE_CHECK.equals(inMemoryStore.getDeviceConnectionMode())) {
            Set<IosDevice> scheduledToDisconnect = Set.copyOf(disconnectedDevices);
            scheduledToDisconnect.forEach(device ->
                    scheduleDisconnectionTimerForDevice(device, newlyConnectedDevices, disconnectedDevices));
        }
    }

    /**
     * If a device is disconnected when its in MUX mode, we will schedule a 2 seconds timer for disconnection.
     * If devices gets reconnected within 2 seconds, we cancel the timer.
     *
     * @param device                device potentially disconnected
     * @param newlyConnectedDevices set of newly connected devices
     * @param disconnectedDevices   set of disconnected devices
     */
    private void scheduleDisconnectionTimerForDevice(final IosDevice device,
                                                     final Set<IosDevice> newlyConnectedDevices,
                                                     final Set<IosDevice> disconnectedDevices) {
        final Timer disconnectionTimer = new Timer();
        disconnectionTimer.scheduleAtFixedRate(new TimerTask() {
            // the device can get reconnected within 3 seconds to resume processing
            // if not it will get disconnected from the system
            private int timeout = 3; // seconds. Timeout will happen on the 3rd second

            @Override
            public void run() {
                if (timeout > 0) {
                    timeout--;
                    LOGGER.info("Checking if device id: {} got re-connected...", device.getId());

                    // every second check if device got reconnected
                    IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
                    if (deviceInTracker != null) {
                        if (deviceInTracker.isReconnectRequired()) {
                            publishDeviceDisconnectedEvents(disconnectedDevices);
                            //cancel the task once it is completed
                            disconnectionTimer.cancel();
                        } else if (deviceInTracker.isEraseInProgress()
                                || deviceInTracker.isRestartInProgress()
                                || deviceInTracker.isRestoreInProgress()
                                || DeviceStage.ERASE_IN_PROGRESS.equals(deviceInTracker.getStage())
                                || DeviceStage.RESTORE_IN_PROGRESS.equals(deviceInTracker.getStage())
                                || DeviceStage.ENABLE_DEV_MODE_IN_PROGRESS.equals(deviceInTracker.getStage())) {
                            disconnectedDevices.remove(device);
                            // stop syslog service on device as it will attempt to restart syslog if there is no
                            // response
                            sysLogServiceManager.stopSysLogServiceForDevice(device);
                            LOGGER.info("Device id: {} is getting restarted for processing ({})", device.getId(),
                                    deviceInTracker.getStage().getText());
                            // since the device is being processed we don't have to continue the timer
                            disconnectionTimer.cancel();
                        } else {
                            try {
                                // Check connected device again to see if there was an intermittent disconnection
                                final Set<IosDevice> newCurrentDevices =
                                        iosDeviceConnectionService.getConnectedDevices();
                                // Remove devices with intermittent disconnection issue from disconnectedDevices
                                // and add them back to connected devices and previous devices
                                boolean isDeviceConnectedAgain = newCurrentDevices.stream()
                                        .anyMatch(iosDevice -> Objects.equals(iosDevice.getId(), device.getId()));
                                if (isDeviceConnectedAgain
                                        && !DeviceStage.DISCONNECTED.equals(deviceInTracker.getStage())) {

                                    Integer previousPortNo = null;
                                    Integer newPortNo = null;
                                    if (isMappingAvailable) {
                                        previousPortNo = deviceInTracker.getPortNumber();
                                        newPortNo = portMappingService.getMappedPort(device.getId());
                                    }

                                    if (!isMappingAvailable || (Objects.equals(previousPortNo, newPortNo))) {
                                        disconnectedDevices.remove(device);
                                        newlyConnectedDevices.add(device);
                                        previouslyConnectedDevices.add(device);
                                        LOGGER.info("Device id: {} got re-connected, for processing ({})",
                                                device.getId(),
                                                deviceInTracker.getStage().getText());
                                        // since the device is reconnected we don't have to continue the timer
                                        disconnectionTimer.cancel();
                                    } else {
                                        // device got reconnected on a different port
                                        LOGGER.info("Device id: {} got re-connected but on a different port, " +
                                                        "restart processing ({})",
                                                device.getId(),
                                                deviceInTracker.getStage());
                                        publishSingleDisconnectedEvent(device);
                                        Thread.sleep(1000);
                                        publishSingleConnectedEvent(device);
                                        previouslyConnectedDevices.add(device);
                                        disconnectionTimer.cancel();
                                    }
                                } else {
                                    LOGGER.info("Device id: {} not found in the newly connected devices. " +
                                                    "Device stage is {}",
                                            device.getId(),
                                            deviceInTracker.getStage());
                                }
                            } catch (IOException | InterruptedException e) {
                                LOGGER.error("Error occurred while getting connected devices", e);
                            }
                        }
                    } else {
                        LOGGER.warn("Device id: {} not found in the tracker; disconnecting.", device.getId());
                        publishDeviceDisconnectedEvents(disconnectedDevices);
                        //cancel the task once it is completed
                        disconnectionTimer.cancel();
                    }
                } else {
                    publishDeviceDisconnectedEvents(disconnectedDevices);
                    //cancel the task once it is completed
                    disconnectionTimer.cancel();
                }
            }
        }, new Date(), 1000);
    }

    /**
     * Publish disconnected event for devices that have been disconnected from the machine.
     *
     * @param disconnectedDevices disconnected devices
     */
    private void publishDeviceDisconnectedEvents(final Set<IosDevice> disconnectedDevices) {
        // Publish DeviceDisconnectedEvent for newly disconnected devices
        for (IosDevice device : disconnectedDevices) {
            publishSingleDisconnectedEvent(device);
        }
    }

    /**
     * Publish a single device disconnected event
     *
     * @param device device
     */
    private void publishSingleDisconnectedEvent(final IosDevice device) {
        if (!isMappingAvailable || deviceConnectionTracker.getDevice(device.getId()) != null) {
            LOGGER.debug("Device disconnected: {}", device.getId());
            eventPublisher.publishEvent(new IosDisconnectedEvent(this, device));
        } else {
            LOGGER.warn("No device found in Tracker for id: {}", device.getId());
        }
    }

    /**
     * Publish DeviceConnectedEvent for newly Connected devices
     * Or continue processing from last known stage for reconnected devices
     *
     * @param connectedDevices connected devices
     */
    private void publishDeviceConnectedEvents(final Set<IosDevice> connectedDevices) {
        for (IosDevice device : connectedDevices) {
            if (DeviceConnectionMode.PORT_MAP.equals(inMemoryStore.getDeviceConnectionMode())) {
                eventPublisher.publishEvent(new PortMapDeviceConnectionEvent(this, device));
            } else {
                publishSingleConnectedEvent(device);
            }
        }
    }

    /**
     * Publish single device connected event
     *
     * @param device iphone
     */
    private void publishSingleConnectedEvent(final IosDevice device) {
        int mappedPort = portMappingService.getMappedPort(device.getId());
        Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());

        LOGGER.info("Connected Device: {} Mapped port: {} Mapping Available: {} isRecovery: {}" +
                        " deviceInTrackerIsNull: {}", device.getId(), (mappedPort != -1 ? mappedPort + 1 : mappedPort),
                isMappingAvailable, device.isConnectedInRecovery(), deviceInTracker == null);

        if (!isMappingAvailable || mappedPort != -1) {

            if (deviceInTracker != null && deviceInTracker.getStage() != null
                    && !deviceInTracker.isRestartInProgress()) {
                // If device is found in existing device, that means it didn't disconnect.
                // So, the process should continue from tha last known stage
                LOGGER.info("Resuming processing for device id: {} from stage: {}", deviceInTracker.getId(),
                        deviceInTracker.getStage());
                new Thread(() -> deviceStageManager.raiseEventForDeviceInProgress(deviceInTracker)).start();
            } else if (deviceInTracker == null) {
                LOGGER.info("New Device detected id: {}", device.getId());
                device.setPortNumber(mappedPort);
                // Remove previously connected device from connected device map if port mapping is available
                if (portMappingService.isMappingAvailable()) {
                    removePreviouslyConnectedDeviceOnPort(device.getPortNumber(), device.getId());
                }
                eventPublisher.publishEvent(new IosConnectedEvent(this, device));
            } else {
                LOGGER.info("Restarting already connected device, don't reprocess");
            }
        } else {
            try {
                boolean deviceConnected = iosDeviceConnectionService.getConnectedDevices()
                        .stream()
                        .anyMatch(connectedDevice -> connectedDevice.getId().equalsIgnoreCase(device.getId()));
                LOGGER.warn("Port number for device {} cannot be retrieved, and device connected {}",
                        device.getId(), deviceConnected);
                if (deviceConnected && !device.isConnectedInRecovery() && deviceInTracker == null) {
                    eventPublisher.publishEvent(new PortMapUpdateRequestEvent(this));
                }
            } catch (Exception e) {
                LOGGER.error("Failed to check port number for device {} due to an error.", device.getId(), e);
            }
        }
    }

    /**
     * Retrieves a connected device by its UDID
     *
     * @param udid device id
     * @return device associated with `udid` or `null` if no match is found
     */
    public IosDevice getDevice(final String udid) {
        synchronized (previouslyConnectedDevices) {
            for (IosDevice device : previouslyConnectedDevices) {
                if (device.getId().equalsIgnoreCase(udid)) {
                    return device;
                }
            }
            return null;
        }
    }

    /**
     * Stop the device processing thread
     */
    public void quit() {
        keepGoing = false;
    }

    /**
     * Resume device processing
     */
    public void resumeDeviceProcessing() {
        shouldPauseDeviceProcessing = false;
    }

    /**
     * Pause device processing
     */
    public void pauseDeviceProcessing() {
        shouldPauseDeviceProcessing = true;
    }

    /**
     * Removes a previously connected device from the connected device map
     * if a new device is connected to the same port.
     *
     * @param newPortNo new port number
     * @param deviceId  device identifier.
     */
    public void removePreviouslyConnectedDeviceOnPort(final int newPortNo, final String deviceId) {
        Map<String, Device> connectedDevices = deviceConnectionTracker.getConnectedDevices();
        for (Map.Entry<String, Device> entry : connectedDevices.entrySet()) {
            Device connectedDevice = entry.getValue();
            if (newPortNo == connectedDevice.getPortNumber()
                    && !connectedDevice.getId().equalsIgnoreCase(deviceId)
                    && connectedDevice instanceof AndroidDevice androidDevice) {
                LOGGER.warn("Found device: {} still connected on the same port: {} as the new device: {}," +
                        " will attempt to remove it", connectedDevice.getId(), newPortNo, deviceId);
                final AndroidDisconnectedEvent event = new AndroidDisconnectedEvent(this, androidDevice);
                // if a device was replaced on a port where erase or restore was in progress, then
                // set the manual release flag to true, so we can remove the old device from our system
                event.setManualRelease(true);
                eventPublisher.publishEvent(event);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    // nothing to do
                }
                break;
            } else if (newPortNo == connectedDevice.getPortNumber()
                    && !connectedDevice.getId().equalsIgnoreCase(deviceId)
                    && connectedDevice instanceof IosDevice iosDevice) {
                LOGGER.warn("Found device: {} still connected on the same port: {} as the new device: {}," +
                        " will attempt to remove it", connectedDevice.getId(), newPortNo, deviceId);

                if (connectedDevice.isConnectedInRecovery()) {
                    // There might be a case where the device was connected in recovery first and being restored.
                    // After few minutes the device gets detected with the id in normal mode. Here, we need to prevent
                    // disconnection as both devices are the same.
                    LOGGER.info("Previous device:{} was connected in recovery so the new device id:{}. may be the " +
                                    "id of same device in normal mode. Not disconnecting the previous device",
                            connectedDevice.getId(), deviceId);
                    return;
                }

                final IosDisconnectedEvent event = new IosDisconnectedEvent(this, iosDevice);
                // if a device was replaced on a port where erase or restore was in progress, then
                // set the manual release flag to true, so we can remove the old device from our system
                event.setManualRelease(true);
                eventPublisher.publishEvent(event);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    // nothing to do
                }
                break;
            }
        }
    }
}
