package com.phonecheck.device.connection.iwatch;

import com.phonecheck.device.connection.ios.IosDeviceConnectionService;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceConnectionMode;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.device.stage.DeviceStage;
import com.phonecheck.model.device.tracker.DeviceConnectionTracker;
import com.phonecheck.model.event.device.iwatch.IWatchHostConnectedEvent;
import com.phonecheck.model.event.device.iwatch.IWatchHostDisconnectedEvent;
import com.phonecheck.model.event.port.PortMapUpdateRequestEvent;
import com.phonecheck.model.event.device.iwatch.IWatchHostWaitForBootUpEvent;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.port.PortMappingService;
import com.phonecheck.syslog.SysLogServiceManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.*;

/**
 * Polls idevice_usb for iWatch hosts connections
 */
@Component
public class GetIWatchHostDevicesThread extends Thread {
    private static final Logger LOGGER = LoggerFactory.getLogger(GetIWatchHostDevicesThread.class);
    private final Set<IosDevice> previouslyConnectedDevices = Collections.synchronizedSet(new HashSet<>());
    private final IosDeviceConnectionService iosDeviceConnectionService;
    private final DeviceConnectionTracker deviceConnectionTracker;
    private final InMemoryStore inMemoryStore;
    private final ApplicationEventPublisher eventPublisher;
    private final PortMappingService portMappingService;
    private final SysLogServiceManager sysLogServiceManager;
    private boolean keepGoing = true;
    private boolean shouldPauseDeviceProcessing = false;
    private boolean isMappingAvailable;

    public GetIWatchHostDevicesThread(final IosDeviceConnectionService iosDeviceConnectionService,
                                      final DeviceConnectionTracker deviceConnectionTracker,
                                      final InMemoryStore inMemoryStore,
                                      final ApplicationEventPublisher eventPublisher,
                                      final PortMappingService portMappingService,
                                      final SysLogServiceManager sysLogServiceManager) {
        this.iosDeviceConnectionService = iosDeviceConnectionService;
        this.deviceConnectionTracker = deviceConnectionTracker;
        this.inMemoryStore = inMemoryStore;
        this.eventPublisher = eventPublisher;
        this.portMappingService = portMappingService;
        this.isMappingAvailable = portMappingService.isMappingAvailable();
        this.sysLogServiceManager = sysLogServiceManager;
    }

    @Override
    public void run() {
        shouldPauseDeviceProcessing = false;
        while (keepGoing) {
            if (shouldPauseDeviceProcessing) {
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    // do nothing
                }
                continue;
            }
            checkDevices();
        }
    }

    public void checkDevices() {
        try {
            isMappingAvailable = portMappingService.isMappingAvailable();
            Set<IosDevice> currentDevices = iosDeviceConnectionService.getConnectedDevices();

            final Set<IosDevice> newlyConnectedDevices = findNewlyConnectedDevices(currentDevices);
            final Set<IosDevice> disconnectedDevices = findDisconnectedDevices(currentDevices);

            // Check whether devices marked as 'disconnected' are actually required to be disconnected
            handleDisconnectedDevices(disconnectedDevices, newlyConnectedDevices);

            // Publish device connected/disconnected events
            publishDeviceConnectedEvents(newlyConnectedDevices);

            // Reset previous devices with current devices
            previouslyConnectedDevices.clear(); //id, serial
            previouslyConnectedDevices.addAll(currentDevices);
        } catch (Throwable e) {
            LOGGER.error("Could not get connected devices", e);
        } finally {
            try {
                Thread.sleep(1100);
            } catch (InterruptedException e) {
                // We don't really care if this thread was interrupted
            }
        }
    }


    /**
     * Method to find the newly connected devices
     *
     * @param currentDevices currently connected devices
     * @return set of newly connected ios devices
     */
    private Set<IosDevice> findNewlyConnectedDevices(final Set<IosDevice> currentDevices) {
        // newly connected devices = all the current devices that are not in previously connected devices
        Set<IosDevice> newlyConnectedDevices = new HashSet<>();

        for (IosDevice device : currentDevices) {
            // checking if the current device is in previous devices list
            Optional<IosDevice> iosDevice = previouslyConnectedDevices.stream()
                    .filter(previousDevice ->
                            StringUtils.isNotBlank(previousDevice.getId())
                                    && previousDevice.getId().equals(device.getId()))
                    .findFirst();

            if (iosDevice.isEmpty()) {
                // device is not in the previous devices list, so considering it's a new device
                newlyConnectedDevices.add(device);
            } else {
                // device was previously connected let's update the old device connection state in tracker
                IosDevice deviceInTracker = (IosDevice) deviceConnectionTracker.getDevice(device.getId());
                if (deviceInTracker != null
                        && deviceInTracker.isUsbMode() != device.isUsbMode()) {
                    LOGGER.info("Ios device {} connection state changed to: {}",
                            device.getId(), device.isUsbMode() ? "USB" : "MUX");
                    deviceInTracker.setUsbMode(device.isUsbMode());
                }
            }
        }

        StringBuilder stringBuilder = new StringBuilder();
        newlyConnectedDevices.forEach(device
                -> stringBuilder.append(device.getId()).append(" ")
                .append(device.isUsbMode() ? "USB" : "MUX").append("\n"));

        if (!stringBuilder.isEmpty()) {
            LOGGER.info("Newly connected devices:\n{}", stringBuilder);
        }
        return newlyConnectedDevices;
    }

    /**
     * Method to find recently disconnected devices
     *
     * @param currentDevices currently connected devices
     * @return set of newly connected ios devices
     */
    private Set<IosDevice> findDisconnectedDevices(final Set<IosDevice> currentDevices) {
        // disconnected devices = previous devices - current devices
        Set<IosDevice> disconnectedDevices = new HashSet<>(previouslyConnectedDevices);
        disconnectedDevices.removeAll(currentDevices);
        return disconnectedDevices;
    }

    /**
     * Handles disconnected devices by scheduling disconnection
     * timers based on the iWatch device connection mode.
     *
     * @param disconnectedDevices   the set of devices that have been disconnected.
     * @param newlyConnectedDevices the set of devices that have recently connected.
     */
    private void handleDisconnectedDevices(final Set<IosDevice> disconnectedDevices,
                                           final Set<IosDevice> newlyConnectedDevices) {
        if (DeviceConnectionMode.IWATCH_HOST.equals(inMemoryStore.getDeviceConnectionMode())) {
            Set<IosDevice> scheduledToDisconnect = Set.copyOf(disconnectedDevices);
            scheduledToDisconnect.forEach(device ->
                    scheduleDisconnectionTimerForDevice(device, newlyConnectedDevices, disconnectedDevices));
        }
    }

    /**
     * If a device is disconnected when its in MUX mode, we will schedule a 2 seconds timer for disconnection.
     * If devices gets reconnected within 2 seconds, we cancel the timer.
     *
     * @param device                device potentially disconnected
     * @param newlyConnectedDevices set of newly connected devices
     * @param disconnectedDevices   set of disconnected devices
     */
    private void scheduleDisconnectionTimerForDevice(final IosDevice device,
                                                     final Set<IosDevice> newlyConnectedDevices,
                                                     final Set<IosDevice> disconnectedDevices) {
        final Timer disconnectionTimer = new Timer();
        disconnectionTimer.scheduleAtFixedRate(new TimerTask() {
            // the device can get reconnected within 3 seconds to resume processing
            // if not it will get disconnected from the system
            private int timeout = 3; // seconds. Timeout will happen on the 3rd second

            @Override
            public void run() {
                if (timeout > 0) {
                    timeout--;
                    LOGGER.info("Checking if device id: {} got re-connected...", device.getId());

                    // every second check if device got reconnected
                    Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());
                    if (deviceInTracker != null) {
                        if (deviceInTracker.isReconnectRequired()) {
                            try {
                                publishDeviceDisconnectedEvents(disconnectedDevices);
                            } catch (IllegalStateException ex) {
                                LOGGER.error("Failed to publish device disconnected event: {}", ex.getMessage());
                            } finally {
                                //cancel the task once it is completed
                                disconnectionTimer.cancel();
                            }
                        } else if (deviceInTracker.isEraseInProgress()
                                || deviceInTracker.isRestartInProgress()) {
                            disconnectedDevices.remove(device);
                            // stop syslog service on device as it will attempt to restart syslog if there is no
                            // response
                            sysLogServiceManager.stopSysLogServiceForDevice(device);
                            LOGGER.info("Device id: {} is getting restarted for processing ({})", device.getId(),
                                    deviceInTracker.getStage().getText());
                            // since the device is being processed we don't have to continue the timer
                            disconnectionTimer.cancel();
                        } else {
                            try {
                                // Check connected device again to see if there was an intermittent disconnection
                                final Set<IosDevice> newCurrentDevices =
                                        iosDeviceConnectionService.getConnectedDevices();
                                // Remove devices with intermittent disconnection issue from disconnectedDevices
                                // and add them back to connected devices and previous devices
                                boolean isDeviceConnectedAgain = newCurrentDevices.stream()
                                        .anyMatch(iosDevice -> Objects.equals(iosDevice.getId(), device.getId()));
                                if (isDeviceConnectedAgain
                                        && !DeviceStage.DISCONNECTED.equals(deviceInTracker.getStage())) {

                                    Integer previousPortNo = null;
                                    Integer newPortNo = null;
                                    if (isMappingAvailable) {
                                        previousPortNo = deviceInTracker.getPortNumber();
                                        newPortNo = portMappingService.getMappedPort(device.getId());
                                    }

                                    if (!isMappingAvailable || (Objects.equals(previousPortNo, newPortNo))) {
                                        disconnectedDevices.remove(device);
                                        newlyConnectedDevices.add(device);
                                        previouslyConnectedDevices.add(device);
                                        LOGGER.info("Device id:{} got re-connected, for processing...", device.getId());
                                        // since the device is reconnected we don't have to continue the timer
                                        disconnectionTimer.cancel();
                                    } else {
                                        // device got reconnected on a different port
                                        LOGGER.info("Device id: {} got re-connected but on a different port, " +
                                                        "restart processing ({})",
                                                device.getId(),
                                                deviceInTracker.getStage());
                                        try {
                                            publishSingleDisconnectedEvent(device);
                                            Thread.sleep(1000);
                                            publishSingleConnectedEvent(device);
                                        } catch (IllegalStateException ex) {
                                            LOGGER.error("Failed to publish device events: {}", ex.getMessage());
                                        } catch (InterruptedException e) {
                                            Thread.currentThread().interrupt();
                                            LOGGER.error("Thread interrupted while publishing events", e);
                                        }
                                        previouslyConnectedDevices.add(device);
                                        disconnectionTimer.cancel();
                                    }
                                } else {
                                    LOGGER.info("Device id: {} not found in the newly connected devices. " +
                                                    "Device stage is {}",
                                            device.getId(),
                                            deviceInTracker.getStage());
                                }
                            } catch (IOException e) {
                                LOGGER.error("Error occurred while getting connected devices", e);
                            }
                        }
                    } else {
                        LOGGER.warn("Device id: {} not found in the tracker; disconnecting.", device.getId());
                        try {
                            publishDeviceDisconnectedEvents(disconnectedDevices);
                        } catch (IllegalStateException ex) {
                            LOGGER.error("Failed to publish device disconnected event: {}", ex.getMessage());
                        } finally {
                            //cancel the task once it is completed
                            disconnectionTimer.cancel();
                        }
                    }
                } else {
                    try {
                        publishDeviceDisconnectedEvents(disconnectedDevices);
                    } catch (IllegalStateException ex) {
                        LOGGER.error("Failed to publish device disconnected event: {}", ex.getMessage());
                    } finally {
                        //cancel the task once it is completed
                        disconnectionTimer.cancel();
                    }
                }
            }
        }, new Date(), 1000);
    }

    /**
     * Publish disconnected event for devices that have been disconnected from the machine.
     *
     * @param disconnectedDevices disconnected devices
     */
    private void publishDeviceDisconnectedEvents(final Set<IosDevice> disconnectedDevices) {
        // Publish DeviceDisconnectedEvent for newly disconnected devices
        for (IosDevice device : disconnectedDevices) {
            publishSingleDisconnectedEvent(device);
        }
    }

    /**
     * Publish a single device disconnected event
     *
     * @param device device
     */
    private void publishSingleDisconnectedEvent(final IosDevice device) {
        if (!isMappingAvailable || deviceConnectionTracker.getDevice(device.getId()) != null) {
            LOGGER.info("IWatch host disconnected: {}", device.getId());
            eventPublisher.publishEvent(new IWatchHostDisconnectedEvent(this, device));
        } else {
            LOGGER.warn("No iWatch host found in Tracker for id: {}", device.getId());
        }
    }

    /**
     * Publish DeviceConnectedEvent for newly Connected devices
     * Or continue processing from last known stage for reconnected devices
     *
     * @param connectedDevices connected devices
     */
    private void publishDeviceConnectedEvents(final Set<IosDevice> connectedDevices) {
        for (IosDevice device : connectedDevices) {
                publishSingleConnectedEvent(device);
            }
    }

    /**
     * Publish single device connected event
     *
     * @param device iphone
     */
    private void publishSingleConnectedEvent(final IosDevice device) {
        int mappedPort = portMappingService.getMappedPort(device.getId());
        Device deviceInTracker = deviceConnectionTracker.getDevice(device.getId());

        LOGGER.info("Connected Host Device: {} Mapped port: {} Mapping Available: {} deviceInTrackerIsNull: {}",
                device.getId(), (mappedPort != -1 ? mappedPort + 1 : mappedPort), isMappingAvailable,
                deviceInTracker == null);

        if (!isMappingAvailable || mappedPort != -1) {
            if (deviceInTracker != null && deviceInTracker.getStage() != null
                    && !deviceInTracker.isRestartInProgress()) {
                // If device is found in existing device, that means it didn't disconnect.
                // So, the process should continue from tha last known stage
                LOGGER.info("Resuming processing for host device id: {} from stage: {}", deviceInTracker.getId(),
                        deviceInTracker.getStage());
                eventPublisher.publishEvent(new IWatchHostWaitForBootUpEvent(this, device));
            } else if (deviceInTracker == null) {
                LOGGER.info("New host device detected id: {}", device.getId());
                device.setPortNumber(mappedPort);
                // Remove previously connected device from connected device map if port mapping is available
                if (portMappingService.isMappingAvailable()) {
                    removePreviouslyConnectedDeviceOnPort(device.getPortNumber(), device.getId());
                }
                eventPublisher.publishEvent(new IWatchHostConnectedEvent(this, device));
            } else {
                LOGGER.info("Restarting already connected host device, don't reprocess");
            }
        } else {
            try {
                boolean deviceConnected = iosDeviceConnectionService.getConnectedDevices()
                        .stream()
                        .anyMatch(connectedDevice -> connectedDevice.getId().equalsIgnoreCase(device.getId()));
                LOGGER.warn("Port number for device {} cannot be retrieved, and device connected {}",
                        device.getId(), deviceConnected);
                if (deviceConnected && deviceInTracker == null) {
                    eventPublisher.publishEvent(new PortMapUpdateRequestEvent(this));
                }
            } catch (Exception e) {
                LOGGER.error("Failed to check port number for device {} due to an error.", device.getId(), e);
            }
        }
    }

    /**
     * Retrieves a connected device by its UDID
     *
     * @param udid device id
     * @return device associated with `udid` or `null` if no match is found
     */
    public IosDevice getDevice(final String udid) {
        synchronized (previouslyConnectedDevices) {
            for (IosDevice device : previouslyConnectedDevices) {
                if (device.getId().equalsIgnoreCase(udid)) {
                    return device;
                }
            }
            return null;
        }
    }

    /**
     * Stop the device processing thread
     */
    public void quit() {
        keepGoing = false;
    }

    /**
     * Resume device processing
     */
    public void resumeDeviceProcessing() {
        shouldPauseDeviceProcessing = false;
    }

    /**
     * Pause device processing
     */
    public void pauseDeviceProcessing() {
        shouldPauseDeviceProcessing = true;
    }

    /**
     * Removes a previously connected device from the connected device map
     * if a new device is connected to the same port.
     *
     * @param newPortNo new port number
     * @param deviceId  device identifier.
     */
    public void removePreviouslyConnectedDeviceOnPort(final int newPortNo, final String deviceId) {
        Map<String, Device> connectedDevices = deviceConnectionTracker.getConnectedDevices();
        for (Map.Entry<String, Device> entry : connectedDevices.entrySet()) {
            Device connectedDevice = entry.getValue();
            if (newPortNo == connectedDevice.getPortNumber()
                    && !connectedDevice.getId().equalsIgnoreCase(deviceId)) {
                LOGGER.info("Found device: {} still connected on the same port: {} as the new device: {}," +
                        " will attempt to remove it", connectedDevice.getId(), newPortNo, deviceId);

                final IWatchHostDisconnectedEvent event = new IWatchHostDisconnectedEvent(this,
                        (IosDevice) connectedDevice);

                event.setManualRelease(true);
                eventPublisher.publishEvent(event);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    // nothing to do
                }
                break;
            }
        }
    }
}
