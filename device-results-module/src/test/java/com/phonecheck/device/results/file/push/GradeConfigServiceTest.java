package com.phonecheck.device.results.file.push;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.ColorConfigResponse;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.ISupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GradeConfigServiceTest {
    @Mock
    private CloudApiRestClient cloudApiRestClient;
    @Mock
    private FileUtil fileUtil;
    @Mock
    private SupportFilePath supportFilePath;

    private final ObjectMapper mapper = new ObjectMapper();
    private GradeConfigService gradeConfigService;

    @BeforeEach
    public void setup() {
        gradeConfigService = new GradeConfigService(cloudApiRestClient, supportFilePath, fileUtil, mapper);
    }

    @Test
    public void testGetGradeAndColorConfigs() throws IOException {
        final int licenseId = 12149;

        CloudCustomizationResponse cloudCustomization = CloudCustomizationResponse.builder()
                .grading(List.of("A", "B", "C")).build();
        ColorConfigResponse colorConfigResponse = new ColorConfigResponse();
        when(cloudApiRestClient.getColorConfigs(licenseId)).
                thenReturn(colorConfigResponse);
        File testFile = new File("GradeConfig.json");
        when(fileUtil.createFile(anyString())).thenReturn(testFile);
        when(supportFilePath.getPaths()).thenReturn(new ISupportFilePathsStrategy() {
            @Override
            public String getRootFolderPath() {
                return "./";
            }

            @Override
            public String getToolsRootFolderPath() {
                return null;
            }

            @Override
            public String getFilesRootFolderPath() {
                return null;
            }

            @Override
            public String getWorkingDirectoryPath() {
                return null;
            }
        });


        gradeConfigService.createGradeConfigFile(licenseId, cloudCustomization);

        verify(cloudApiRestClient, never()).getGradeConfigs(anyInt());
        verify(cloudApiRestClient).getColorConfigs(anyInt());
        verify(fileUtil).createFile(anyString());
        verify(fileUtil).writeStringToFileIfDifferent(any(), anyString());

        testFile.delete();
    }
}