package com.phonecheck.device.results;

import com.phonecheck.command.device.android.files.AndroidPullFileCommand;
import com.phonecheck.command.device.android.files.AndroidReadFileContentCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.AndroidDevice;
import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.*;
import com.phonecheck.model.util.*;
import com.phonecheck.parser.device.DeviceBatteryResultsParser;
import com.phonecheck.parser.device.test.DeviceTestResultsParser;
import com.phonecheck.util.CustomizationUtil;
import jdk.jfr.Description;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationEventPublisher;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class AndroidTestResultsServiceTest {

    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private CustomizationUtil customizationUtil;
    @Mock
    private ApplicationEventPublisher eventPublisher;
    @Mock
    private CommandExecutor executor;
    @Mock
    private DeviceTestResultsParser testResultsParser;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private AndroidDevice device;
    private AndroidTestResultsService classUnderTest;
    @Mock
    private LocalizationService localizationService;
    @Mock
    private DeviceBatteryResultsParser deviceBatteryResultsParser;
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private FileUtil fileUtil;

    private static final String ROOT_FOLDER_PATH = "/root";
    private static final String DEVICE_SERIAL = "ABC123";
    private static final String COMMAND_OUTPUT = "Success";

    private static final String BATTERY_RESULTS_FROM_FILE =
            " <FileContent>\n" +
                    " {\"batteryStat\":{\"batteryDrainType\":\"Duration\",\"totalDischarge\":\"0%\"," +
                    "\"totalDuration\":\"1\",\n" +
                    "  \"startBattery\":\"84.0%\",\"endBattery\":\"84.0%\"},\"batteryDrainInfo\":{\"1\":\"84%\"}}\n" +
                    " </FileContent>\n";

    @BeforeEach
    void setup() throws IOException {
        device = new AndroidDevice();
        device.setId("12345678");
        classUnderTest = new AndroidTestResultsService(customizationUtil, eventPublisher, inMemoryStore, executor,
                testResultsParser, localizationService, deviceBatteryResultsParser, supportFilePath, fileUtil,
                timerLoggerUtil);
    }

    @Test
    public void testUpdateBatteryDrainPassResult() throws IOException {
        DeviceTestResult deviceTestResult = new DeviceTestResult();

        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Passed")));
        testResults.setFailed(new ArrayList<>(List.of(("Failed"))));
        deviceTestResult.setTestResults(testResults);

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("10%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("97");
        summaryFromFile.setTotalDischarge("10%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");

        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(20)
                                .build())
                        .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        classUnderTest.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getPassed().contains("Battery Drain"));
        assertFalse(testResults.getFailed().contains("Battery Drain"));
    }

    @Test
    public void testUpdateBatteryDrainFailResult() throws IOException {
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Passed")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("10%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("97");
        summaryFromFile.setTotalDischarge("10%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");


        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(5)
                                .build())
                        .build();
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        classUnderTest.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getFailed().contains("Battery Drain"));
        assertFalse(testResults.getPassed().contains("Battery Drain"));
    }

    @Test
    @Description("When battery drain test is done for the 2nd time and start battery is same, " +
            "don't update battery results")
    public void testBatteryResultsAndUpdateBatteryDrainResult() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Battery Drain")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("1%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("100");
        summaryFromFile.setEndBattery("96");
        summaryFromFile.setTotalDischarge("4%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");


        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(3)
                                .build())
                        .build();

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        classUnderTest.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertFalse(testResults.getFailed().contains("Battery Drain"));
        assertTrue(testResults.getPassed().contains("Battery Drain"));
    }

    @Test
    @Description("When battery drain test is done for the 2nd time and start battery is different, " +
            " update battery results")
    public void testBatteryResultsAndUpdateBatteryDrainResult1() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Battery Drain")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(5)
                                .build())
                        .build();

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("1%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("92");
        summaryFromFile.setTotalDischarge("7%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");


        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        classUnderTest.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getFailed().contains("Battery Drain"));
        assertFalse(testResults.getPassed().contains("Battery Drain"));
    }

    @Test
    @Description("When battery drain test is done for the 1st time always update battery results")
    public void testBatteryResultsAndUpdateBatteryDrainResult3() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Passed")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(5)
                                .build())
                        .build();


        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("92");
        summaryFromFile.setTotalDischarge("7%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");

        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        classUnderTest.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getFailed().contains("Battery Drain"));
        assertFalse(testResults.getPassed().contains("Battery Drain"));
    }

    @Test
    void testGetBatteryDrainTestResultSuccessfulParsing() throws IOException {
        when(executor.execute(any(AndroidReadFileContentCommand.class))).thenReturn(BATTERY_RESULTS_FROM_FILE);
        BatteryResults expectedResults = new BatteryResults();
        when(deviceBatteryResultsParser.parse(BATTERY_RESULTS_FROM_FILE)).thenReturn(expectedResults);

        BatteryResults result = classUnderTest.getBatteryDrainTestResult(device);

        assertNotNull(result);
        assertEquals(expectedResults, result);
    }

    @Test
    void testGetBatteryDrainTestResultFileNotAvailable() throws IOException {
        when(executor.execute(any(AndroidReadFileContentCommand.class))).
                thenReturn("\"ERROR: Could not connect to lockdownd");

        BatteryResults result = classUnderTest.getBatteryDrainTestResult(device);

        assertNull(result);
    }

    @Test
    void testGetBatteryDrainTestResultEmptyOutput() throws IOException {
        when(executor.execute(any(AndroidReadFileContentCommand.class))).thenReturn("");

        BatteryResults result = classUnderTest.getBatteryDrainTestResult(device);

        assertNull(result);
    }

    @Test
    void testPullAndZipMobileDataFolderSuccess() throws Exception {
        File rootDir = new File(ROOT_FOLDER_PATH);
        File deviceFolder = new File(rootDir, DEVICE_SERIAL + "_12345678");
        File zipFile = new File("/zip/output/file.zip");

        when(fileUtil.createDirectory(anyString(), anyString())).thenReturn(rootDir);
        when(fileUtil.createDirectory(eq(rootDir.getAbsolutePath()), anyString())).thenReturn(deviceFolder);
        when(supportFilePath.getPaths()).thenReturn(new WindowsSupportFilePathsStrategy(false));
        when(executor.execute(any(AndroidPullFileCommand.class))).thenReturn(COMMAND_OUTPUT);
        when(fileUtil.zipAndDeleteMobileDataFolder(deviceFolder.getAbsolutePath())).thenReturn(zipFile);

        File result = classUnderTest.pullAndZipMobileDataFolder(device);

        assertNotNull(result);
        assertEquals(zipFile, result);
    }

    @Test
    void testPullAndZipMobileDataFolderFailureOnAdb() throws Exception {
        File rootDir = new File(ROOT_FOLDER_PATH);
        File deviceFolder = new File(rootDir, DEVICE_SERIAL + "_12345678");

        when(fileUtil.createDirectory(anyString(), anyString())).thenReturn(rootDir);
        when(fileUtil.createDirectory(eq(rootDir.getAbsolutePath()), anyString())).thenReturn(deviceFolder);
        when(supportFilePath.getPaths()).thenReturn(new WindowsSupportFilePathsStrategy(false));
        when(executor.execute(any(AndroidPullFileCommand.class))).thenThrow(new RuntimeException("ADB failed"));

        File result = classUnderTest.pullAndZipMobileDataFolder(device);

        assertNull(result);
    }

    @Test
    void testPullAndZipMobileDataFolderFailureOnZip() throws Exception {
        File rootDir = new File(ROOT_FOLDER_PATH);
        File deviceFolder = new File(rootDir, DEVICE_SERIAL + "_12345678");

        when(fileUtil.createDirectory(anyString(), anyString())).thenReturn(rootDir);
        when(fileUtil.createDirectory(eq(rootDir.getAbsolutePath()), anyString())).thenReturn(deviceFolder);
        when(supportFilePath.getPaths()).thenReturn(new WindowsSupportFilePathsStrategy(false));
        when(executor.execute(any(AndroidPullFileCommand.class))).thenReturn(COMMAND_OUTPUT);
        when(fileUtil.zipAndDeleteMobileDataFolder(deviceFolder.getAbsolutePath()))
                .thenThrow(new RuntimeException("Zip failed"));

        File result = classUnderTest.pullAndZipMobileDataFolder(device);

        assertNull(result);
    }

}