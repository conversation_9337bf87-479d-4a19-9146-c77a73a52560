package com.phonecheck.device.results.file.push;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.ISupportFilePathsStrategy;
import com.phonecheck.model.util.SupportFilePath;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MobileConfigFileServiceTest {
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private FileUtil fileUtil;
    private final ObjectMapper mapper = new ObjectMapper();
    private MobileConfigFileService mobileConfigFileService;

    @BeforeEach
    public void setup() {
        mobileConfigFileService = new MobileConfigFileService(inMemoryStore, supportFilePath, fileUtil, mapper);
    }

    @Test
    public void getMobileConfigFileContentTest() throws IOException {
        File testFile = new File("test.json");
        when(fileUtil.createFile(anyString())).thenReturn(testFile);
        when(supportFilePath.getPaths()).thenReturn(new ISupportFilePathsStrategy() {
                                                        @Override
                                                        public String getRootFolderPath() {
                                                            return "./";
                                                        }

                                                        @Override
                                                        public String getToolsRootFolderPath() {
                                                            return null;
                                                        }

                                                        @Override
                                                        public String getFilesRootFolderPath() {
                                                            return null;
                                                        }

                                                        @Override
                                                        public String getWorkingDirectoryPath() {
                                                            return null;
                                                        }
                                                    }
        );
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder()
                .cosmeticSettings(CloudCustomizationResponse.CosmeticSettings
                        .builder()
                        .enableCosmetics(true)
                        .cosmeticType(CloudCustomizationResponse.CosmeticType.QA)
                        .cosmeticQuestions(new CloudCustomizationResponse.CosmeticQuestion[]
                                {CloudCustomizationResponse.CosmeticQuestion.builder()
                                        .os(CloudCustomizationResponse.CosmeticOS.ANDROID)
                                        .name("Cosmetic question 1")
                                        .question("Is the screen broken?")
                                        .responses(List.of(
                                                CloudCustomizationResponse.CosmeticResponse.builder()
                                                        .criteria(true).response("Yes").build(),
                                                CloudCustomizationResponse.CosmeticResponse.builder()
                                                        .criteria(false).response("No").build()))
                                        .build()})
                        .build())
                .build());

        mobileConfigFileService.createMobileConfigFile();

        String expectedCosmeticsString = "{\"Cosmetics\":[{\"title\":\"Is the screen broken?\"," +
                "\"platform\":\"Android\",\"shortKey\":\"Cosmetic question 1\"," +
                "\"options\":[{\"response\":\"Yes\",\"result\":true},{\"response\":\"No\",\"result\":false}]}]" +
                ",\"cosmetic_customization_value\":\"1\"}";
        verify(fileUtil).createFile(anyString());
        verify(fileUtil).writeStringToFileIfDifferent(any(), eq(expectedCosmeticsString));
        testFile.delete();
    }
}
