package com.phonecheck.device.results;

import com.phonecheck.command.device.ios.test.IosCopyFileContentCommand;
import com.phonecheck.command.device.ios.test.IosListFilesCommand;
import com.phonecheck.command.device.ios.test.IosReadFileCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.info.ios.IosDeviceInfoService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.device.IosDevice;
import com.phonecheck.model.status.NotificationStatus;
import com.phonecheck.model.status.WorkingStatus;
import com.phonecheck.model.store.InMemoryStore;
import com.phonecheck.model.test.*;
import com.phonecheck.model.util.*;
import com.phonecheck.parser.device.DeviceBatteryResultsParser;
import com.phonecheck.parser.device.test.DeviceTestResultsParser;
import jdk.jfr.Description;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IosTestResultsServiceTest {

    @Mock
    private CommandExecutor executor;
    @Mock
    private IosDeviceInfoService deviceInfoService;
    @Mock
    private DeviceTestResultsParser deviceTestResultsParser;
    @InjectMocks
    private IosTestResultsService testResultsService;
    @Mock
    private TimerLoggerUtil timerLoggerUtil;
    @Mock
    private LocalizationService localizationService;
    @Mock
    private DeviceBatteryResultsParser deviceBatteryResultsParser;
    @Mock
    private InMemoryStore inMemoryStore;
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private FileUtil fileUtil;

    private IosDevice device;

    private final String testResultsFromFile = "{ \"CosmeticResults\": { \"total\": \"Q1-Fail,Q2-Fail,Q3-Good\"," +
            " \"passed\": \"Q3-Good\", \"failed\": \"Q1-Fail,Q2-Fail\", \"pending\": \"Q4\", \"total_count\": \"4\"," +
            " \"failed_count\": \"2\", \"passed_count\": \"1\", \"pending_count\": \"1\" }," +
            " \"BatteryResults\": { \"batteryStat\" : { \"startBattery\" : \"43%\", \"endBattery\" : \"41%\"," +
            " \"totalDuration\" : \"\", \"batteryDrainType\" : \"Percentage\", \"totalDischarge\" : \"2%\" }," +
            " \"batteryDrainInfo\" : { \"4\" : \"0%\", \"2\" : \"0%\", \"7\" : \"0%\", \"6\" : \"0%\"," +
            " \"5\" : \"1%\", \"1\" : \"0%\", \"3\" : \"0%\" } }, \"GradeResults\": \"B\", " +
            "\"TestResults\": { \"total_count\": \"70\", \"failed\": \"Cosmetics,LCD,Microphone,Flip Switch," +
            "Volume Up Button,Network Connectivity,Loud Speaker,Power Button,UltraWide Camera,Front Camera Quality," +
            "Front Camera,Rear Video Camera,Front Microphone,Front Video Camera,Rear Camera,Video Microphone," +
            "Volume Down Button,Glass Cracked,Digitizer,Proximity Sensor,Telephoto Camera Quality,Flashlight," +
            "Vibration,Sim Reader,Rear Camera Quality,Telephoto Camera,Ultra Wide Camera Quality,Ear Speaker\"," +
            " \"failed_count\": \"31\", \"not_attended\": \"LCD,Microphone,Flip Switch,Volume Up Button," +
            "Network Connectivity,Loud Speaker,Power Button,UltraWide Camera,Front Camera Quality,Front Camera," +
            "Rear Video Camera,Front Microphone,Front Video Camera,Rear Camera,Video Microphone,Volume Down Button," +
            "Glass Cracked,Digitizer,Proximity Sensor,Telephoto Camera Quality,Flashlight,Vibration,Sim Reader," +
            "Rear Camera Quality,Telephoto Camera,Ultra Wide Camera Quality,Ear Speaker\", " +
            "\"passed\": \"Bluetooth,Wifi+GPS,Gyroscope,Screen Rotation,Accelerometer\", " +
            "\"total\": \"Spen Plus buttons,Rear Video Camera@Rear Video Camera,Headset-Left,Screen Rotation," +
            "Bluetooth@Bluetooth,Rear Camera Quality@Rear Camera Quality,Audio Output,Headset-Right,AutoSnapFront," +
            "Volume Up Button@Volume Up Button,Auto Accelerometer,Menu Button,AutoSnapRear,Loud Speaker-M," +
            "Telephoto Camera Quality@Telephoto Camera Quality,Flashlight@Flashlight,Mic ES,Edge Screen,Auto ES," +
            "Power Button@Power Button,Camera AutoFocus,Headset Port,Cosmetics@Cosmetics," +
            "Telephoto Camera@Telephoto Camera,SPen Remove,Glass Condition@Glass Cracked," +
            "Front Microphone@Front Microphone,Microphone@Microphone,Flash,Gyroscope," +
            "Network Connectivity@Network Connectivity,Audio Test,Video Microphone@Video Microphone," +
            "Proximity Sensor@Proximity Sensor,Glass Cracked,WiFi@Wifi+GPS,Vid Mic ES,Flip Switch@Flip Switch," +
            "SPen Back Button,SPen Menu Button,Mic LS Test,Auto LS,SPen Hover,Audio Input,Rear Camera@Rear Camera," +
            "Manual Vibration,Digitizer@Digitizer,Home Button,Volume Down Button@Volume Down Button," +
            "Sim Reader@Sim Reader,Front Video Camera@Front Video Camera,Camera Test," +
            "UltraWide Camera@UltraWide Camera,Earpiece@Ear Speaker,3D Touch,LCD@LCD," +
            "UltraWide Camera Quality@Ultra Wide Camera Quality,Call Test,Front Camera@Front Camera,Back Button," +
            "Mic ES Test,Stylus,Loud Speaker@Loud Speaker,Buttons Test," +
            "Accelerometer@Accelerometer$Screen Rotation$Gyroscope,Vibration@Vibration,FingerTrail Digitizer," +
            "Force Touch,Front Camera Quality@Front Camera Quality,SPen\", \"not_attended_count\": \"27\"," +
            " \"pending\": \"\", \"not_supported\": \"Spen Plus buttons,Headset-Left,Screen Rotation,Audio Output," +
            "Headset-Right,AutoSnapFront,Auto Accelerometer,Menu Button,AutoSnapRear,Loud Speaker-M,Mic ES," +
            "Edge Screen,Auto ES,Camera AutoFocus,Headset Port,SPen Remove,Flash,Gyroscope,Audio Test,Glass Cracked," +
            "Vid Mic ES,SPen Back Button,SPen Menu Button,Mic LS Test,Auto LS,SPen Hover,Audio Input," +
            "Manual Vibration,Home Button,Camera Test,3D Touch,Call Test,Back Button,Mic ES Test,Stylus," +
            "Buttons Test,FingerTrail Digitizer,Force Touch,SPen\", \"not_supported_count\": \"39\", " +
            "\"pending_count\": \"0\", \"passed_count\": \"5\" }, \"MicrophoneResults\": " +
            "{ \"BMAmplitude\": \"0.8586\", \"FMAmplitude\": \"0.8503\", \"RMAmplitude\": \"0.4263\" } }";

    private static final String BATTERY_RESULTS_FROM_FILE =
            " <FileContent>\n" +
            " {\"batteryStat\":{\"batteryDrainType\":\"Duration\",\"totalDischarge\":\"0%\"" +
                    ",\"totalDuration\":\"1\",\n" +
            "  \"startBattery\":\"84.0%\",\"endBattery\":\"84.0%\"},\"batteryDrainInfo\":{\"1\":\"84%\"}}\n" +
            " </FileContent>\n";

    private final String testResultsFromSysLog = "rhkg38yw4w-" + testResultsFromFile + "-4rhjg7x9gw";

    @BeforeEach
    void setup() throws IOException {
        device = new IosDevice();
        device.setId("12345678");
        device.setProductType("iPhone8,1");
        device.setTouchIdSensor(WorkingStatus.YES);
        device.setFaceIdSupported(false);
    }

    @Test
    public void testGetTestResults() throws IOException {
        IosDevice device = new IosDevice();
        device.setId("id");
        DeviceTestResult deviceTestResult = new DeviceTestResultsParser().parse(testResultsFromSysLog);
        when(executor.execute(any(IosReadFileCommand.class))).thenReturn("something");
        when(deviceTestResultsParser.parse(any())).thenReturn(deviceTestResult);
        when(deviceInfoService.postDeviceNotificationProxy(eq(device), anyString()))
                .thenReturn(NotificationStatus.SUCCESS);

        DeviceTestResultStatus testResultStatus = testResultsService.getTestResults(device, "com.phonecheckdiag3");

        DeviceTestResult testResult = testResultStatus.getDeviceTestResult();
        assertNotNull(testResult.getTestResults());
        assertTrue(testResult.getTestResults().getPassed().contains("Bluetooth"));
        assertNotNull(testResult.getCosmeticResults());
        assertNotNull(testResult.getBatteryResults());
        assertNotNull(testResult.getGradeResults());
        assertNotNull(testResult.getMicrophoneResults());

        verify(executor).execute(any(IosReadFileCommand.class));
        verify(deviceInfoService).postDeviceNotificationProxy(eq(device), anyString());
    }

    @Test
    void updatePassedResultsTest() {
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("passed")));
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);

        TestResults testResults1 = new TestResults();
        testResults1.setPassed(new ArrayList<>());
        DeviceTestResult deviceTestResult1 = new DeviceTestResult();
        deviceTestResult1.setTestResults(testResults1);
        device.setDeviceTestResult(deviceTestResult1);

        device.getDeviceTestResult().getTestResults().setPassed(
                new ArrayList<>(List.of("M-Fingerprint Sensor", "LCD")));
        when(localizationService.getLanguageSpecificText(InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey()))
                .thenReturn("Technical Conformity Mark");
        when(localizationService.getLanguageSpecificText(InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK.getKey()))
                .thenReturn("M-Technical Conformity Mark");

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().id("123")
                .testPlan(new CloudCustomizationResponse.TestPlan()).build());

        testResultsService.updatePassedTestResults(device, deviceTestResult);
        assertEquals("[M-Fingerprint Sensor, passed]",
                deviceTestResult.getTestResults().getPassed().toString());
    }

    @Test
    void updateFailedResultsTest() {
        TestResults testResults = new TestResults();
        testResults.setFailed(new ArrayList<>(List.of("failed")));
        DeviceTestResult deviceTestResult = new DeviceTestResult();
        deviceTestResult.setTestResults(testResults);

        TestResults testResults1 = new TestResults();
        testResults1.setFailed(new ArrayList<>());
        DeviceTestResult deviceTestResult1 = new DeviceTestResult();
        deviceTestResult1.setTestResults(testResults1);
        device.setDeviceTestResult(deviceTestResult1);

        device.getDeviceTestResult().getTestResults().setFailed(
                new ArrayList<>(List.of("M-Face ID", "Wifi")));
        when(localizationService.getLanguageSpecificText(InitialDefectKey.JAPANESE_CONFORMITY_MARK.getKey()))
                .thenReturn("Technical Conformity Mark");
        when(localizationService.getLanguageSpecificText(InitialDefectKey.MANUAL_JAPANESE_CONFORMITY_MARK.getKey()))
                .thenReturn("M-Technical Conformity Mark");

        device.setFaceIdSupported(true);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(CloudCustomizationResponse.builder().id("123")
                .testPlan(new CloudCustomizationResponse.TestPlan()).build());

        testResultsService.updateFailedTestResults(device, deviceTestResult);
        assertTrue(deviceTestResult.getTestResults().getFailed().size() == 2);
        assertTrue(deviceTestResult.getTestResults().getFailed().containsAll(
                List.of("M-Face ID", "failed")));
    }


    @Test
    public void testUpdateBatteryDrainPassResult() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();

        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Passed")));
        testResults.setFailed(new ArrayList<>(List.of(("Failed"))));
        deviceTestResult.setTestResults(testResults);

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("10%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("97");
        summaryFromFile.setTotalDischarge("10%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");

        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(20)
                                .build())
                        .build();
        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getPassed().contains("Battery Drain"));
        assertFalse(testResults.getFailed().contains("Battery Drain"));
    }

    @Test
    public void testUpdateBatteryDrainFailResult() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Passed")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("10%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("97");
        summaryFromFile.setTotalDischarge("10%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");


        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(5)
                                .build())
                        .build();
        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getFailed().contains("Battery Drain"));
        assertFalse(testResults.getPassed().contains("Battery Drain"));
    }

    @Test
    @Description("When battery drain test is done for the 2nd time and start battery is same, " +
            "don't update battery results")
    public void testBatteryResultsAndUpdateBatteryDrainResult() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Battery Drain")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("1%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("100");
        summaryFromFile.setEndBattery("96");
        summaryFromFile.setTotalDischarge("4%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");


        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(3)
                                .build())
                        .build();

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertFalse(testResults.getFailed().contains("Battery Drain"));
        assertTrue(testResults.getPassed().contains("Battery Drain"));
    }


    @Test
    @Description("Update battery discharge for ios>=17.0 ")
    public void testUpdateBatteryDrainResultForIosGreaterThan17() throws IOException {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Battery Drain")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);
        device.setOsMajorVersion(18);


        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("100");
        summaryFromFile.setEndBattery("");
        summaryFromFile.setTotalDischarge("");
        summaryFromFile.setTotalDuration("6");
        summaryFromFile.setBatteryDrainType("DURATION");


        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(3)
                                .build())
                        .build();

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);
        when(deviceInfoService.getCurrentBatteryChargePercentage(device)).thenReturn(95);

        testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getFailed().contains("Battery Drain"));
        assertFalse(testResults.getPassed().contains("Battery Drain"));
    }


    @Test
    @Description("When battery drain test is done for the 2nd time and start battery is different, " +
            " update battery results")
    public void testBatteryResultsAndUpdateBatteryDrainResult1() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Battery Drain")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(5)
                                .build())
                        .build();

        BatteryDrainSummary summary = new BatteryDrainSummary();
        summary.setStartBattery("100");
        summary.setEndBattery("99");
        summary.setTotalDischarge("1%");
        summary.setTotalDuration("3");
        summary.setBatteryDrainType("DURATION");

        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("92");
        summaryFromFile.setTotalDischarge("7%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");


        BatteryResults batteryResults = new BatteryResults();
        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResults.setBatteryDrain(summary);
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);
        deviceTestResult.setBatteryResults(batteryResults);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getFailed().contains("Battery Drain"));
        assertFalse(testResults.getPassed().contains("Battery Drain"));
    }

    @Test
    @Description("When battery drain test is done for the 1st time always update battery results")
    public void testBatteryResultsAndUpdateBatteryDrainResult3() {

        DeviceTestResult deviceTestResult = new DeviceTestResult();
        TestResults testResults = new TestResults();
        testResults.setPassed(new ArrayList<>(List.of("Passed")));
        testResults.setFailed(new ArrayList<>(List.of("Failed")));
        deviceTestResult.setTestResults(testResults);

        CloudCustomizationResponse cloudCustomization =
                CloudCustomizationResponse.builder()
                        .wifiSettings(new CloudCustomizationResponse.WifiSettings())
                        .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                                .failDrainIfDischargePercentage(5)
                                .build())
                        .build();


        BatteryDrainSummary summaryFromFile = new BatteryDrainSummary();
        summaryFromFile.setStartBattery("99");
        summaryFromFile.setEndBattery("92");
        summaryFromFile.setTotalDischarge("7%");
        summaryFromFile.setTotalDuration("3");
        summaryFromFile.setBatteryDrainType("DURATION");

        BatteryResults batteryResultsFromFile = new BatteryResults();
        batteryResultsFromFile.setBatteryDrain(summaryFromFile);

        device.setDeviceTestResult(deviceTestResult);
        device.getDeviceTestResult().setTestResults(testResults);

        DeviceTestResultStatus deviceTestResultStatus = new DeviceTestResultStatus();
        deviceTestResultStatus.setDeviceTestResult(deviceTestResult);
        deviceTestResultStatus.setNotificationStatus(NotificationStatus.SUCCESS);

        when(inMemoryStore.getAssignedCloudCustomization()).thenReturn(cloudCustomization);

        testResultsService.setBatteryResultsAndUpdateBatteryDrainResult(device, batteryResultsFromFile);
        // Verifying the expected behavior
        assertTrue(testResults.getFailed().contains("Battery Drain"));
        assertFalse(testResults.getPassed().contains("Battery Drain"));
    }

    @Test
    void testGetBatteryDrainTestResultSuccessfulParsing() throws IOException {
        when(executor.execute(any(IosReadFileCommand.class))).thenReturn(BATTERY_RESULTS_FROM_FILE);
        BatteryResults expectedResults = new BatteryResults();
        when(deviceBatteryResultsParser.parse(BATTERY_RESULTS_FROM_FILE)).thenReturn(expectedResults);

        BatteryResults result = testResultsService.getBatteryDrainTestResult(device, "iosAppIdentifier");

        assertNotNull(result);
        assertEquals(expectedResults, result);
    }

    @Test
    void testGetBatteryDrainTestResultFileNotAvailable() throws IOException {
        when(executor.execute(any(IosReadFileCommand.class))).
                thenReturn("\"ERROR: Could not connect to lockdownd");

        BatteryResults result = testResultsService.getBatteryDrainTestResult(
                device, "iosAppIdentifier");

        assertNull(result);
    }

    @Test
    void testGetBatteryDrainTestResultEmptyOutput() throws IOException {
        when(executor.execute(any(IosReadFileCommand.class))).thenReturn("");

        BatteryResults result = testResultsService.getBatteryDrainTestResult(
                device, "iosAppIdentifier");

        assertNull(result);
    }

    @Test
    void testPullAndZipMobileDataFolderSuccessfulFlow() throws Exception {
        File parentDir = new File("/tmp/root/mobile_data");
        File dataDir = new File(parentDir, "serialABC_123456");
        File zipFile = new File("/tmp/root/output.zip");

        when(fileUtil.createDirectory(anyString(), anyString())).thenReturn(parentDir);
        when(fileUtil.createDirectory(eq(parentDir.getAbsolutePath()), anyString())).thenReturn(dataDir);
        when(supportFilePath.getPaths()).thenReturn(new WindowsSupportFilePathsStrategy(false));

        String fileList = "log1.log\nlog2.json\nskip.exe\nnotes.txt";
        when(executor.execute(any(IosListFilesCommand.class))).thenReturn(fileList);

        for (String fileName : List.of("log1.log", "log2.json", "notes.txt")) {
            File mockLocalFile = new File(dataDir, fileName);
            when(fileUtil.createFile(mockLocalFile.getAbsolutePath())).thenReturn(mockLocalFile);
        }

        when(fileUtil.zipAndDeleteMobileDataFolder(dataDir.getAbsolutePath())).thenReturn(zipFile);

        File result = testResultsService.pullAndZipMobileDataFolder(device, "com.example.app");

        assertNotNull(result);
        assertEquals(zipFile, result);
        verify(fileUtil, times(1)).zipAndDeleteMobileDataFolder(anyString());
    }

    @Test
    void testPullAndZipMobileDataFolderNoFilesFound() throws Exception {
        File parentDir = new File("/tmp/root/mobile_data");
        File dataDir = new File(parentDir, "serialABC_123456");

        when(fileUtil.createDirectory(anyString(), anyString())).thenReturn(parentDir);
        when(fileUtil.createDirectory(eq(parentDir.getAbsolutePath()), anyString())).thenReturn(dataDir);
        when(supportFilePath.getPaths()).thenReturn(new WindowsSupportFilePathsStrategy(false));
        when(executor.execute(any(IosListFilesCommand.class))).thenReturn("");

        File zipFile = new File("/tmp/empty.zip");
        when(fileUtil.zipAndDeleteMobileDataFolder(eq(dataDir.getAbsolutePath()))).thenReturn(zipFile);

        File result = testResultsService.pullAndZipMobileDataFolder(device, "com.example.app");

        assertNotNull(result);
        verify(executor, never()).execute(argThat(cmd -> cmd instanceof IosCopyFileContentCommand));
    }

    @Test
    void testPullAndZipMobileDataFolderCopyFailsForSomeFiles() throws Exception {
        File parentDir = new File("/tmp/root/mobile_data");
        File dataDir = new File(parentDir, "serialABC_123456");

        when(fileUtil.createDirectory(anyString(), anyString())).thenReturn(parentDir);
        when(fileUtil.createDirectory(eq(parentDir.getAbsolutePath()), anyString())).thenReturn(dataDir);
        when(supportFilePath.getPaths()).thenReturn(new WindowsSupportFilePathsStrategy(false));

        when(executor.execute(any(IosListFilesCommand.class)))
                .thenReturn("log1.log\nfailme.txt");

        File file1 = new File(dataDir, "log1.log");
        File file2 = new File(dataDir, "failme.txt");

        when(fileUtil.createFile(file1.getAbsolutePath())).thenReturn(file1);
        when(fileUtil.createFile(file2.getAbsolutePath())).thenReturn(file2);
        when(fileUtil.zipAndDeleteMobileDataFolder(dataDir.getAbsolutePath()))
                .thenReturn(new File("/tmp/partial.zip"));

        File result = testResultsService.pullAndZipMobileDataFolder(device, "com.example.app");

        assertNotNull(result);
        verify(executor, times(2)).execute(any(IosCopyFileContentCommand.class));
        verify(fileUtil, times(1)).zipAndDeleteMobileDataFolder(anyString());
    }

    @Test
    void testPullAndZipMobileDataFolderExceptionInTopLevel() {
        when(fileUtil.createDirectory(anyString(), anyString())).thenThrow(new RuntimeException("Disk full"));
        when(supportFilePath.getPaths()).thenReturn(new WindowsSupportFilePathsStrategy(false));

        File result = testResultsService.pullAndZipMobileDataFolder(device, "com.example.app");

        assertNull(result);
        verify(fileUtil, never()).zipAndDeleteMobileDataFolder(anyString());
    }
}
