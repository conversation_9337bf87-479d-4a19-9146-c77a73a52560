plugins {
    id 'java'
    id 'org.springframework.boot' version '3.0.5'
    id 'io.spring.dependency-management' version '1.0.11.RELEASE'
    id 'org.openjfx.javafxplugin' version '0.0.13'
    id 'maven-publish'
    id "io.freefair.lombok" version "6.5.0.3"
    id 'checkstyle'
}

group = 'com.phonecheck'
version = '1.0.0'

repositories {
    mavenLocal()
    mavenCentral()
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'

    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'org.apache.poi:poi-ooxml:4.1.1'
    implementation 'commons-io:commons-io:2.16.1'

    implementation 'io.netty:netty-codec-mqtt:4.1.78.Final'
    implementation 'org.controlsfx:controlsfx:9.0.0'
    implementation 'com.jfoenix:jfoenix:9.0.1'
    implementation 'de.jensd:fontawesomefx-materialdesignfont:2.0.26-9.1.2'
    implementation 'org.openjfx:javafx-web:20.0.1'
    implementation group: 'com.google.code.gson', name: 'gson', version: '2.10.1'

    implementation group: 'net.sourceforge.barbecue', name: 'barbecue', version: '1.5-beta1'
    implementation "com.google.zxing:core:3.3.0"

    implementation project(':model')
    implementation project(':commands-module')
    implementation project(':cloud-api-module')

    // https://mvnrepository.com/artifact/org.json/json
    implementation group: 'org.json', name: 'json', version: '20240303'


    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation "org.testfx:testfx-core:4.0.16-alpha"
    testImplementation "org.testfx:testfx-junit5:4.0.16-alpha"
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testImplementation 'org.testfx:testfx-core:4.0.16-alpha'
    testImplementation 'org.testfx:testfx-junit5:4.0.16-alpha'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'
    testImplementation 'org.mockito:mockito-core:4.6.1'
    testImplementation 'org.mockito:mockito-junit-jupiter:4.6.1'
}

test {
    useJUnitPlatform()
    jvmArgs '--add-exports', 'javafx.graphics/com.sun.javafx.application=ALL-UNNAMED'
}

javafx {
    modules = ['javafx.controls', 'javafx.fxml', 'javafx.web', 'javafx.swing']
    version = '19.0.2.1'
}