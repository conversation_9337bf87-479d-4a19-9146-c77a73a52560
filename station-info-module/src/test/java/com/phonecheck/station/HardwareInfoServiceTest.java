package com.phonecheck.station;

import com.phonecheck.command.system.mac.GetMacHardwareInfoCommand;
import com.phonecheck.command.system.windows.GetWindowsDriveSerialCommand;
import com.phonecheck.command.system.windows.GetWindowsHardwareIdCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.system.mac.GetMacHardwareIdParser;
import com.phonecheck.parser.system.windows.GetWindowsDriveSerialParser;
import com.phonecheck.parser.system.windows.GetWindowsHardwareIdParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class HardwareInfoServiceTest {
    @Mock
    private CommandExecutor executor;
    @Mock
    private GetMacHardwareIdParser getMacHardwareIdParser;
    @Mock
    private GetWindowsHardwareIdParser windowsHardwareIdParser;
    @Mock
    private GetWindowsDriveSerialParser windowsDriveSerialParser;
    @Mock
    private OsChecker osChecker;
    private HardwareInfoService hardwareInfoService;
    private final String outputString = "output value";

    @BeforeEach
    void setup() {
        hardwareInfoService = new HardwareInfoService(executor,
                getMacHardwareIdParser,
                windowsHardwareIdParser,
                windowsDriveSerialParser,
                osChecker);
    }

    @Test
    public void getHardwareIdTest1() throws IOException {
        if (System.getProperty("os.name").startsWith("Mac")) {
            when(executor.execute(any(GetMacHardwareInfoCommand.class))).thenReturn(outputString);
            when(getMacHardwareIdParser.parse(eq(outputString))).thenReturn("uuid");
            when(osChecker.isMac()).thenReturn(true);

            String hardwareUuid = hardwareInfoService.getPcId();

            verify(executor).execute(any(GetMacHardwareInfoCommand.class));
            verify(getMacHardwareIdParser).parse(eq(outputString));
            assertEquals("uuid", hardwareUuid);
        } else {
            when(executor.execute(any(GetWindowsHardwareIdCommand.class))).thenReturn(outputString);
            when(windowsHardwareIdParser.parse(eq(outputString))).thenReturn("uuid");

            String hardwareUuid = hardwareInfoService.getPcId();

            verify(executor).execute(any(GetWindowsHardwareIdCommand.class));
            verify(windowsHardwareIdParser).parse(eq(outputString));
            assertEquals("uuid", hardwareUuid);
        }
    }

    @Test
    public void getHardwareIdTest2() throws IOException {
        if (System.getProperty("os.name").startsWith("Windows")) {
            when(executor.execute(any(GetWindowsHardwareIdCommand.class))).thenReturn(outputString);
            when(windowsHardwareIdParser.parse(eq(outputString))).thenReturn(null);
            when(executor.execute(any(GetWindowsDriveSerialCommand.class))).thenReturn(outputString);
            when(windowsDriveSerialParser.parse(eq(outputString))).thenReturn("uuid");

            String hardwareUuid = hardwareInfoService.getPcId();

            verify(executor).execute(any(GetWindowsHardwareIdCommand.class));
            verify(windowsHardwareIdParser).parse(eq(outputString));
            verify(executor).execute(any(GetWindowsDriveSerialCommand.class));
            verify(windowsDriveSerialParser).parse(eq(outputString));
            assertEquals("uuid", hardwareUuid);
        }
    }
}
