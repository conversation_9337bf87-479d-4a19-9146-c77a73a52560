package com.phonecheck.communicator.redis;

import com.phonecheck.command.system.mac.GetMacProcessIdWithPortCommand;
import com.phonecheck.command.system.mac.MacKillProcessCommand;
import com.phonecheck.command.system.mac.StartMacRedisServerCommand;
import com.phonecheck.command.system.windows.GetWindowsProcessIdWithPortCommand;
import com.phonecheck.command.system.windows.StartWindowsRedisServerCommand;
import com.phonecheck.command.system.windows.WindowsKillProcessCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.system.mac.GetMacProcessIdParser;
import com.phonecheck.parser.system.windows.GetWindowsProcessIdParser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class RedisService {
    private static final Logger LOGGER = LoggerFactory.getLogger(RedisService.class);

    private final int redisPort;
    private final CommandExecutor executor;
    private final OsChecker osChecker;
    private final GetMacProcessIdParser macProcessIdParser;
    private final GetWindowsProcessIdParser windowsProcessIdParser;

    public RedisService(
            @Qualifier("redisPort") final int redisPort,
            final CommandExecutor executor,
            final OsChecker osChecker,
            final GetMacProcessIdParser macProcessIdParser,
            final GetWindowsProcessIdParser windowsProcessIdParser
    ) {
        this.redisPort = redisPort;
        this.executor = executor;
        this.osChecker = osChecker;
        this.macProcessIdParser = macProcessIdParser;
        this.windowsProcessIdParser = windowsProcessIdParser;
    }

    /**
     * Method to start the redis server
     */
    public void startRedisServer() {
        new Thread(() -> {
            try {
                if (osChecker.isMac()) {
                    String processId = macProcessIdParser.parse(
                            executor.execute(new GetMacProcessIdWithPortCommand(String.valueOf(redisPort))));
                    if (StringUtils.isBlank(processId)) {
                        LOGGER.info("Starting Redis communicator server...");

                        executor.execute(new StartMacRedisServerCommand());
                    }
                } else {
                    String processId = windowsProcessIdParser.parse(
                            executor.execute(new GetWindowsProcessIdWithPortCommand(String.valueOf(redisPort))));
                    if (StringUtils.isBlank(processId)) {
                        LOGGER.info("Starting Redis communicator server...");

                        executor.execute(new StartWindowsRedisServerCommand());
                    }
                }
            } catch (IOException e) {
                LOGGER.error(
                        "Exception occurred while starting redis communicator server at port: {}",
                        redisPort,
                        e
                );
            }
        }).start();
    }

    /**
     * Method to kill the redis server
     */
    public void killRedisServer() {
        if (osChecker.isMac()) {
            killMacRedisServer();
        } else {
            killWindowsRedisServer();
        }
    }

    /**
     * Method to kill the redis server for macOS
     */
    private void killMacRedisServer() {
        try {
            String processId = macProcessIdParser.parse(
                    executor.execute(new GetMacProcessIdWithPortCommand(String.valueOf(redisPort))));
            if (StringUtils.isNotBlank(processId)) {
                String killProcessOutput = executor.execute(new MacKillProcessCommand(processId));
                if (StringUtils.isBlank(killProcessOutput) || killProcessOutput.equals("0")) {
                    LOGGER.info("Redis communicator server killed successfully");
                } else {
                    LOGGER.warn("Killing redis communicator server failed");
                }
            }
        } catch (IOException e) {
            LOGGER.error("Exception occurred while killing redis communicator server", e);
        }
    }

    /**
     * Method to kill the redis server for windows
     */
    private void killWindowsRedisServer() {
        try {
            String processId = windowsProcessIdParser.parse(
                    executor.execute(new GetWindowsProcessIdWithPortCommand(String.valueOf(redisPort))));
            if (StringUtils.isNotBlank(processId)) {
                String killProcessOutput = executor.execute(new WindowsKillProcessCommand(processId));
                if (StringUtils.containsIgnoreCase(killProcessOutput, "success")) {
                    LOGGER.info("Redis communicator server killed successfully.");
                } else {
                    LOGGER.warn("Killing redis communicator server failed.");
                }
            }
        } catch (IOException e) {
            LOGGER.error("Exception occurred while killing redis communicator server", e);
        }
    }
}
