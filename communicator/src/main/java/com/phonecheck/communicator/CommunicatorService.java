package com.phonecheck.communicator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonecheck.communicator.redis.RedisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

@Service
public class CommunicatorService {
    private final int redisPort;
    private final String redisHost;
    private static final Logger LOGGER = LoggerFactory.getLogger(CommunicatorService.class);
    private final ObjectMapper objectMapper;
    private final RedisService redisService;

    public CommunicatorService(
            @Qualifier("redisPort") final int redisPort,
            @Qualifier("redisHost") final String redisHost,
            final ObjectMapper objectMapper,
            final RedisService redisService
    ) {
        this.redisPort = redisPort;
        this.redisHost = redisHost;
        this.objectMapper = objectMapper;
        this.redisService = redisService;
    }

    /**
     * Method to save provided data object against the provided key
     * by converting it in json
     *
     * @param key  data key
     * @param data payload
     */
    public void setData(final String key, final Object... data) {
        Jedis jedis = new Jedis(redisHost, redisPort);

        try {
            jedis.connect();
            jedis.set(key, objectMapper.writeValueAsString(data[0]));
            LOGGER.info("Value written to Redis.");
            jedis.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while setting data for key ({}) in jedis.", key, e);

            if (data.length != 2) {
                redisService.killRedisServer();
                redisService.startRedisServer();

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    // do nothing
                }

                setData(key, data[0], true); // True means we need to restart the redis server
            }
        }
    }

    /**
     * Method to retrieve the data from redis db saved against the
     * provided key and delete the data afterwards
     *
     * @param key data key
     * @return payload json string
     */
    public String getData(final String key) {
        String data = null;

        Jedis jedis = new Jedis(redisHost, redisPort);

        try {
            redisService.startRedisServer();
            jedis.connect();
            data = jedis.get(key);
            jedis.unlink(key);
            jedis.close();
        } catch (Exception e) {
            LOGGER.error("Error occurred while reading data for key ({}) from jedis.", key, e);
        }

        return data;
    }
}
