package com.phonecheck.communicator;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CommunicatorConfig {
    @Value("${communicator.redis.port}")
    private int redisPort;

    @Value("${communicator.redis.host}")
    private String redisHost;

    @Bean("redisPort")
    public int redisPort() {
        return redisPort;
    }

    @Bean("redisHost")
    public String redisHost() {
        return redisHost;
    }
}
