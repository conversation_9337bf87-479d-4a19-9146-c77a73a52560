package com.phonecheck.print.service;

import com.phonecheck.command.print.mac.MacPrintWithLpCommand;
import com.phonecheck.command.print.windows.WindowsPrintCommand;
import com.phonecheck.command.print.windows.WindowsPrintWithBrotherPrinterCommand;
import com.phonecheck.command.print.windows.WindowsPrintWithDymoPrinterCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.customization.AutomationStepsStatus;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.print.PrintOperation;
import com.phonecheck.model.print.Printer;
import com.phonecheck.model.print.dymo.DymoPaperTray;
import com.phonecheck.model.print.label.LabelOrientation;
import com.phonecheck.model.print.label.LabelVariant;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.print.util.PdfUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * Service to handle all the print related operations i.e. queuing print operations,
 * saving label image, calculating printer dpi etc.
 */
@Service
public class LabelPrintService {
    private static final Logger LOGGER = LoggerFactory.getLogger(LabelPrintService.class);
    private static final int PRINT_OPERATION_PROCESSING_INTERVAL = 1500; // Milliseconds
    private static final String NOT_FOUND = "Not Found";
    private static final String ERROR_DYMO_SDK_FAILED = "SDK failed to print image through printer";
    private static final String ERROR_DYMO_PRINT_IMAGE_FAILED = "Error while printing image";

    private final CommandExecutor executor;
    private final BlockingQueue<PrintOperation> printOperationQueue;
    private boolean keepProcessingPrintOperations = true;
    private final FileUtil fileUtil;
    private final PdfUtil pdfUtil;
    private final OsChecker osChecker;

    public LabelPrintService(final CommandExecutor executor,
                             final FileUtil fileUtil,
                             final PdfUtil pdfUtil,
                             final OsChecker osChecker) {
        this.executor = executor;
        this.fileUtil = fileUtil;
        this.pdfUtil = pdfUtil;
        this.osChecker = osChecker;

        printOperationQueue = new LinkedBlockingQueue<>();
        startProcessingPrintOperations();
    }

    /**
     * Method to start Processing Label Print Operations
     */
    private void startProcessingPrintOperations() {
        new Thread(() -> {
            while (keepProcessingPrintOperations) {
                try {
                    PrintOperation printOperation = printOperationQueue.take();

                    MDC.put("id", printOperation.getDevice() == null ?
                            "NO-ID" : printOperation.getDevice().getId());
                    performPrintOperation(printOperation);
                    MDC.clear();

                    Thread.sleep(PRINT_OPERATION_PROCESSING_INTERVAL);
                } catch (Exception e) {
                    LOGGER.error("Exception occurred while processing print operations from queue", e);
                }
            }
        }).start();
    }

    /**
     * Method to stop label print operations by setting
     * keepProcessingPrintOperations flag to false
     */
    public void stopProcessingPrintOperations() {
        keepProcessingPrintOperations = false;
    }


    /**
     * Method to enqueue the label print operation
     *
     * @param printOperation object that contains the details of label
     *                       print operation e.g. print to pdf etc.
     */
    public void enqueuePrintOperation(final PrintOperation printOperation) {
        printOperationQueue.add(printOperation);
    }

    /**
     * Method that performs the operations to print label i.e.
     * loads label controller -> saves label image -> performs print operation
     *
     * @param printOperation object that contains the details of label
     *                       print operation e.g. print to pdf etc.
     */
    private void performPrintOperation(final PrintOperation printOperation) {
        try {
            final String printerName;
            if (StringUtils.isNotBlank(printOperation.getPrinterName())) {
                printerName = printOperation.getPrinterName();
            } else {
                printerName = NOT_FOUND;
            }

            // Print label as pdf and open file
            if (printOperation.isPrintToPdf()) {
                if (osChecker.isMac()) {
                    // Command for mac
                    pdfUtil.saveAndPrintToPdfForMac(printOperation.getLabelImagePath());
                } else {
                    // Command for windows
                    pdfUtil.saveAndPrintToPdfForWindows(printOperation.getLabelImagePath());
                }
            } else {
                if (!printerName.equalsIgnoreCase(NOT_FOUND)) {
                    if (osChecker.isMac()) {
                        // We only need LP command(CUPS) to print for Mac

                        String lpCommandOptions;
                        if (Printer.DYMO.equals(Printer.getByName(printerName))) {
                            lpCommandOptions = printOperation.getLpCommandOptionsDymo();
                        } else if (Printer.BROTHER.equals(Printer.getByName(printerName))) {
                            lpCommandOptions = printOperation.getLpCommandOptionsBrother();
                        } else {
                            lpCommandOptions = printOperation.getLpCommandOptionsZebra();
                        }

                        if (lpCommandOptions != null) {
                            String output = executor.execute(
                                    new MacPrintWithLpCommand(
                                            printerName,
                                            printOperation.getLabelImagePath(),
                                            lpCommandOptions
                                    )
                            );

                            LOGGER.info("""
                                            Printing label through LP command:
                                             \t Label Name: {}
                                             \t Printer Name: {}
                                             \t Orientation: {}
                                             \t CloudRollOrientation: {}
                                             \t LP Command Options: {}
                                             \t Command output: {}""",
                                    printOperation.getLabelName(), printerName,
                                    printOperation.getLabelOrientation(), printOperation.getCloudRollOrientation(),
                                    lpCommandOptions, output);
                        } else {
                            LOGGER.info("Print failed, no supported LP options available for printer: {} ",
                                    printerName);
                        }
                    } else {
                        String output;
                        String labelName = printOperation.getLabelName();
                        if (Printer.BROTHER.equals(Printer.getByName(printerName))) {
                            output = executor.execute(
                                    new WindowsPrintWithBrotherPrinterCommand(
                                            printOperation.getLabelImagePath(),
                                            printerName,
                                            printOperation.getBrotherLabelConfigFileName(),
                                            printOperation.getPaperType().getValue()
                                    )
                            );
                        } else if (Printer.DYMO.equals(Printer.getByName(printerName))) {
                            if (printOperation.getCloudRollOrientation() == null) {
                                if (Objects.equals(labelName, LabelVariant.LABEL_2x1.getName())) {
                                    printOperation.setCloudRollOrientation(LabelOrientation.LANDSCAPE);
                                } else {
                                    printOperation.setCloudRollOrientation(LabelOrientation.PORTRAIT);
                                }
                            }
                            output = executor.execute(
                                    new WindowsPrintWithDymoPrinterCommand(
                                            printOperation.getLabelImagePath(),
                                            printerName,
                                            printOperation.getDymoLabelConfigFileName(),
                                            printOperation.getDymoPaperTray() != null
                                                    ? printOperation.getDymoPaperTray().getSlotNo()
                                                    : StringUtils.EMPTY
                                    )
                            );

                            if (StringUtils.containsIgnoreCase(output, ERROR_DYMO_SDK_FAILED) ||
                                    StringUtils.containsIgnoreCase(output, ERROR_DYMO_PRINT_IMAGE_FAILED)) {
                                output = executor.execute(
                                        new WindowsPrintCommand(
                                                printerName,
                                                printOperation.getLabelImagePath(),
                                                printOperation.getWidth(),
                                                printOperation.getHeight(),
                                                printOperation.getLabelSizeUnit(),
                                                printOperation.getCloudRollOrientation(),
                                                (printOperation.getDymoPaperTray() != null
                                                        && (printOperation.getDymoPaperTray() == DymoPaperTray.RIGHT_P1
                                                        || printOperation.getDymoPaperTray() == DymoPaperTray.RIGHT_P2))
                                                        ? "2" : "1"
                                        )
                                );
                            }
                        } else {
                            if (printOperation.getCloudRollOrientation() == null) {
                                if (Objects.equals(labelName, LabelVariant.BRANDED_LABEL_LANDSCAPE_2X4.getName())) {
                                    printOperation.setCloudRollOrientation(LabelOrientation.PORTRAIT);
                                } else if (Objects.equals(labelName, LabelVariant.BRANDED_LABEL_PORTRAIT.getName())
                                        || Objects.equals(labelName, LabelVariant.LPN_LABEL_PORTRAIT.getName())) {
                                    printOperation.setCloudRollOrientation(LabelOrientation.LANDSCAPE);
                                }
                            }
                            output = executor.execute(
                                    new WindowsPrintCommand(
                                            printerName,
                                            printOperation.getLabelImagePath(),
                                            printOperation.getWidth(),
                                            printOperation.getHeight(),
                                            printOperation.getLabelSizeUnit(),
                                            printOperation.getCloudRollOrientation(),
                                            StringUtils.EMPTY
                                    )
                            );
                        }
                        LOGGER.info("""
                                        Printing label with following params...
                                         \t Command Output: {}
                                         \t Printer Name: {}
                                         \t Label Name: {}
                                         \t Width = {}, Height = {}, SizeUnit = {}
                                         \t DymoPaperTray: {}
                                         \t BrotherConfigFile: {}
                                         \t BrotherPaperType: {}
                                         \t CloudRollOrientation = {}, Orientation = {}""",
                                output, printerName, printOperation.getLabelName(),
                                printOperation.getWidth(), printOperation.getHeight(),
                                printOperation.getLabelSizeUnit(), printOperation.getDymoPaperTray(),
                                printOperation.getBrotherLabelConfigFileName(),
                                printOperation.getPaperType(),
                                printOperation.getCloudRollOrientation(), printOperation.getLabelOrientation());
                    }
                } else {
                    LOGGER.error("Printer was not found connected.");
                }
            }

            // Set status automation workflow print step to SUCCESS, if this is called from respective automation
            setPrintAutomationStepStatus(printOperation.getDevice(), AutomationStepsStatus.SUCCESS);

            fileUtil.deleteFile(printOperation.getLabelImagePath());
        } catch (Exception e) {
            // Set status automation workflow print step to FAILED, if this is called from respective automation
            setPrintAutomationStepStatus(printOperation.getDevice(), AutomationStepsStatus.FAILED);

            // we need to delete the file even if the exception occurs
            fileUtil.deleteFile(printOperation.getLabelImagePath());
            LOGGER.error("Exception occurred while performing print operation", e);
        }
    }

    /**
     * Set status automation workflow print step to either SUCCESS or failed,
     * if this print operation is called from some automation
     *
     * @param device                       target device
     * @param newPrintAutomationStepStatus new automation step status of PRINT
     */
    private void setPrintAutomationStepStatus(final Device device,
                                              final AutomationStepsStatus newPrintAutomationStepStatus) {
        if (device.getCurrentRunningAutomation() != null && device.getAutomationSteps() != null) {
            device.getAutomationSteps().stream()
                    .filter(automationStep ->
                            CloudCustomizationResponse.AutomationSteps.PRINT == automationStep.getStep() &&
                                    AutomationStepsStatus.IN_PROGRESS == automationStep.getStatus())
                    .findFirst()
                    .ifPresent(automationStep -> automationStep.setStatus(newPrintAutomationStepStatus));
        }
    }
}
