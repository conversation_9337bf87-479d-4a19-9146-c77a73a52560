package com.phonecheck.print.service;

import com.phonecheck.command.print.mac.GetMacPrintersListCommand;
import com.phonecheck.command.print.windows.GetWindowsPrintersListCommand;
import com.phonecheck.executor.CommandExecutor;
import com.phonecheck.model.util.OsChecker;
import com.phonecheck.parser.print.mac.MacPrintersListParser;
import com.phonecheck.parser.print.windows.WindowsPrintersListParser;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

/**
 * Service to Get connected printers
 */
@Service
@AllArgsConstructor
public class PrintersListService {
    private static final Logger LOGGER = LoggerFactory.getLogger(PrintersListService.class);

    private final OsChecker osChecker;
    private CommandExecutor executor;
    private final MacPrintersListParser printersListParser;
    private final WindowsPrintersListParser windowsPrintersListParser;

    /**
     * Method to get all the connected printer names list
     *
     * @param localizedPrinterKeyword localized printer keyword
     * @return list of connected printer names
     */
    public List<String> getConnectedPrintersList(final String localizedPrinterKeyword) {
        List<String> connectedPrintersList = null;

        try {
            if (osChecker.isMac()) {
                final String printersListCommandOutput = executor.execute(new GetMacPrintersListCommand(
                        localizedPrinterKeyword)
                );

                LOGGER.info("Connected printers list command output: {}", printersListCommandOutput);
                connectedPrintersList = printersListParser.parse(printersListCommandOutput, localizedPrinterKeyword);
            } else {
                final String printersListCommandOutput = executor.execute(new GetWindowsPrintersListCommand());

                LOGGER.info("Connected printers list command output: {}", printersListCommandOutput);
                connectedPrintersList = windowsPrintersListParser.parse(printersListCommandOutput);
            }
        } catch (IOException e) {
            LOGGER.error("Exception occurred while getting connected printers list.", e);
        }

        return connectedPrintersList;
    }
}
