package com.phonecheck.print.util;

import com.phonecheck.command.print.mac.MacPrintToPdfCommand;
import com.phonecheck.command.print.mac.OpenFileCommand;
import com.phonecheck.command.print.windows.WindowsPrintToPdfCommand;
import com.phonecheck.executor.CommandExecutor;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

@Component
@AllArgsConstructor
public class PdfUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(PdfUtil.class);
    private final CommandExecutor executor;

    /**
     * Opens a PDF file located at the given path using the default application
     * associated with PDF files in the system.
     *
     * @param filePath The absolute path of the PDF file to be opened.
     */
    private void openPdfFile(final String filePath) {
        try {
            executor.execute(new OpenFileCommand(filePath));
        } catch (IOException e) {
            LOGGER.error("Failed to open PDF file from path: {}", filePath, e);
        }
    }

    /**
     * Saves the input file as a PDF and opens it on the default PDF viewer.
     *
     * @param sourceFilePath the path to the file to be saved as PDF.
     */
    public void saveAndPrintToPdfForMac(final String sourceFilePath) {
        File sourceFile = new File(sourceFilePath);
        try {
            String absolutePath = sourceFile.getAbsolutePath();
            String destinationFilePath = getPdfFilePathOnDesktop(absolutePath);

            String output = executor.execute(new MacPrintToPdfCommand(absolutePath, destinationFilePath));
            LOGGER.info("Print to PDF command output for Mac: {}", output);
            // Open created file from path
            openPdfFile(destinationFilePath);
        } catch (Exception e) {
            LOGGER.error("Failed to create and open PDF of label", e);
        }
    }

    /**
     * Saves the input file as a PDF and opens it on the default PDF viewer.
     *
     * @param imagePath the path to the file to be saved as PDF.
     */
    public void saveAndPrintToPdfForWindows(final String imagePath) {
        File imageSourceFile = new File(imagePath);
        try {
            String imageAbsolutePath = imageSourceFile.getAbsolutePath();

            // Copy the file to the desktop
            String tempPdfPath = imagePath.substring(0, imagePath.lastIndexOf('.')) + ".pdf";

            String output = executor.execute(
                    new WindowsPrintToPdfCommand(tempPdfPath, imageAbsolutePath)
            );

            LOGGER.info("Print to PDF command output for windows: {}", output);

            String destinationFilePath = getPdfFilePathOnDesktop(imageAbsolutePath);

            Files.copy(
                    Path.of(tempPdfPath),
                    Path.of(destinationFilePath),
                    StandardCopyOption.REPLACE_EXISTING
            );
        } catch (IOException e) {
            LOGGER.error("Failed to create and open PDF of label", e);
        }
    }

    /**
     * Method to get the pdf file path on desktop
     *
     * @param sourceFilePath pdf source file path
     * @return destination pdf path
     */
    private String getPdfFilePathOnDesktop(final String sourceFilePath) {
        int lastSeparatorIndex = sourceFilePath.lastIndexOf(File.separator);
        String fileNameWithoutExtension = sourceFilePath.substring(
                lastSeparatorIndex + 1,
                sourceFilePath.lastIndexOf('.')
        );

        String completeFileName = fileNameWithoutExtension + ".pdf";

        File desktopLoc = new File(System.getProperty("user.home") + File.separator + "Desktop");

        // On windows, if the user has signed in with a microsoft account,
        // then the desktop location is on OneDrive. In that case, add OneDrive to the path.
        String desktopPath = desktopLoc.exists() && desktopLoc.isDirectory() ?
                System.getProperty("user.home") + File.separator + "Desktop" :
                System.getProperty("user.home") + File.separator + "OneDrive" + File.separator + "Desktop";
        return desktopPath + File.separator + completeFileName;
    }
}
