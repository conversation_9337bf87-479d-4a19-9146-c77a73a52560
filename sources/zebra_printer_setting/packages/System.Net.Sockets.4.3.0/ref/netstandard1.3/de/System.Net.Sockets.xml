<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Sockets</name>
  </assembly>
  <members>
    <member name="T:System.Net.Sockets.ProtocolType">
      <summary>Gibt die Protokolle an, die von der <see cref="T:System.Net.Sockets.Socket" />-Klasse unterstützt werden.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Tcp">
      <summary>Transmission Control Protocol.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Udp">
      <summary>User Datagram-Protokoll.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unknown">
      <summary>Unbekanntes Protokoll.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unspecified">
      <summary>Nicht definiertes Protokoll.</summary>
    </member>
    <member name="T:System.Net.Sockets.Socket">
      <summary>Implement<PERSON>t die Berkeley-Sockets-Schnittstelle.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Sockets.Socket" />-Klasse unter Verwendung der angegebenen Adressfamilie sowie des angegebenen Sockettyps und Protokolls.</summary>
      <param name="addressFamily">Einer der <see cref="T:System.Net.Sockets.AddressFamily" />-Werte. </param>
      <param name="socketType">Einer der <see cref="T:System.Net.Sockets.SocketType" />-Werte. </param>
      <param name="protocolType">Einer der <see cref="T:System.Net.Sockets.ProtocolType" />-Werte. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Die Kombination von <paramref name="addressFamily" />, <paramref name="socketType" /> und <paramref name="protocolType" /> führt zu einem ungültigen Socket. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Net.Sockets.Socket" />-Klasse unter Verwendung der angegebenen Sockettyps und Protokolls.</summary>
      <param name="socketType">Einer der <see cref="T:System.Net.Sockets.SocketType" />-Werte.</param>
      <param name="protocolType">Einer der <see cref="T:System.Net.Sockets.ProtocolType" />-Werte.</param>
      <exception cref="T:System.Net.Sockets.SocketException">Die Kombination von <paramref name="socketType" /> und <paramref name="protocolType" /> führt zu einem ungültigen Socket. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Beginnt einen asynchronen Vorgang, um eine eingehende Verbindung anzunehmen.</summary>
      <returns>Gibt true zurück, wenn der E/A-Vorgang aussteht.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nach dem Abschluss des Vorgangs ausgelöst.Gibt false zurück, wenn der E/A-Vorgang synchron abgeschlossen wurde.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nicht ausgelöst, und das als Parameter übergebene <paramref name="e" />-Objekt kann direkt nach der Rückgabe des Methodenaufrufs untersucht werden, um die Ergebnisse des Vorgangs abzurufen.</returns>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das für diesen asynchronen Socketvorgang verwendet werden soll.</param>
      <exception cref="T:System.ArgumentException">Ein Argument ist ungültig.Diese Ausnahme tritt auf, wenn der bereitgestellte Puffer nicht groß genug ist.Der Puffer muss wenigstens 2 * (sizeof(SOCKADDR_STORAGE + 16) Bytes betragen.Diese Ausnahme tritt auch auf, wenn mehrere Puffer angegeben werden und die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />-Eigenschaft nicht NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Ein Argument liegt außerhalb des gültigen Bereichs.Die Ausnahme tritt auf, wenn <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Count" /> kleiner als 0 ist.</exception>
      <exception cref="T:System.InvalidOperationException">Es wurde eine ungültige Operation angefordert.Diese Ausnahme tritt auf, wenn der annehmende <see cref="T:System.Net.Sockets.Socket" /> keine Verbindungen überwacht oder der angenommene Socket gebunden ist.Sie müssen die <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" />-Methode und die <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" />-Methode aufrufen, bevor Sie die <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" />-Methode aufrufen.Diese Ausnahme tritt auch auf, wenn der Socket bereits verbunden ist oder bereits ein Socketvorgang mit dem angegebenen <paramref name="e" />-Parameter ausgeführt wird. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.NotSupportedException">Für diese Methode ist Windows XP oder höher erforderlich.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.AddressFamily">
      <summary>Ruft die Adressfamilie des <see cref="T:System.Net.Sockets.Socket" /> ab.</summary>
      <returns>Einer der <see cref="T:System.Net.Sockets.AddressFamily" />-Werte.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)">
      <summary>Ordnet einem <see cref="T:System.Net.Sockets.Socket" /> einen lokalen Endpunkt zu.</summary>
      <param name="localEP">Der lokale <see cref="T:System.Net.EndPoint" />, der dem <see cref="T:System.Net.Sockets.Socket" /> zugeordnet werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localEP" /> ist null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Security.SecurityException">Ein in der Aufrufliste übergeordneter Aufrufer hat keine Berechtigung für den angeforderten Vorgang. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.SocketPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.CancelConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Bricht eine asynchrone Anforderung einer Remotehostverbindung ab.</summary>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das verwendet wurde, um die Verbindung mit dem Remotehost durch Aufrufen einer der <see cref="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)" />-Methoden anzufordern.</param>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="e" />-Parameter kann nicht NULL und der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> kann nicht NULL sein.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket. </exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Security.SecurityException">Ein in der Aufrufliste übergeordneter Aufrufer hat keine Berechtigung für den angeforderten Vorgang.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Beginnt eine asynchrone Anforderung einer Verbindung mit einem Remotehost.</summary>
      <returns>Gibt true zurück, wenn der E/A-Vorgang aussteht.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nach dem Abschluss des Vorgangs ausgelöst.Gibt false zurück, wenn der E/A-Vorgang synchron abgeschlossen wurde.In diesem Fall wird das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter nicht ausgelöst, und das als Parameter übergebene <paramref name="e" />-Objekt kann direkt nach der Rückgabe des Methodenaufrufs untersucht werden, um die Ergebnisse des Vorgangs abzurufen.</returns>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das für diesen asynchronen Socketvorgang verwendet werden soll.</param>
      <exception cref="T:System.ArgumentException">Ein Argument ist ungültig.Diese Ausnahme tritt auf, wenn mehrere Puffer angegeben werden und die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />-Eigenschaft nicht NULL ist.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="e" />-Parameter kann nicht NULL und der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> kann nicht NULL sein.</exception>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Net.Sockets.Socket" /> führt eine Überwachung durch, oder ein Socketvorgang wird bereits mit dem im <paramref name="e" />-Parameter angegebenen <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt ausgeführt.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.NotSupportedException">Für diese Methode ist Windows XP oder höher erforderlich.Diese Ausnahme tritt auch auf, wenn der lokale Endpunkt und der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> nicht die gleiche Adressfamilie aufweisen.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Security.SecurityException">Ein in der Aufrufliste übergeordneter Aufrufer hat keine Berechtigung für den angeforderten Vorgang.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Beginnt eine asynchrone Anforderung einer Verbindung mit einem Remotehost.</summary>
      <returns>Gibt true zurück, wenn der E/A-Vorgang aussteht.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nach dem Abschluss des Vorgangs ausgelöst.Gibt false zurück, wenn der E/A-Vorgang synchron abgeschlossen wurde.In diesem Fall wird das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter nicht ausgelöst, und das als Parameter übergebene <paramref name="e" />-Objekt kann direkt nach der Rückgabe des Methodenaufrufs untersucht werden, um die Ergebnisse des Vorgangs abzurufen.</returns>
      <param name="socketType">Einer der <see cref="T:System.Net.Sockets.SocketType" />-Werte.</param>
      <param name="protocolType">Einer der <see cref="T:System.Net.Sockets.ProtocolType" />-Werte.</param>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das für diesen asynchronen Socketvorgang verwendet werden soll.</param>
      <exception cref="T:System.ArgumentException">Ein Argument ist ungültig.Diese Ausnahme tritt auf, wenn mehrere Puffer angegeben werden und die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />-Eigenschaft nicht NULL ist.</exception>
      <exception cref="T:System.ArgumentNullException">Der <paramref name="e" />-Parameter kann nicht NULL und der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> kann nicht NULL sein.</exception>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Net.Sockets.Socket" /> führt eine Überwachung durch, oder ein Socketvorgang wird bereits mit dem im <paramref name="e" />-Parameter angegebenen <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt ausgeführt.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.NotSupportedException">Für diese Methode ist Windows XP oder höher erforderlich.Diese Ausnahme tritt auch auf, wenn der lokale Endpunkt und der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> nicht die gleiche Adressfamilie aufweisen.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Security.SecurityException">Ein in der Aufrufliste übergeordneter Aufrufer hat keine Berechtigung für den angeforderten Vorgang.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Connected">
      <summary>Ruft einen Wert ab, der angibt, ob ein <see cref="T:System.Net.Sockets.Socket" /> mit dem Remotehost des letzten <see cref="Overload:System.Net.Sockets.Socket.Send" />-Vorgangs oder <see cref="Overload:System.Net.Sockets.Socket.Receive" />-Vorgangs verbunden ist.</summary>
      <returns>true, wenn <see cref="T:System.Net.Sockets.Socket" /> beim letzten Vorgang mit einer Remoteressource verbunden war, andernfalls false.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Net.Sockets.Socket" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Net.Sockets.Socket" /> verwendeten, nicht verwalteten Ressourcen frei und verwirft optional auch die verwalteten Ressourcen.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben. false, wenn ausschließlich nicht verwaltete Ressourcen freigegeben werden sollen. </param>
    </member>
    <member name="M:System.Net.Sockets.Socket.Finalize">
      <summary>Gibt von der <see cref="T:System.Net.Sockets.Socket" />-Klasse verwendete Ressourcen frei.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Listen(System.Int32)">
      <summary>Versetzt einen <see cref="T:System.Net.Sockets.Socket" /> in den Überwachungszustand.</summary>
      <param name="backlog">Die maximale Länge der Warteschlange für ausstehende Verbindungen. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.LocalEndPoint">
      <summary>Ruft den lokalen Endpunkt ab.</summary>
      <returns>Der <see cref="T:System.Net.EndPoint" />, den der <see cref="T:System.Net.Sockets.Socket" /> für die Kommunikation verwendet.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.NoDelay">
      <summary>Ruft einen <see cref="T:System.Boolean" />-Wert ab, der angibt, ob der Stream-<see cref="T:System.Net.Sockets.Socket" /> den Nagle-Algorithmus verwendet, oder legt diesen fest.</summary>
      <returns>false, wenn der <see cref="T:System.Net.Sockets.Socket" /> den Nagle-Algorithmus verwendet, andernfalls true.Die Standardeinstellung ist false.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den <see cref="T:System.Net.Sockets.Socket" />.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv4">
      <summary>Gibt an, ob das zugrunde liegende Betriebssystem und die Netzwerkkarten IPv4 (Internet Protocol, Version 4) unterstützen.</summary>
      <returns>true, wenn das Betriebssystem und die Netzwerkkarten das IPv4-Protokoll unterstützen, andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv6">
      <summary>Gibt an, ob das zugrunde liegende Betriebssystem und die Netzwerkkarten IPv6 (Internet Protocol, Version 6) unterstützen.</summary>
      <returns>true, wenn das Betriebssystem und die Netzwerkkarten das Protokoll IPv6 unterstützen, andernfalls false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.ProtocolType">
      <summary>Ruft den Protokolltyp des <see cref="T:System.Net.Sockets.Socket" /> ab.</summary>
      <returns>Einer der <see cref="T:System.Net.Sockets.ProtocolType" />-Werte.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Startet eine asynchrone Anforderung, um Daten von einem verbundenen <see cref="T:System.Net.Sockets.Socket" />-Objekt zu empfangen.</summary>
      <returns>Gibt true zurück, wenn der E/A-Vorgang aussteht.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nach dem Abschluss des Vorgangs ausgelöst.Gibt false zurück, wenn der E/A-Vorgang synchron abgeschlossen wurde.In diesem Fall wird das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter nicht ausgelöst, und das als Parameter übergebene <paramref name="e" />-Objekt kann direkt nach der Rückgabe des Methodenaufrufs untersucht werden, um die Ergebnisse des Vorgangs abzurufen.</returns>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das für diesen asynchronen Socketvorgang verwendet werden soll.</param>
      <exception cref="T:System.ArgumentException">Ein Argument war ungültig.Die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft oder <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />-Eigenschaft des <paramref name="e" />-Parameters muss auf gültige Puffer verweisen.Eine dieser Eigenschaften kann festgelegt werden, nicht jedoch beide gleichzeitig.</exception>
      <exception cref="T:System.InvalidOperationException">Es wird bereits ein Socketvorgang mit dem im <paramref name="e" />-Parameter angegebenen <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt ausgeführt.</exception>
      <exception cref="T:System.NotSupportedException">Für diese Methode ist Windows XP oder höher erforderlich.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ReceiveBufferSize">
      <summary>Ruft einen Wert ab, der die Größe des Empfangspuffers des <see cref="T:System.Net.Sockets.Socket" /> angibt, oder legt diesen fest.</summary>
      <returns>Ein <see cref="T:System.Int32" />, das die Größe des Empfangspuffer in Bytes enthält.Der Standard ist 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der für einen set-Vorgang angegebene Wert ist kleiner als 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFromAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Beginnt den asynchronen Datenempfang aus dem angegebenen Netzwerkgerät.</summary>
      <returns>Gibt true zurück, wenn der E/A-Vorgang aussteht.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nach dem Abschluss des Vorgangs ausgelöst.Gibt false zurück, wenn der E/A-Vorgang synchron abgeschlossen wurde.In diesem Fall wird das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter nicht ausgelöst, und das als Parameter übergebene <paramref name="e" />-Objekt kann direkt nach der Rückgabe des Methodenaufrufs untersucht werden, um die Ergebnisse des Vorgangs abzurufen.</returns>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das für diesen asynchronen Socketvorgang verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> darf nicht NULL sein.</exception>
      <exception cref="T:System.InvalidOperationException">Es wird bereits ein Socketvorgang mit dem im <paramref name="e" />-Parameter angegebenen <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt ausgeführt.</exception>
      <exception cref="T:System.NotSupportedException">Für diese Methode ist Windows XP oder höher erforderlich.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.RemoteEndPoint">
      <summary>Ruft den Remoteendpunkt ab.</summary>
      <returns>Der <see cref="T:System.Net.EndPoint" />, mit dem der <see cref="T:System.Net.Sockets.Socket" /> kommuniziert.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Sendet Daten asynchron an ein verbundenes <see cref="T:System.Net.Sockets.Socket" />-Objekt.</summary>
      <returns>Gibt true zurück, wenn der E/A-Vorgang aussteht.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nach dem Abschluss des Vorgangs ausgelöst.Gibt false zurück, wenn der E/A-Vorgang synchron abgeschlossen wurde.In diesem Fall wird das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter nicht ausgelöst, und das als Parameter übergebene <paramref name="e" />-Objekt kann direkt nach der Rückgabe des Methodenaufrufs untersucht werden, um die Ergebnisse des Vorgangs abzurufen.</returns>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das für diesen asynchronen Socketvorgang verwendet werden soll.</param>
      <exception cref="T:System.ArgumentException">Die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft oder <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />-Eigenschaft des <paramref name="e" />-Parameters muss auf gültige Puffer verweisen.Eine dieser Eigenschaften kann festgelegt werden, nicht jedoch beide gleichzeitig.</exception>
      <exception cref="T:System.InvalidOperationException">Es wird bereits ein Socketvorgang mit dem im <paramref name="e" />-Parameter angegebenen <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt ausgeführt.</exception>
      <exception cref="T:System.NotSupportedException">Für diese Methode ist Windows XP oder höher erforderlich.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Der <see cref="T:System.Net.Sockets.Socket" /> ist noch nicht verbunden oder wurde nicht über eine <see cref="M:System.Net.Sockets.Socket.Accept" />-<see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" />- oder <see cref="Overload:System.Net.Sockets.Socket.BeginAccept" />-Methode abgerufen.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SendBufferSize">
      <summary>Ruft einen Wert ab, der die Größe des Sendepuffers für den <see cref="T:System.Net.Sockets.Socket" /> angibt, oder legt diesen fest.</summary>
      <returns>Ein <see cref="T:System.Int32" />, das die Größe des Sendepuffer in Bytes enthält.Der Standard ist 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Der für einen set-Vorgang angegebene Wert ist kleiner als 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendToAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Sendet Daten asynchron an einen bestimmten Remotehost.</summary>
      <returns>Gibt true zurück, wenn der E/A-Vorgang aussteht.Das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter wird nach dem Abschluss des Vorgangs ausgelöst.Gibt false zurück, wenn der E/A-Vorgang synchron abgeschlossen wurde.In diesem Fall wird das <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" />-Ereignis für den <paramref name="e" />-Parameter nicht ausgelöst, und das als Parameter übergebene <paramref name="e" />-Objekt kann direkt nach der Rückgabe des Methodenaufrufs untersucht werden, um die Ergebnisse des Vorgangs abzurufen.</returns>
      <param name="e">Das <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt, das für diesen asynchronen Socketvorgang verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> darf nicht NULL sein.</exception>
      <exception cref="T:System.InvalidOperationException">Es wird bereits ein Socketvorgang mit dem im <paramref name="e" />-Parameter angegebenen <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Objekt ausgeführt.</exception>
      <exception cref="T:System.NotSupportedException">Für diese Methode ist Windows XP oder höher erforderlich.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Das angegebene Protokoll ist verbindungsorientiert, aber der <see cref="T:System.Net.Sockets.Socket" /> wurde noch nicht verbunden.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)">
      <summary>Deaktiviert Senden und Empfangen für einen <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="how">Einer der <see cref="T:System.Net.Sockets.SocketShutdown" />-Werte, der den Vorgang angibt, der nicht mehr zulässig ist. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Weitere Informationen finden Sie im Abschnitt Hinweise.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.Ttl">
      <summary>Ruft einen Wert ab, der die Gültigkeitsdauer (TTL) von IP (Internet Protocol)-Paketen angibt, die vom <see cref="T:System.Net.Sockets.Socket" /> gesendet werden.</summary>
      <returns>Der TTL-Wert.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">Für den TTL-Wert kann keine negative Zahl festgelegt werden.</exception>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft kann nur für Sockets in der <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />-Familie oder der <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />-Familie festgelegt werden.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Fehler beim Zugriff auf den Socket.Dieser Fehler wird auch zurückgegeben, wenn versucht wird, TTL auf einen höheren Wert als 255 festzulegen.</exception>
      <exception cref="T:System.ObjectDisposedException">Der <see cref="T:System.Net.Sockets.Socket" /> wurde geschlossen. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncEventArgs">
      <summary>Stellt einen asynchronen Socketvorgang dar.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.#ctor">
      <summary>Erstellt eine leere <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Instanz.</summary>
      <exception cref="T:System.NotSupportedException">Die Plattform wird nicht unterstützt. </exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.AcceptSocket">
      <summary>Ruft den Socket ab, der zum Akzeptieren einer Verbindung mit einer asynchronen Socketmethode erstellt wird, oder legt ihn fest.</summary>
      <returns>Der zu verwendende <see cref="T:System.Net.Sockets.Socket" /> oder der Socket, der zum Akzeptieren einer Verbindung mit einer asynchronen Socketmethode erstellt wird.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer">
      <summary>Ruft den Datenpuffer ab, der mit einer asynchronen Socketmethode verwendet werden soll.</summary>
      <returns>Ein <see cref="T:System.Byte" />-Array, das den Datenpuffer darstellt, der mit einer asynchronen Socketmethode verwendet werden soll.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList">
      <summary>Ruft ein Array von Datenpuffern ab, die mit einer asynchronen Socketmethode verwendet werden sollen, oder legt es fest.</summary>
      <returns>Eine <see cref="T:System.Collections.IList" />, die ein Array von Datenpuffern darstellt, die mit einer asynchronen Socketmethode verwendet werden sollen.</returns>
      <exception cref="T:System.ArgumentException">Für einen set-Vorgang wurden mehrdeutige Puffer angegeben.Diese Ausnahme tritt auf, wenn die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft auf einen Wert ungleich NULL festgelegt wurde und versucht wurde, die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />-Eigenschaft auf einen Wert ungleich NULL festzulegen.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BytesTransferred">
      <summary>Ruft die Anzahl der im Socketvorgang übertragenen Bytes ab.</summary>
      <returns>Ein <see cref="T:System.Int32" /> mit der Anzahl der im Socketvorgang übertragenen Bytes.</returns>
    </member>
    <member name="E:System.Net.Sockets.SocketAsyncEventArgs.Completed">
      <summary>Das Ereignis, das zum Abschließen eines asynchronen Vorgangs verwendet wird.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectByNameError">
      <summary>Ruft die Ausnahme im Fall eines Verbindungsfehlers ab, wenn <see cref="T:System.Net.DnsEndPoint" /> verwendet wurde.</summary>
      <returns>Ein <see cref="T:System.Exception" />, das die Ursache des Verbindungsfehlers angibt, wenn ein <see cref="T:System.Net.DnsEndPoint" /> für die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />-Eigenschaft angegeben wurde.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectSocket">
      <summary>Das erstellte und verbundene <see cref="T:System.Net.Sockets.Socket" />-Objekt nach dem erfolgreichen Beenden der <see cref="Overload:System.Net.Sockets.Socket.ConnectAsync" />-Methode.</summary>
      <returns>Das verbundene <see cref="T:System.Net.Sockets.Socket" />-Objekt.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Count">
      <summary>Ruft die maximale Datenmenge in Bytes ab, die in einem asynchronen Vorgang gesendet oder empfangen wird.</summary>
      <returns>Ein <see cref="T:System.Int32" /> mit der maximalen Datenmenge in Bytes, die gesendet oder empfangen werden soll.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Dispose">
      <summary>Gibt die von der <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Instanz verwendeten nicht verwalteten Ressourcen zurück und verwirft optional die verwalteten Ressourcen.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Finalize">
      <summary>Gibt von der <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />-Klasse verwendete Ressourcen frei.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.LastOperation">
      <summary>Ruft den Typ des Socketvorgangs ab, der zuletzt mit diesem Kontextobjekt ausgeführt wurde.</summary>
      <returns>Eine <see cref="T:System.Net.Sockets.SocketAsyncOperation" />-Instanz, die den Typ des Socketvorgangs angibt, der zuletzt mit diesem Kontextobjekt ausgeführt wurde.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Offset">
      <summary>Ruft den Offset in Bytes im Datenpuffer ab, auf den von der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft verwiesen wird.</summary>
      <returns>Ein <see cref="T:System.Int32" /> mit dem Offset in Bytes im Datenpuffer, auf den von der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft verwiesen wird.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.OnCompleted(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Stellt eine Methode dar, die beim Abschluss eines asynchronen Vorgangs aufgerufen wird.</summary>
      <param name="e">Das signalisierte Ereignis.</param>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint">
      <summary>Ruft den Remote-IP-Endpunkt für einen asynchronen Vorgang ab oder legt ihn fest.</summary>
      <returns>Ein <see cref="T:System.Net.EndPoint" />, der den Remote-IP-Endpunkt für einen asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>Legt den Datenpuffer fest, der mit einer asynchronen Socketmethode verwendet werden soll.</summary>
      <param name="buffer">Der Datenpuffer, der mit einer asynchronen Socketmethode verwendet werden soll.</param>
      <param name="offset">Der Offset (in Bytes) im Datenpuffer, in dem der Vorgang beginnt.</param>
      <param name="count">Die maximale Datenmenge in Bytes, die im Puffer gesendet oder empfangen werden soll.</param>
      <exception cref="T:System.ArgumentException">Es wurden mehrdeutige Puffer angegeben.Diese Ausnahme tritt auf, wenn die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft nicht NULL ist und die <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />-Eigenschaft ebenfalls nicht NULL ist.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Ein Argument lag außerhalb des gültigen Bereichs.Diese Ausnahme tritt auf, wenn der <paramref name="offset" />-Parameter kleiner als 0 (null) oder größer als die Länge des Arrays in der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft ist.Diese Ausnahme tritt außerdem auf, wenn der <paramref name="count" />-Parameter kleiner als 0 (null) oder größer als die Länge des Arrays in der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft abzüglich des <paramref name="offset" />-Parameters ist.</exception>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Int32,System.Int32)">
      <summary>Legt den Datenpuffer fest, der mit einer asynchronen Socketmethode verwendet werden soll.</summary>
      <param name="offset">Der Offset (in Bytes) im Datenpuffer, in dem der Vorgang beginnt.</param>
      <param name="count">Die maximale Datenmenge in Bytes, die im Puffer gesendet oder empfangen werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Ein Argument lag außerhalb des gültigen Bereichs.Diese Ausnahme tritt auf, wenn der <paramref name="offset" />-Parameter kleiner als 0 (null) oder größer als die Länge des Arrays in der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft ist.Diese Ausnahme tritt außerdem auf, wenn der <paramref name="count" />-Parameter kleiner als 0 (null) oder größer als die Länge des Arrays in der <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />-Eigenschaft abzüglich des <paramref name="offset" />-Parameters ist.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SocketError">
      <summary>Ruft das Ergebnis des asynchronen Socketvorgangs ab oder legt dieses fest.</summary>
      <returns>Ein <see cref="T:System.Net.Sockets.SocketError" />, der das Ergebnis des asynchronen Socketvorgangs darstellt.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.UserToken">
      <summary>Ruft ein Benutzer- oder Anwendungsobjekt ab, das diesem asynchronen Socketvorgang zugeordnet ist, oder legt es fest.</summary>
      <returns>Ein Objekt, das das Benutzer- oder Anwendungsobjekt darstellt, das diesem asynchronen Socketvorgang zugeordnet ist.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncOperation">
      <summary>Der Typ des asynchronen Socketvorgangs, der zuletzt mit diesem Kontextobjekt ausgeführt wurde.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Accept">
      <summary>Ein Accept-Socketvorgang. </summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Connect">
      <summary>Ein Connect-Socketvorgang.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.None">
      <summary>Keiner der Socketvorgänge.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Receive">
      <summary>Ein Receive-Socketvorgang.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.ReceiveFrom">
      <summary>Ein ReceiveFrom-Socketvorgang.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Send">
      <summary>Ein Send-Socketvorgang.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.SendTo">
      <summary>Ein SendTo-Socketvorgang.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketShutdown">
      <summary>Definiert Konstanten, die von der <see cref="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)" />-Methode verwendet werden.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Both">
      <summary>Deaktiviert das Senden und Empfangen für einen <see cref="T:System.Net.Sockets.Socket" />.Dieses Feld ist konstant.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Receive">
      <summary>Deaktiviert das Empfangen für einen <see cref="T:System.Net.Sockets.Socket" />.Dieses Feld ist konstant.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Send">
      <summary>Deaktiviert das Senden für einen <see cref="T:System.Net.Sockets.Socket" />.Dieses Feld ist konstant.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketType">
      <summary>Gibt den Sockettyp an, der von einer Instanz der <see cref="T:System.Net.Sockets.Socket" />-Klasse dargestellt wird.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Dgram">
      <summary>Unterstützt Datagramme, die verbindungslose, unzuverlässige Meldungen mit einer festen (i. d. R. kleinen) maximalen Länge sind.Meldungen können verloren gehen, doppelt oder in der falschen Reihenfolge empfangen werden.Ein <see cref="T:System.Net.Sockets.Socket" /> vom Typ <see cref="F:System.Net.Sockets.SocketType.Dgram" /> benötigt vor dem Senden und Empfangen von Daten keine Verbindung und kann mit mehreren Peers kommunizieren.<see cref="F:System.Net.Sockets.SocketType.Dgram" /> verwendet das Datagram-Protokoll (<see cref="F:System.Net.Sockets.ProtocolType.Udp" />) und die <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /><see cref="T:System.Net.Sockets.AddressFamily" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Stream">
      <summary>Unterstützt zuverlässige, bidirektionale, verbindungsbasierte Bytestreams, bei denen keine Daten dupliziert und die Begrenzungen nicht beibehalten werden.Ein Socket dieses Typs kommuniziert mit einem einzigen Peer und benötigt vor dem Beginn der Kommunikation eine Verbindung mit einem Remotehost.<see cref="F:System.Net.Sockets.SocketType.Stream" /> verwendet das Transmission Control Protocol (<see cref="F:System.Net.Sockets.ProtocolType.Tcp" />) <see cref="T:System.Net.Sockets.ProtocolType" /> und das InterNetwork<see cref="T:System.Net.Sockets.AddressFamily" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Unknown">
      <summary>Gibt einen unbekannten Socket-Typ an.</summary>
    </member>
  </members>
</doc>