<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Sockets</name>
  </assembly>
  <members>
    <member name="T:System.Net.Sockets.ProtocolType">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> 클래스가 지원하는 프로토콜을 지정합니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Tcp">
      <summary>Transmission Control 프로토콜입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Udp">
      <summary>User Datagram 프로토콜입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unknown">
      <summary>알 수 없는 프로토콜입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unspecified">
      <summary>지정되지 않은 프로토콜입니다.</summary>
    </member>
    <member name="T:System.Net.Sockets.Socket">
      <summary>Berkeley 소켓 인터페이스를 구현합니다.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>지정된 주소 패밀리, 소켓 종류 및 프로토콜을 사용하여 <see cref="T:System.Net.Sockets.Socket" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="addressFamily">
        <see cref="T:System.Net.Sockets.AddressFamily" /> 값 중 하나입니다. </param>
      <param name="socketType">
        <see cref="T:System.Net.Sockets.SocketType" /> 값 중 하나입니다. </param>
      <param name="protocolType">
        <see cref="T:System.Net.Sockets.ProtocolType" /> 값 중 하나입니다. </param>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="addressFamily" />, <paramref name="socketType" /> 및 <paramref name="protocolType" />을 조합했을 때 소켓이 잘못된 경우 </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>지정된 소켓 종류 및 프로토콜을 사용하여 <see cref="T:System.Net.Sockets.Socket" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="socketType">
        <see cref="T:System.Net.Sockets.SocketType" /> 값 중 하나입니다.</param>
      <param name="protocolType">
        <see cref="T:System.Net.Sockets.ProtocolType" /> 값 중 하나입니다.</param>
      <exception cref="T:System.Net.Sockets.SocketException">
        <paramref name="socketType" />과 <paramref name="protocolType" />을 조합했을 때 소켓이 잘못된 경우 </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>들어오는 연결 시도를 받아들이는 비동기 작업을 시작합니다.</summary>
      <returns>I/O 작업이 보류 중인 경우 true를 반환합니다.작업이 완료되면 <paramref name="e" /> 매개 변수에 대한 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생합니다.I/O 작업이 동기적으로 완료된 경우 false를 반환합니다.이 경우에는 <paramref name="e" /> 매개 변수에서 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생하지 않으며, 메서드 호출이 반환된 직후 매개 변수로 전달된 <paramref name="e" /> 개체를 검사하여 작업 결과를 검색할 수 있습니다.</returns>
      <param name="e">이 비동기 소켓 작업에 사용할 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">인수가 잘못된 경우.제공된 버퍼의 크기가 너무 작으면 이 예외가 발생합니다.버퍼의 크기는 최소한 2 * (sizeof(SOCKADDR_STORAGE + 16)바이트 이상이어야 합니다.버퍼를 여러 개 지정하고 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> 속성이 null이 아닌 경우에도 이 예외가 발생합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">인수가 범위를 벗어난 경우.<see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Count" />가 0보다 작으면 이 예외가 발생합니다.</exception>
      <exception cref="T:System.InvalidOperationException">잘못된 작업이 요청된 경우.받아들이는 <see cref="T:System.Net.Sockets.Socket" />이 연결을 수신 대기하지 않거나 받아들인 소켓이 바인딩되어 있으면 이 예외가 발생합니다.<see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> 메서드를 호출하기 전에 <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> 및 <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> 메서드를 호출해야 합니다.소켓이 이미 연결되어 있거나 지정된 <paramref name="e" /> 매개 변수를 사용하여 소켓 작업이 이미 진행 중인 경우에도 이 예외가 발생합니다. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.NotSupportedException">이 메서드에 Windows XP 이상이 필요한 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.AddressFamily">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />의 주소 패밀리를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.AddressFamily" /> 값 중 하나입니다.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />을 로컬 끝점과 연결합니다.</summary>
      <param name="localEP">
        <see cref="T:System.Net.Sockets.Socket" />과 연결된 로컬 <see cref="T:System.Net.EndPoint" />입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localEP" />가 null입니다. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출 스택에 있는 상위 호출자에게 요청된 작업에 대한 권한이 없는 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.SocketPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.CancelConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>원격 호스트 연결에 대한 비동기 요청을 취소합니다.</summary>
      <param name="e">
        <see cref="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)" /> 메서드 중 하나를 호출하여 원격 호스트에 대한 연결을 요청하는 데 사용되는 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="e" /> 매개 변수가 null일 수 없으며, <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />도 null일 수 없습니다.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우 </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출 스택에 있는 상위 호출자에게 요청된 작업에 대한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>원격 호스트 연결에 대한 비동기 요청을 시작합니다.</summary>
      <returns>I/O 작업이 보류 중인 경우 true를 반환합니다.작업이 완료되면 <paramref name="e" /> 매개 변수에 대한 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생합니다.I/O 작업이 동기적으로 완료된 경우 false를 반환합니다.이 경우에는 <paramref name="e" /> 매개 변수에서 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생하지 않으며, 메서드 호출이 반환된 직후 매개 변수로 전달된 <paramref name="e" /> 개체를 검사하여 작업 결과를 검색할 수 있습니다.</returns>
      <param name="e">이 비동기 소켓 작업에 사용할 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">인수가 잘못된 경우.버퍼를 여러 개 지정하고 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> 속성이 null이 아니면 이 예외가 발생합니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="e" /> 매개 변수가 null일 수 없으며, <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />도 null일 수 없습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.Socket" />이 수신 대기 중이거나 <paramref name="e" /> 매개 변수에 지정된 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체를 사용하여 소켓 작업이 이미 진행 중입니다.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.NotSupportedException">이 메서드에 Windows XP 이상이 필요한 경우.로컬 끝점과 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />가 같은 주소 패밀리에 포함되지 않은 경우에도 이 예외가 발생합니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출 스택에 있는 상위 호출자에게 요청된 작업에 대한 권한이 없는 경우</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>원격 호스트 연결에 대한 비동기 요청을 시작합니다.</summary>
      <returns>I/O 작업이 보류 중인 경우 true를 반환합니다.작업이 완료되면 <paramref name="e" /> 매개 변수에 대한 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생합니다.I/O 작업이 동기적으로 완료된 경우 false를 반환합니다.이 경우에는 <paramref name="e" /> 매개 변수에서 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생하지 않으며, 메서드 호출이 반환된 직후 매개 변수로 전달된 <paramref name="e" /> 개체를 검사하여 작업 결과를 검색할 수 있습니다.</returns>
      <param name="socketType">
        <see cref="T:System.Net.Sockets.SocketType" /> 값 중 하나입니다.</param>
      <param name="protocolType">
        <see cref="T:System.Net.Sockets.ProtocolType" /> 값 중 하나입니다.</param>
      <param name="e">이 비동기 소켓 작업에 사용할 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">인수가 잘못된 경우.버퍼를 여러 개 지정하고 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> 속성이 null이 아니면 이 예외가 발생합니다.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="e" /> 매개 변수가 null일 수 없으며, <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />도 null일 수 없습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.Socket" />이 수신 대기 중이거나 <paramref name="e" /> 매개 변수에 지정된 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체를 사용하여 소켓 작업이 이미 진행 중입니다.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.NotSupportedException">이 메서드에 Windows XP 이상이 필요한 경우.로컬 끝점과 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />가 같은 주소 패밀리에 포함되지 않은 경우에도 이 예외가 발생합니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Security.SecurityException">호출 스택에 있는 상위 호출자에게 요청된 작업에 대한 권한이 없는 경우</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Connected">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />이 마지막으로 <see cref="Overload:System.Net.Sockets.Socket.Send" /> 또는 <see cref="Overload:System.Net.Sockets.Socket.Receive" /> 작업을 수행할 때 원격 호스트에 연결되었는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>가장 최근 작업에서 <see cref="T:System.Net.Sockets.Socket" />이 원격 리소스에 연결되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> 클래스의 현재 인스턴스에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />에서 사용하는 관리되지 않는 리소스를 해제하고, 필요에 따라 관리되는 리소스를 삭제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다. </param>
    </member>
    <member name="M:System.Net.Sockets.Socket.Finalize">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> 클래스에서 사용한 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Listen(System.Int32)">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />을 수신 상태로 둡니다.</summary>
      <param name="backlog">보류 중인 연결 큐의 최대 길이입니다. </param>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.LocalEndPoint">
      <summary>로컬 끝점을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.Socket" />이 통신하는 데 사용하는 <see cref="T:System.Net.EndPoint" />입니다.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.NoDelay">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> 스트림에서 Nagle 알고리즘을 사용하는지 여부를 나타내는 <see cref="T:System.Boolean" /> 값을 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.Socket" />에서 Nagle 알고리즘을 사용하면 false이고, 그렇지 않으면 true입니다.기본값은 false입니다.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">
        <see cref="T:System.Net.Sockets.Socket" />에 액세스하려고 시도하는 동안 오류가 발생한 경우.자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv4">
      <summary>내부 운영 체제 및 네트워크 어댑터에서 IPv4(인터넷 프로토콜 버전 4)를 지원하는지 여부를 나타냅니다.</summary>
      <returns>운영 체제 및 네트워크 어댑터에서 IPv4 프로토콜을 지원하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv6">
      <summary>내부 운영 체제 및 네트워크 어댑터에서 IPv6(인터넷 프로토콜 버전 6)을 지원하는지 여부를 나타냅니다.</summary>
      <returns>운영 체제 및 네트워크 어댑터에서 IPv6 프로토콜을 지원하면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.ProtocolType">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />의 프로토콜 종류를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.ProtocolType" /> 값 중 하나입니다.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>연결된 <see cref="T:System.Net.Sockets.Socket" /> 개체에서 데이터를 받기 위해 비동기 요청을 시작합니다.</summary>
      <returns>I/O 작업이 보류 중인 경우 true를 반환합니다.작업이 완료되면 <paramref name="e" /> 매개 변수에 대한 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생합니다.I/O 작업이 동기적으로 완료된 경우 false를 반환합니다.이 경우에는 <paramref name="e" /> 매개 변수에서 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생하지 않으며, 메서드 호출이 반환된 직후 매개 변수로 전달된 <paramref name="e" /> 개체를 검사하여 작업 결과를 검색할 수 있습니다.</returns>
      <param name="e">이 비동기 소켓 작업에 사용할 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">인수가 잘못된 경우.<paramref name="e" /> 매개 변수의 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 또는 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> 속성이 올바른 버퍼를 참조하지 않는 경우.이러한 속성 중 하나를 설정할 수 있지만 두 속성을 동시에 설정할 수는 없습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="e" /> 매개 변수에 지정된 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체를 사용하여 소켓 작업이 이미 진행 중인 경우</exception>
      <exception cref="T:System.NotSupportedException">이 메서드에 Windows XP 이상이 필요한 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ReceiveBufferSize">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />의 수신 버퍼 크기를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>수신 버퍼의 크기(바이트)가 들어 있는 <see cref="T:System.Int32" />입니다.기본값은 8192입니다.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">set 작업에 지정된 값이 0보다 작은 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFromAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>지정된 네트워크 장치에서 비동기적으로 데이터를 받기 시작합니다.</summary>
      <returns>I/O 작업이 보류 중인 경우 true를 반환합니다.작업이 완료되면 <paramref name="e" /> 매개 변수에 대한 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생합니다.I/O 작업이 동기적으로 완료된 경우 false를 반환합니다.이 경우에는 <paramref name="e" /> 매개 변수에서 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생하지 않으며, 메서드 호출이 반환된 직후 매개 변수로 전달된 <paramref name="e" /> 개체를 검사하여 작업 결과를 검색할 수 있습니다.</returns>
      <param name="e">이 비동기 소켓 작업에 사용할 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="e" /> 매개 변수에 지정된 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체를 사용하여 소켓 작업이 이미 진행 중인 경우</exception>
      <exception cref="T:System.NotSupportedException">이 메서드에 Windows XP 이상이 필요한 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우 </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.RemoteEndPoint">
      <summary>원격 끝점을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.Socket" />이 통신에 사용하는 <see cref="T:System.Net.EndPoint" />입니다.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>데이터를 연결된 <see cref="T:System.Net.Sockets.Socket" /> 개체에 비동기적으로 보냅니다.</summary>
      <returns>I/O 작업이 보류 중인 경우 true를 반환합니다.작업이 완료되면 <paramref name="e" /> 매개 변수에 대한 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생합니다.I/O 작업이 동기적으로 완료된 경우 false를 반환합니다.이 경우에는 <paramref name="e" /> 매개 변수에서 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생하지 않으며, 메서드 호출이 반환된 직후 매개 변수로 전달된 <paramref name="e" /> 개체를 검사하여 작업 결과를 검색할 수 있습니다.</returns>
      <param name="e">이 비동기 소켓 작업에 사용할 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="e" /> 매개 변수의 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 또는 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> 속성이 올바른 버퍼를 참조하지 않는 경우.이러한 속성 중 하나를 설정할 수 있지만 두 속성을 동시에 설정할 수는 없습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="e" /> 매개 변수에 지정된 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체를 사용하여 소켓 작업이 이미 진행 중인 경우</exception>
      <exception cref="T:System.NotSupportedException">이 메서드에 Windows XP 이상이 필요한 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Net.Sockets.SocketException">
        <see cref="T:System.Net.Sockets.Socket" />이 아직 연결되지 않았거나 <see cref="M:System.Net.Sockets.Socket.Accept" />, <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> 또는 <see cref="Overload:System.Net.Sockets.Socket.BeginAccept" /> 메서드를 통해 소켓을 가져오지 못한 경우</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SendBufferSize">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />의 송신 버퍼 크기를 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>송신 버퍼의 크기(바이트)가 들어 있는 <see cref="T:System.Int32" />입니다.기본값은 8192입니다.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">set 작업에 지정된 값이 0보다 작은 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendToAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>특정 원격 호스트에 데이터를 비동기적으로 보냅니다.</summary>
      <returns>I/O 작업이 보류 중인 경우 true를 반환합니다.작업이 완료되면 <paramref name="e" /> 매개 변수에 대한 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생합니다.I/O 작업이 동기적으로 완료된 경우 false를 반환합니다.이 경우에는 <paramref name="e" /> 매개 변수에서 <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> 이벤트가 발생하지 않으며, 메서드 호출이 반환된 직후 매개 변수로 전달된 <paramref name="e" /> 개체를 검사하여 작업 결과를 검색할 수 있습니다.</returns>
      <param name="e">이 비동기 소켓 작업에 사용할 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="e" /> 매개 변수에 지정된 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 개체를 사용하여 소켓 작업이 이미 진행 중인 경우</exception>
      <exception cref="T:System.NotSupportedException">이 메서드에 Windows XP 이상이 필요한 경우.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <exception cref="T:System.Net.Sockets.SocketException">연결 지향 프로토콜이 지정되었는데 <see cref="T:System.Net.Sockets.Socket" />이 아직 연결되지 않은 경우</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />에서 보내기 및 받기를 사용할 수 없도록 설정합니다.</summary>
      <param name="how">더 이상 허용하지 않을 작업을 지정하는 <see cref="T:System.Net.Sockets.SocketShutdown" /> 값 중 하나입니다. </param>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우자세한 내용은 설명 부분을 참조하십시오.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.Ttl">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />에서 보낸 IP(인터넷 프로토콜) 패킷의 TTL(Time-To-Live) 값을 지정하는 값을 가져오거나 설정합니다.</summary>
      <returns>TTL 값입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">TTL 값은 음수로 설정할 수 있습니다.</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> 또는 <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" /> 패밀리의 소켓이 아닌 소켓에 대해 이 속성을 설정한 경우</exception>
      <exception cref="T:System.Net.Sockets.SocketException">소켓에 액세스하려고 시도하는 동안 오류가 발생한 경우TTL을 255보다 큰 값으로 설정하고자 할 때에도 이 오류가 반환됩니다.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" />이 닫힌 경우 </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncEventArgs">
      <summary>비동기 소켓 작업을 나타냅니다.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.#ctor">
      <summary>빈 <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 인스턴스를 만듭니다.</summary>
      <exception cref="T:System.NotSupportedException">플랫폼이 지원되지 않는 경우 </exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.AcceptSocket">
      <summary>비동기 소켓 메서드를 통해 연결을 허용하기 위해 만들었거나 사용할 소켓을 가져오거나 설정합니다.</summary>
      <returns>비동기 소켓 메서드를 통해 연결을 허용하기 위해 만들었거나 사용할 <see cref="T:System.Net.Sockets.Socket" />입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer">
      <summary>비동기 소켓 메서드에 사용할 데이터 버퍼를 가져옵니다.</summary>
      <returns>비동기 소켓 메서드에 사용할 데이터 버퍼를 나타내는 <see cref="T:System.Byte" /> 배열입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList">
      <summary>비동기 소켓 메서드에 사용할 데이터 버퍼의 배열을 가져오거나 설정합니다.</summary>
      <returns>비동기 소켓 메서드에 사용할 데이터 버퍼의 배열을 나타내는 <see cref="T:System.Collections.IList" />입니다.</returns>
      <exception cref="T:System.ArgumentException">설정 작업에 지정된 버퍼가 명확하지 않은 경우.<see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성이 null이 아닌 값으로 설정되고, <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" />속성을 null이 아닌 값으로 설정하고자 하는 경우, 이러한 예외가 발생합니다.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BytesTransferred">
      <summary>소켓 작업에서 전송된 바이트 수를 가져옵니다.</summary>
      <returns>소켓 작업에서 전송된 바이트 수를 포함하는 <see cref="T:System.Int32" />입니다.</returns>
    </member>
    <member name="E:System.Net.Sockets.SocketAsyncEventArgs.Completed">
      <summary>비동기 작업을 완료하는 데 사용할 이벤트입니다.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectByNameError">
      <summary>
        <see cref="T:System.Net.DnsEndPoint" />를 사용할 때 연결 실패가 발생하는 경우의 예외를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Net.DnsEndPoint" />가 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> 속성에 지정된 경우 연결 오류의 원인을 나타내는 <see cref="T:System.Exception" />입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectSocket">
      <summary>
        <see cref="Overload:System.Net.Sockets.Socket.ConnectAsync" /> 메서드가 성공적으로 완료된 후 만들어지고 연결되는 <see cref="T:System.Net.Sockets.Socket" /> 개체입니다.</summary>
      <returns>연결된 <see cref="T:System.Net.Sockets.Socket" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Count">
      <summary>비동기 작업을 통해 보내거나 받을 최대 데이터 양(바이트)을 가져옵니다.</summary>
      <returns>보내거나 받을 최대 데이터 양(바이트)을 포함하는 <see cref="T:System.Int32" />입니다.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Dispose">
      <summary>
        <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 인스턴스에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 삭제합니다.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Finalize">
      <summary>
        <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> 클래스에서 사용하는 리소스를 해제합니다.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.LastOperation">
      <summary>이 컨텍스트 개체를 사용하여 가장 최근에 수행한 소켓 작업의 유형을 가져옵니다.</summary>
      <returns>이 컨텍스트 개체를 사용하여 가장 최근에 수행한 소켓 작업의 유형을 나타내는 <see cref="T:System.Net.Sockets.SocketAsyncOperation" /> 인스턴스입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Offset">
      <summary>
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성에서 참조하는 데이터 버퍼의 오프셋(바이트)을 가져옵니다.</summary>
      <returns>
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성에서 참조하는 데이터 버퍼의 오프셋(바이트)이 포함된 <see cref="T:System.Int32" />입니다.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.OnCompleted(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>비동기 작업이 완료되면 호출할 메서드를 나타냅니다.</summary>
      <param name="e">신호를 받는 이벤트입니다.</param>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint">
      <summary>비동기 작업의 원격 IP 끝점을 가져오거나 설정합니다.</summary>
      <returns>비동기 작업의 원격 IP 끝점을 나타내는 <see cref="T:System.Net.EndPoint" />입니다.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>비동기 소켓 메서드에 사용할 데이터 버퍼를 설정합니다.</summary>
      <param name="buffer">비동기 소켓 메서드에 사용할 데이터 버퍼입니다.</param>
      <param name="offset">데이터 버퍼에서 작업이 시작되는 오프셋(바이트)입니다.</param>
      <param name="count">버퍼에서 보내거나 받을 최대 데이터 양(바이트)입니다.</param>
      <exception cref="T:System.ArgumentException">지정된 버퍼가 명확하지 않은 경우.<see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성도 null이 아니고 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> 속성도 null이 아니면 이 예외가 발생합니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">인수가 범위를 벗어난 경우.<paramref name="offset" /> 매개 변수가 0보다 작거나 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성에 지정된 배열 길이보다 크면 이 예외가 발생합니다.또한 <paramref name="count" /> 매개 변수가 0보다 작거나, <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성에 지정된 배열 길이에서 <paramref name="offset" /> 매개 변수를 뺀 값보다 큰 경우에도 이 예외가 발생합니다.</exception>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Int32,System.Int32)">
      <summary>비동기 소켓 메서드에 사용할 데이터 버퍼를 설정합니다.</summary>
      <param name="offset">데이터 버퍼에서 작업이 시작되는 오프셋(바이트)입니다.</param>
      <param name="count">버퍼에서 보내거나 받을 최대 데이터 양(바이트)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">인수가 범위를 벗어난 경우.<paramref name="offset" /> 매개 변수가 0보다 작거나 <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성에 지정된 배열 길이보다 크면 이 예외가 발생합니다.또한 <paramref name="count" /> 매개 변수가 0보다 작거나, <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> 속성에 지정된 배열 길이에서 <paramref name="offset" /> 매개 변수를 뺀 값보다 큰 경우에도 이 예외가 발생합니다.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SocketError">
      <summary>비동기 소켓 작업의 결과를 가져오거나 설정합니다.</summary>
      <returns>비동기 소켓 작업의 결과를 나타내는 <see cref="T:System.Net.Sockets.SocketError" />입니다.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.UserToken">
      <summary>이 비동기 소켓 작업과 연결된 사용자 또는 응용 프로그램 개체를 가져오거나 설정합니다.</summary>
      <returns>이 비동기 소켓 작업과 연결된 사용자 또는 응용 프로그램 개체를 나타내는 개체입니다.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncOperation">
      <summary>이 컨텍스트 개체를 사용하여 가장 최근에 수행된 비동기 소켓 작업의 유형입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Accept">
      <summary>소켓 Accept 작업입니다. </summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Connect">
      <summary>소켓 Connect 작업입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.None">
      <summary>소켓 작업이 없습니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Receive">
      <summary>소켓 Receive 작업입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.ReceiveFrom">
      <summary>소켓 ReceiveFrom 작업입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Send">
      <summary>소켓 Send 작업입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.SendTo">
      <summary>소켓 SendTo 작업입니다.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketShutdown">
      <summary>
        <see cref="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)" /> 메서드에서 사용하는 상수를 정의합니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Both">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />을 보내기와 받기 모두에 사용할 수 없도록 설정합니다.이 필드는 상수입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Receive">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />을 받기에 사용할 수 없도록 설정합니다.이 필드는 상수입니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Send">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" />을 보내기에 사용할 수 없도록 설정합니다.이 필드는 상수입니다.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketType">
      <summary>
        <see cref="T:System.Net.Sockets.Socket" /> 클래스의 인스턴스가 나타내는 소켓의 종류를 지정합니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Dgram">
      <summary>고정된 최대 길이(대개 작음)의 신뢰할 수 없고 연결 없는 메시지인 데이터그램을 지원합니다.메시지가 손실되거나 중복될 수 있으며 메시지 순서가 잘못될 수도 있습니다.<see cref="F:System.Net.Sockets.SocketType.Dgram" /> 종류의 <see cref="T:System.Net.Sockets.Socket" />은 데이터를 보내고 받기 전에 연결하지 않고도 여러 피어와 통신할 수 있습니다.<see cref="F:System.Net.Sockets.SocketType.Dgram" />은 Datagram Protocol(<see cref="F:System.Net.Sockets.ProtocolType.Udp" />)과 <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /><see cref="T:System.Net.Sockets.AddressFamily" />를 사용합니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Stream">
      <summary>데이터 중복이나 경계 유지 없이 신뢰성 있는 양방향 연결 기반의 바이트 스트림을 지원합니다.이 종류의 Socket은 단일 피어와 통신하며 이 소켓을 사용할 경우 통신을 시작하기 전에 원격 호스트에 연결해야 합니다.<see cref="F:System.Net.Sockets.SocketType.Stream" />은 Transmission Control Protocol(<see cref="F:System.Net.Sockets.ProtocolType.Tcp" />) <see cref="T:System.Net.Sockets.ProtocolType" /> 및 InterNetwork<see cref="T:System.Net.Sockets.AddressFamily" />를 사용합니다.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Unknown">
      <summary>알 수 없는 Socket 종류를 지정합니다.</summary>
    </member>
  </members>
</doc>