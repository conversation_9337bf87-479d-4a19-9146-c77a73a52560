<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Sockets</name>
  </assembly>
  <members>
    <member name="T:System.Net.Sockets.ProtocolType">
      <summary>Spécifie les protocoles pris en charge par la classe <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Tcp">
      <summary>Protocole TCP (Transmission Control Protocol).</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Udp">
      <summary>Protocole UDP (User Datagram Protocol).</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unknown">
      <summary>Protocole inconnu.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unspecified">
      <summary>Protocole non spécifié.</summary>
    </member>
    <member name="T:System.Net.Sockets.Socket">
      <summary>Implémente l'interface de sockets Berkeley.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Sockets.Socket" /> en utilisant la famille d'adresses, le type de socket et le protocole spécifiés.</summary>
      <param name="addressFamily">Une des valeurs de <see cref="T:System.Net.Sockets.AddressFamily" />. </param>
      <param name="socketType">Une des valeurs de <see cref="T:System.Net.Sockets.SocketType" />. </param>
      <param name="protocolType">Une des valeurs de <see cref="T:System.Net.Sockets.ProtocolType" />. </param>
      <exception cref="T:System.Net.Sockets.SocketException">La combinaison de <paramref name="addressFamily" />, <paramref name="socketType" /> et <paramref name="protocolType" /> crée un socket non valide. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Net.Sockets.Socket" /> à l'aide du type de socket et du protocole spécifiés.</summary>
      <param name="socketType">Une des valeurs de <see cref="T:System.Net.Sockets.SocketType" />.</param>
      <param name="protocolType">Une des valeurs de <see cref="T:System.Net.Sockets.ProtocolType" />.</param>
      <exception cref="T:System.Net.Sockets.SocketException">La combinaison de <paramref name="socketType" /> et <paramref name="protocolType" /> crée un socket non valide. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Démarre une opération asynchrone pour accepter une tentative de connexion entrante.</summary>
      <returns>Retourne la valeur true si l'opération d'E/S est en attente.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> sera déclenché une fois l'opération terminée.Retourne la valeur false si l'opération d'E/S a été terminée de manière synchrone.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> ne sera pas déclenché et l'objet <paramref name="e" /> transmis en tant que paramètre peut être examiné immédiatement après que l'appel de méthode a été retourné pour extraire le résultat de l'opération.</returns>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> à utiliser pour cette opération de socket asynchrone.</param>
      <exception cref="T:System.ArgumentException">Un argument n'est pas valide.Cette exception se produit si la mémoire tampon fournie n'est pas assez grande.La mémoire tampon doit être d'au moins 2 * (taille de (SOCKADDR_STORAGE + 16) octets.Cette exception se produit également si plusieurs mémoires tampons sont spécifiées, la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> n'est pas null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Un argument est hors limites.L'exception se produit si <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Count" /> est inférieur à 0.</exception>
      <exception cref="T:System.InvalidOperationException">Une opération incorrecte a été demandée.Cette exception se produit si le <see cref="T:System.Net.Sockets.Socket" /> acceptant n'écoute pas les connexions ou si le socket accepté est lié.Vous devez appeler les méthodes <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> et <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> avant d'appeler la méthode <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" />.Cette exception se produit également si le socket est déjà connecté ou si une opération de socket utilisait déjà le paramètre de <paramref name="e" /> spécifié. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP ou version ultérieure est requis pour cette méthode.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.AddressFamily">
      <summary>Obtient la famille d'adresses de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Une des valeurs de <see cref="T:System.Net.Sockets.AddressFamily" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)">
      <summary>Associe <see cref="T:System.Net.Sockets.Socket" /> à un point de terminaison local.</summary>
      <param name="localEP">
        <see cref="T:System.Net.EndPoint" /> local à associer à <see cref="T:System.Net.Sockets.Socket" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localEP" /> a la valeur null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Security.SecurityException">Un appelant situé plus haut dans la pile des appels n'a pas l'autorisation pour l'opération demandée. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.SocketPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.CancelConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Annule une requête asynchrone pour une connexion d'hôte distant.</summary>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> utilisé pour demander la connexion à l'hôte distant en appelant l'une des méthodes <see cref="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)" />.</param>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="e" /> ne peut pas être null et <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> ne peut pas être vide.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket. </exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Security.SecurityException">Un appelant situé plus haut dans la pile des appels n'a pas l'autorisation pour l'opération demandée.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Démarre une demande asynchrone pour une connexion à un hôte distant.</summary>
      <returns>Retourne la valeur true si l'opération d'E/S est en attente.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> sera déclenché une fois l'opération terminée.Retourne la valeur false si l'opération d'E/S a été terminée de manière synchrone.Dans ce cas, l'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> ne sera pas déclenché et l'objet <paramref name="e" /> transmis en tant que paramètre peut être examiné immédiatement après que l'appel de méthode a été retourné pour extraire le résultat de l'opération.</returns>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> à utiliser pour cette opération de socket asynchrone.</param>
      <exception cref="T:System.ArgumentException">Un argument n'est pas valide.Cette exception se produit si plusieurs mémoires tampons sont spécifiées, la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> n'est pas null.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="e" /> ne peut pas être null et <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> ne peut pas être vide.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.Socket" /> est à l'écoute ou une opération de socket utilisant l'objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> spécifié dans le paramètre <paramref name="e" /> spécifié était déjà en cours.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP ou version ultérieure est requis pour cette méthode.Cette exception se produit également si le point de terminaison local et les <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> ne sont pas la même famille d'adresses.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Security.SecurityException">Un appelant situé plus haut dans la pile des appels n'a pas l'autorisation pour l'opération demandée.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Démarre une demande asynchrone pour une connexion à un hôte distant.</summary>
      <returns>Retourne la valeur true si l'opération d'E/S est en attente.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> sera déclenché une fois l'opération terminée.Retourne la valeur false si l'opération d'E/S a été terminée de manière synchrone.Dans ce cas, l'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> ne sera pas déclenché et l'objet <paramref name="e" /> transmis en tant que paramètre peut être examiné immédiatement après que l'appel de méthode a été retourné pour extraire le résultat de l'opération.</returns>
      <param name="socketType">Une des valeurs de <see cref="T:System.Net.Sockets.SocketType" />.</param>
      <param name="protocolType">Une des valeurs de <see cref="T:System.Net.Sockets.ProtocolType" />.</param>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> à utiliser pour cette opération de socket asynchrone.</param>
      <exception cref="T:System.ArgumentException">Un argument n'est pas valide.Cette exception se produit si plusieurs mémoires tampons sont spécifiées, la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> n'est pas null.</exception>
      <exception cref="T:System.ArgumentNullException">Le paramètre <paramref name="e" /> ne peut pas être null et <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> ne peut pas être vide.</exception>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Net.Sockets.Socket" /> est à l'écoute ou une opération de socket utilisant l'objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> spécifié dans le paramètre <paramref name="e" /> spécifié était déjà en cours.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP ou version ultérieure est requis pour cette méthode.Cette exception se produit également si le point de terminaison local et les <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> ne sont pas la même famille d'adresses.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Security.SecurityException">Un appelant situé plus haut dans la pile des appels n'a pas l'autorisation pour l'opération demandée.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Connected">
      <summary>Obtient une valeur qui indique si <see cref="T:System.Net.Sockets.Socket" /> est connecté à un hôte distant depuis la dernière opération <see cref="Overload:System.Net.Sockets.Socket.Send" /> ou <see cref="Overload:System.Net.Sockets.Socket.Receive" />.</summary>
      <returns>true si <see cref="T:System.Net.Sockets.Socket" /> était connecté à une ressource distante lors de l'opération la plus récente ; sinon, false.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose">
      <summary>Libère toutes les ressources utilisées par l'instance actuelle de la classe <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par <see cref="T:System.Net.Sockets.Socket" /> et supprime éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour libérer uniquement les ressources non managées. </param>
    </member>
    <member name="M:System.Net.Sockets.Socket.Finalize">
      <summary>Libère les ressources utilisées par la classe <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Listen(System.Int32)">
      <summary>Met <see cref="T:System.Net.Sockets.Socket" /> dans un état d'écoute.</summary>
      <param name="backlog">Longueur maximale de la file d'attente des connexions en attente. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.LocalEndPoint">
      <summary>Obtient le point de terminaison local.</summary>
      <returns>
        <see cref="T:System.Net.EndPoint" /> que <see cref="T:System.Net.Sockets.Socket" /> utilise pour les communications.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.NoDelay">
      <summary>Obtient ou définit une valeur <see cref="T:System.Boolean" /> spécifiant si le flux de données <see cref="T:System.Net.Sockets.Socket" /> utilise l'algorithme Nagle.</summary>
      <returns>false si <see cref="T:System.Net.Sockets.Socket" /> utilise l'algorithme Nagle ; sinon, true.La valeur par défaut est false.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès à <see cref="T:System.Net.Sockets.Socket" />.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv4">
      <summary>Indique si le système d'exploitation et les cartes réseau sous-jacents prennent en charge le protocole IPv4 (Internet Protocol version 4).</summary>
      <returns>true si le système d'exploitation et les cartes réseau prennent en charge le protocole IPv4 ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv6">
      <summary>Indique si le système d'exploitation et les cartes réseau sous-jacents prennent en charge le protocole IPv6 (Internet Protocol version 6).</summary>
      <returns>true si le système d'exploitation et les cartes réseau prennent en charge le protocole IPv6 ; sinon, false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.ProtocolType">
      <summary>Obtient le type de protocole de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Une des valeurs de <see cref="T:System.Net.Sockets.ProtocolType" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Démarre une demande asynchrone pour recevoir les données d'un objet <see cref="T:System.Net.Sockets.Socket" /> connecté.</summary>
      <returns>Retourne la valeur true si l'opération d'E/S est en attente.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> sera déclenché une fois l'opération terminée.Retourne la valeur false si l'opération d'E/S a été terminée de manière synchrone.Dans ce cas, l'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> ne sera pas déclenché et l'objet <paramref name="e" /> transmis en tant que paramètre peut être examiné immédiatement après que l'appel de méthode a été retourné pour extraire le résultat de l'opération.</returns>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> à utiliser pour cette opération de socket asynchrone.</param>
      <exception cref="T:System.ArgumentException">Un argument n'était pas valide.La propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> ou <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> sur le paramètre de <paramref name="e" /> doit référencer des mémoires tampon valides.L'une ou l'autre de ces propriétés peut être définie, mais pas les deux à la fois.</exception>
      <exception cref="T:System.InvalidOperationException">Une opération de socket utilisant l'objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> spécifié dans le paramètre <paramref name="e" /> spécifié était déjà en cours.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP ou version ultérieure est requis pour cette méthode.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ReceiveBufferSize">
      <summary>Obtient ou définit une valeur spécifiant la taille de la mémoire tampon de réception de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>
        <see cref="T:System.Int32" /> contenant la taille de la mémoire tampon de réception en octets.La valeur par défaut est 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La valeur spécifiée pour une opération ensembliste est inférieure à 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFromAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Démarre la réception asynchrone de données à partir d'un périphérique réseau spécifié.</summary>
      <returns>Retourne la valeur true si l'opération d'E/S est en attente.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> sera déclenché une fois l'opération terminée.Retourne la valeur false si l'opération d'E/S a été terminée de manière synchrone.Dans ce cas, l'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> ne sera pas déclenché et l'objet <paramref name="e" /> transmis en tant que paramètre peut être examiné immédiatement après que l'appel de méthode a été retourné pour extraire le résultat de l'opération.</returns>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> à utiliser pour cette opération de socket asynchrone.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> ne peut pas être Null.</exception>
      <exception cref="T:System.InvalidOperationException">Une opération de socket utilisant l'objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> spécifié dans le paramètre <paramref name="e" /> spécifié était déjà en cours.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP ou version ultérieure est requis pour cette méthode.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.RemoteEndPoint">
      <summary>Obtient le point de terminaison distant.</summary>
      <returns>
        <see cref="T:System.Net.EndPoint" /> avec lequel <see cref="T:System.Net.Sockets.Socket" /> communique.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Envoie des données de façon asynchrone à un objet <see cref="T:System.Net.Sockets.Socket" /> connecté.</summary>
      <returns>Retourne la valeur true si l'opération d'E/S est en attente.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> sera déclenché une fois l'opération terminée.Retourne la valeur false si l'opération d'E/S a été terminée de manière synchrone.Dans ce cas, l'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> ne sera pas déclenché et l'objet <paramref name="e" /> transmis en tant que paramètre peut être examiné immédiatement après que l'appel de méthode a été retourné pour extraire le résultat de l'opération.</returns>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> à utiliser pour cette opération de socket asynchrone.</param>
      <exception cref="T:System.ArgumentException">La propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> ou <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> sur le paramètre de <paramref name="e" /> doit référencer des mémoires tampon valides.L'une ou l'autre de ces propriétés peut être définie, mais pas les deux à la fois.</exception>
      <exception cref="T:System.InvalidOperationException">Une opération de socket utilisant l'objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> spécifié dans le paramètre <paramref name="e" /> spécifié était déjà en cours.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP ou version ultérieure est requis pour cette méthode.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Le <see cref="T:System.Net.Sockets.Socket" /> n'est pas encore connecté ou n'a pas été obtenu via une méthode <see cref="M:System.Net.Sockets.Socket.Accept" />, <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" />ou <see cref="Overload:System.Net.Sockets.Socket.BeginAccept" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SendBufferSize">
      <summary>Obtient ou définit une valeur spécifiant la taille de la mémoire tampon d'envoi de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>
        <see cref="T:System.Int32" /> contenant la taille de la mémoire tampon d'envoi en octets.La valeur par défaut est 8192.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">La valeur spécifiée pour une opération ensembliste est inférieure à 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendToAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Envoie des données de façon asynchrone à un hôte distant spécifique.</summary>
      <returns>Retourne la valeur true si l'opération d'E/S est en attente.L'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> sera déclenché une fois l'opération terminée.Retourne la valeur false si l'opération d'E/S a été terminée de manière synchrone.Dans ce cas, l'événement <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> sur le paramètre <paramref name="e" /> ne sera pas déclenché et l'objet <paramref name="e" /> transmis en tant que paramètre peut être examiné immédiatement après que l'appel de méthode a été retourné pour extraire le résultat de l'opération.</returns>
      <param name="e">Objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> à utiliser pour cette opération de socket asynchrone.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> ne peut pas être Null.</exception>
      <exception cref="T:System.InvalidOperationException">Une opération de socket utilisant l'objet <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> spécifié dans le paramètre <paramref name="e" /> spécifié était déjà en cours.</exception>
      <exception cref="T:System.NotSupportedException">Windows XP ou version ultérieure est requis pour cette méthode.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Le protocole spécifié est orienté connexion, mais le <see cref="T:System.Net.Sockets.Socket" /> n'est pas encore connecté.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)">
      <summary>Désactive les envois et les réceptions sur un <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="how">Une des valeurs de <see cref="T:System.Net.Sockets.SocketShutdown" /> spécifiant l'opération qui ne sera plus autorisée. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Pour plus d'informations, consultez la section Notes.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.Ttl">
      <summary>Obtient ou définit une valeur qui spécifie la durée de vie des paquets IP (Internet Protocol) envoyés par <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Durée de vie.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">La valeur TTL ne peut pas être un nombre négatif.</exception>
      <exception cref="T:System.NotSupportedException">Cette propriété ne peut être définie que pour les sockets dans les familles <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> ou <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Une erreur s'est produite lors de la tentative d'accès au socket.Cette erreur est également retournée lorsqu'une tentative a été faite pour affecter à TTL une valeur supérieure à 255.</exception>
      <exception cref="T:System.ObjectDisposedException">
        <see cref="T:System.Net.Sockets.Socket" /> a été fermé. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncEventArgs">
      <summary>Représente une opération de socket asynchrone.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.#ctor">
      <summary>Crée une instance <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> vide.</summary>
      <exception cref="T:System.NotSupportedException">La plateforme n'est pas prise en charge. </exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.AcceptSocket">
      <summary>Obtient ou définit le socket à utiliser ou le socket créé pour accepter une connexion avec une méthode de socket asynchrone.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.Socket" /> à utiliser ou socket créé pour accepter une connexion avec une méthode de socket asynchrone.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer">
      <summary>Obtient la mémoire tampon des données à utiliser avec une méthode de socket asynchrone.</summary>
      <returns>Tableau <see cref="T:System.Byte" /> qui représente la mémoire tampon des données à utiliser avec une méthode de socket asynchrone.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList">
      <summary>Obtient ou définit un tableau de la mémoire tampon de données à utiliser avec une méthode de socket asynchrone.</summary>
      <returns>
        <see cref="T:System.Collections.IList" /> qui représente un tableau de mémoires tampons de données à utiliser avec une méthode de socket asynchrone.</returns>
      <exception cref="T:System.ArgumentException">Des mémoires tampon ambiguës sont spécifiées sur une opération ensembliste.Cette exception se produit si la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> a eu une valeur non NULL et une tentative a été faite pour affecter à la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> une valeur non NULL.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BytesTransferred">
      <summary>Obtient le nombre d'octets transférés dans l'opération de socket.</summary>
      <returns>
        <see cref="T:System.Int32" /> qui contient le nombre d'octets transférés dans l'opération de socket.</returns>
    </member>
    <member name="E:System.Net.Sockets.SocketAsyncEventArgs.Completed">
      <summary>Événement utilisé pour terminer une opération asynchrone.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectByNameError">
      <summary>Obtient l'exception dans le cas d'un échec de connexion lorsqu'un <see cref="T:System.Net.DnsEndPoint" /> a été utilisé.</summary>
      <returns>
        <see cref="T:System.Exception" /> qui indique la cause de l'erreur de connexion lorsqu'un <see cref="T:System.Net.DnsEndPoint" /> a été spécifié pour la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectSocket">
      <summary>Objet <see cref="T:System.Net.Sockets.Socket" /> créé et connecté après l'exécution correcte de la méthode <see cref="Overload:System.Net.Sockets.Socket.ConnectAsync" />.</summary>
      <returns>Objet <see cref="T:System.Net.Sockets.Socket" /> connecté.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Count">
      <summary>Obtient la quantité maximale de données, en octets, à envoyer ou recevoir dans une opération asynchrone.</summary>
      <returns>
        <see cref="T:System.Int32" /> qui contient la quantité maximale de données, en octets, à envoyer ou recevoir.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Dispose">
      <summary>Libère les ressources non managées utilisées par l'instance <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> et supprime éventuellement les ressources managées.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Finalize">
      <summary>Libère les ressources utilisées par la classe <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.LastOperation">
      <summary>Obtient le type d'opération de socket exécuté le plus récemment avec cet objet de contexte.</summary>
      <returns>Instance <see cref="T:System.Net.Sockets.SocketAsyncOperation" /> qui indique le type d'opération de socket exécutée le plus récemment avec cet objet de contexte.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Offset">
      <summary>Obtient l'offset, en octets, dans la mémoire tampon de données référencée par la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.</summary>
      <returns>
        <see cref="T:System.Int32" /> qui contient l'offset, en octets, dans la mémoire tampon de données référencée par la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.OnCompleted(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Représente une méthode qui est appelée lorsqu'une opération asynchrone se termine.</summary>
      <param name="e">Événement qui est signalé.</param>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint">
      <summary>Obtient ou définit le point de terminaison IP distant d'une opération asynchrone.</summary>
      <returns>
        <see cref="T:System.Net.EndPoint" /> qui représente le point de terminaison IP distant d'une opération asynchrone.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>Définit la mémoire tampon de données à utiliser avec une méthode de socket asynchrone.</summary>
      <param name="buffer">Mémoire tampon de données à utiliser avec une méthode de socket asynchrone.</param>
      <param name="offset">Offset, en octets, dans la mémoire tampon de données où l'opération démarre.</param>
      <param name="count">Quantité maximale de données, en octets, à envoyer ou à recevoir dans la mémoire tampon.</param>
      <exception cref="T:System.ArgumentException">Des mémoires tampons ambiguës sont spécifiées.Cette exception se produit si la valeur des propriétés <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> et <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> n'est pas Null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Un argument est hors limites.Cette exception se produit si le paramètre <paramref name="offset" /> est inférieur à zéro ou supérieur à la longueur du tableau dans la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.Cette exception se produit également si le paramètre <paramref name="count" /> est inférieur à zéro ou supérieur à la longueur du tableau dans la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> moins le paramètre <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Int32,System.Int32)">
      <summary>Définit la mémoire tampon de données à utiliser avec une méthode de socket asynchrone.</summary>
      <param name="offset">Offset, en octets, dans la mémoire tampon de données où l'opération démarre.</param>
      <param name="count">Quantité maximale de données, en octets, à envoyer ou à recevoir dans la mémoire tampon.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Un argument est hors limites.Cette exception se produit si le paramètre <paramref name="offset" /> est inférieur à zéro ou supérieur à la longueur du tableau dans la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.Cette exception se produit également si le paramètre <paramref name="count" /> est inférieur à zéro ou supérieur à la longueur du tableau dans la propriété <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> moins le paramètre <paramref name="offset" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SocketError">
      <summary>Obtient ou définit le résultat de l'opération de socket asynchrone.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.SocketError" /> qui représente le résultat final de l'opération de socket asynchrone.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.UserToken">
      <summary>Obtient ou définit un objet utilisateur ou application associé à cette opération de socket asynchrone.</summary>
      <returns>Objet qui représente l'objet utilisateur ou application associé à cette opération de socket asynchrone.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncOperation">
      <summary>Type d'opération de socket asynchrone exécutée le plus récemment avec cet objet de contexte.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Accept">
      <summary>Opération Accept du socket. </summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Connect">
      <summary>Opération Connect du socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.None">
      <summary>Aucune des opérations de socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Receive">
      <summary>Opération Receive du socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.ReceiveFrom">
      <summary>Opération ReceiveFrom du socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Send">
      <summary>Opération Send du socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.SendTo">
      <summary>Opération SendTo du socket.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketShutdown">
      <summary>Définit les constantes qui sont utilisées par la méthode <see cref="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Both">
      <summary>Désactive <see cref="T:System.Net.Sockets.Socket" /> pour l'envoi et la réception.Ce champ est constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Receive">
      <summary>Désactive <see cref="T:System.Net.Sockets.Socket" /> pour la réception.Ce champ est constant.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Send">
      <summary>Désactive <see cref="T:System.Net.Sockets.Socket" /> pour l'envoi.Ce champ est constant.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketType">
      <summary>Spécifie le type de socket que représente une instance de la classe <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Dgram">
      <summary>Prend en charge des datagrammes, qui sont des messages peu fiables, sans connexion, d'une longueur maximale fixe (généralement réduite).Des messages pourraient être perdus ou dupliqués et arriver dans le désordre.Un <see cref="T:System.Net.Sockets.Socket" /> de type <see cref="F:System.Net.Sockets.SocketType.Dgram" /> ne requiert aucune connexion avant d'envoyer et de recevoir des données, et peut communiquer avec plusieurs homologues.Le champ <see cref="F:System.Net.Sockets.SocketType.Dgram" /> utilise le protocole UDP (<see cref="F:System.Net.Sockets.ProtocolType.Udp" />) et le champ <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /><see cref="T:System.Net.Sockets.AddressFamily" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Stream">
      <summary>Prend en charge les flux d'octets fiables, bidirectionnels, orientés connexion sans la duplication de données et sans préservation de limites.Un Socket de ce type communique avec un homologue unique et nécessite une connexion d'hôte distant avant que la communication puisse débuter.Le champ <see cref="F:System.Net.Sockets.SocketType.Stream" /> utilise le protocole TCP (<see cref="F:System.Net.Sockets.ProtocolType.Tcp" />) <see cref="T:System.Net.Sockets.ProtocolType" /> et InterNetwork<see cref="T:System.Net.Sockets.AddressFamily" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Unknown">
      <summary>Spécifie un type Socket inconnu.</summary>
    </member>
  </members>
</doc>