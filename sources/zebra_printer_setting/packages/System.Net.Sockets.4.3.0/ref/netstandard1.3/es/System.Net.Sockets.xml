<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Net.Sockets</name>
  </assembly>
  <members>
    <member name="T:System.Net.Sockets.ProtocolType">
      <summary>Especifica los protocolos que admite la clase <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Tcp">
      <summary>Protocolo de control de transporte.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Udp">
      <summary>Protocolo de datagramas de usuarios.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unknown">
      <summary>Protocolo desconocido.</summary>
    </member>
    <member name="F:System.Net.Sockets.ProtocolType.Unspecified">
      <summary>Protocolo no especificado.</summary>
    </member>
    <member name="T:System.Net.Sockets.Socket">
      <summary>Implementa la interfaz de sockets Berkeley.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.AddressFamily,System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Inicializa una instancia nueva de la clase <see cref="T:System.Net.Sockets.Socket" /> con la familia de direcciones, el tipo de socket y el protocolo que se especifiquen.</summary>
      <param name="addressFamily">Uno de los valores de <see cref="T:System.Net.Sockets.AddressFamily" />. </param>
      <param name="socketType">Uno de los valores de <see cref="T:System.Net.Sockets.SocketType" />. </param>
      <param name="protocolType">Uno de los valores de <see cref="T:System.Net.Sockets.ProtocolType" />. </param>
      <exception cref="T:System.Net.Sockets.SocketException">La combinación de <paramref name="addressFamily" />, <paramref name="socketType" /> y <paramref name="protocolType" /> tiene como resultado un socket no válido. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.#ctor(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType)">
      <summary>Inicializa una instancia nueva de la clase <see cref="T:System.Net.Sockets.Socket" /> usando el tipo de socket y el protocolo que se especifiquen.</summary>
      <param name="socketType">Uno de los valores de <see cref="T:System.Net.Sockets.SocketType" />.</param>
      <param name="protocolType">Uno de los valores de <see cref="T:System.Net.Sockets.ProtocolType" />.</param>
      <exception cref="T:System.Net.Sockets.SocketException">La combinación de <paramref name="socketType" /> y <paramref name="protocolType" /> da como resultado un socket no válido. </exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Comienza una operación asincrónica para aceptar un intento de conexión entrante.</summary>
      <returns>Devuelve true si la operación de E/S está pendiente.Al completar la operación se provoca el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" />.Devuelve false si la operación de E/S se completó de forma sincrónica.El evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" /> no se provoca y el objeto <paramref name="e" /> que se pasa como parámetro puede examinarse inmediatamente después de que se devuelva la llamada al método para recuperar el resultado de la operación.</returns>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para esta operación de socket asincrónica.</param>
      <exception cref="T:System.ArgumentException">Un argumento no es válido.Esta excepción produce si el búfer proporcionado no es suficientemente grande.El búfer debe ser de al menos 2 bytes * (sizeof(SOCKADDR_STORAGE + 16).Esta excepción también se produce si se especifican varios búferes; es decir, si la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> no es null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Un argumento está fuera de intervalo.La excepción produce si <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Count" /> es menor que 0.</exception>
      <exception cref="T:System.InvalidOperationException">Se ha solicitado una operación no válida.Esta excepción se produce si el <see cref="T:System.Net.Sockets.Socket" /> de aceptación no realiza escuchas para las conexiones o el socket aceptado está enlazado.Debe llamar al método <see cref="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)" /> y <see cref="M:System.Net.Sockets.Socket.Listen(System.Int32)" /> antes de llamar al método <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" />.Esta excepción también se produce si el socket ya está conectado o si ya hay una operación de socket en curso con el parámetro <paramref name="e" /> especificado. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.NotSupportedException">Se requiere Windows XP o posteriores para este método.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.AddressFamily">
      <summary>Obtiene la familia de direcciones de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Uno de los valores de <see cref="T:System.Net.Sockets.AddressFamily" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Bind(System.Net.EndPoint)">
      <summary>Asocia un objeto <see cref="T:System.Net.Sockets.Socket" /> a un extremo local.</summary>
      <param name="localEP">
        <see cref="T:System.Net.EndPoint" /> local que se va a asociar a <see cref="T:System.Net.Sockets.Socket" />. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="localEP" /> es null. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Security.SecurityException">Una llamada situada más arriba en la pila de llamadas no dispone de permiso para la operación solicitada. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Net.SocketPermission, System, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.CancelConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Cancela una solicitud asincrónica de una conexión a un host remoto.</summary>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para solicitar la conexión al host remoto llamando a uno de los métodos <see cref="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)" />.</param>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="e" /> y <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> no puede ser null.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket. </exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Security.SecurityException">Una llamada situada más arriba en la pila de llamadas no dispone de permiso para la operación solicitada.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Comienza una solicitud asincrónica para una conexión a host remoto.</summary>
      <returns>Devuelve true si la operación de E/S está pendiente.Al completar la operación se provoca el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" />.Devuelve false si la operación de E/S se completó de forma sincrónica.En ese caso, el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" /> no se provoca y el objeto <paramref name="e" /> que se pasa como parámetro puede examinarse inmediatamente después de que se devuelva la llamada al método para recuperar el resultado de la operación.</returns>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para esta operación de socket asincrónica.</param>
      <exception cref="T:System.ArgumentException">Un argumento no es válido.Esta excepción también se produce si se especifican varios búferes; es decir, si la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> no es null.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="e" /> y <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> no puede ser null.</exception>
      <exception cref="T:System.InvalidOperationException">El objeto <see cref="T:System.Net.Sockets.Socket" /> está escuchando o ya hay una operación de socket en curso que utiliza el objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> especificado en el parámetro <paramref name="e" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.NotSupportedException">Se requiere Windows XP o posteriores para este método.Esta excepción también se produce si el extremo local y <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> no son la misma familia de direcciones.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Security.SecurityException">Una llamada situada más arriba en la pila de llamadas no dispone de permiso para la operación solicitada.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.ConnectAsync(System.Net.Sockets.SocketType,System.Net.Sockets.ProtocolType,System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Comienza una solicitud asincrónica para una conexión a host remoto.</summary>
      <returns>Devuelve true si la operación de E/S está pendiente.Al completar la operación se provoca el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" />.Devuelve false si la operación de E/S se completó de forma sincrónica.En ese caso, el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" /> no se provoca y el objeto <paramref name="e" /> que se pasa como parámetro puede examinarse inmediatamente después de que se devuelva la llamada al método para recuperar el resultado de la operación.</returns>
      <param name="socketType">Uno de los valores de <see cref="T:System.Net.Sockets.SocketType" />.</param>
      <param name="protocolType">Uno de los valores de <see cref="T:System.Net.Sockets.ProtocolType" />.</param>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para esta operación de socket asincrónica.</param>
      <exception cref="T:System.ArgumentException">Un argumento no es válido.Esta excepción también se produce si se especifican varios búferes; es decir, si la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> no es null.</exception>
      <exception cref="T:System.ArgumentNullException">El valor del parámetro <paramref name="e" /> y <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> no puede ser null.</exception>
      <exception cref="T:System.InvalidOperationException">El objeto <see cref="T:System.Net.Sockets.Socket" /> está escuchando o ya hay una operación de socket en curso que utiliza el objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> especificado en el parámetro <paramref name="e" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.NotSupportedException">Se requiere Windows XP o posteriores para este método.Esta excepción también se produce si el extremo local y <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> no son la misma familia de direcciones.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Security.SecurityException">Una llamada situada más arriba en la pila de llamadas no dispone de permiso para la operación solicitada.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.Connected">
      <summary>Obtiene un valor que indica si <see cref="T:System.Net.Sockets.Socket" /> se conecta con un host remoto a partir de la última operación <see cref="Overload:System.Net.Sockets.Socket.Send" /> u <see cref="Overload:System.Net.Sockets.Socket.Receive" />.</summary>
      <returns>Es true si el objeto <see cref="T:System.Net.Sockets.Socket" /> estaba conectado a un recurso remoto desde la operación más reciente; de lo contrario, es false.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose">
      <summary>Libera todos los recursos usados por la instancia actual de la clase <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Dispose(System.Boolean)">
      <summary>Libera los recursos no administrados que utiliza el objeto <see cref="T:System.Net.Sockets.Socket" /> y, de forma opcional, desecha los recursos administrados.</summary>
      <param name="disposing">Es true para liberar los recursos administrados y no administrados; es false para liberar sólo los recursos no administrados. </param>
    </member>
    <member name="M:System.Net.Sockets.Socket.Finalize">
      <summary>Libera los recursos utilizados por la clase <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="M:System.Net.Sockets.Socket.Listen(System.Int32)">
      <summary>Coloca un objeto <see cref="T:System.Net.Sockets.Socket" /> en un estado de escucha.</summary>
      <param name="backlog">Longitud máxima de la cola de conexiones pendientes. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.LocalEndPoint">
      <summary>Obtiene el extremo local.</summary>
      <returns>
        <see cref="T:System.Net.EndPoint" /> que utiliza el <see cref="T:System.Net.Sockets.Socket" /> para las comunicaciones.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.NoDelay">
      <summary>Obtiene o establece un valor de <see cref="T:System.Boolean" /> que especifica si la secuencia <see cref="T:System.Net.Sockets.Socket" /> está utilizando el algoritmo de Nagle.</summary>
      <returns>false si <see cref="T:System.Net.Sockets.Socket" /> utiliza el algoritmo de Nagle; de lo contrario, true.El valor predeterminado es false.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Error al intentar obtener acceso a <see cref="T:System.Net.Sockets.Socket" />.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv4">
      <summary>Indica si el sistema operativo subyacente y los adaptadores de red admiten la versión 4 del protocolo de Internet (IPv4).</summary>
      <returns>Es true si el sistema operativo y los adaptadores de red admiten el protocolo IPv4; de lo contrario, es false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.OSSupportsIPv6">
      <summary>Indica si el sistema operativo subyacente y los adaptadores de red admiten la versión 6 del protocolo Internet (IPv6).</summary>
      <returns>true si el sistema operativo y los adaptadores de red admiten el protocolo IPv6; de lo contrario, false.</returns>
    </member>
    <member name="P:System.Net.Sockets.Socket.ProtocolType">
      <summary>Obtiene el tipo de protocolo de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Uno de los valores de <see cref="T:System.Net.Sockets.ProtocolType" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Comienza una solicitud asincrónica para recibir los datos de un objeto <see cref="T:System.Net.Sockets.Socket" /> conectado.</summary>
      <returns>Devuelve true si la operación de E/S está pendiente.Al completar la operación se provoca el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" />.Devuelve false si la operación de E/S se completó de forma sincrónica.En ese caso, el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" /> no se provoca y el objeto <paramref name="e" /> que se pasa como parámetro puede examinarse inmediatamente después de que se devuelva la llamada al método para recuperar el resultado de la operación.</returns>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para esta operación de socket asincrónica.</param>
      <exception cref="T:System.ArgumentException">Un argumento no era válido.Las propiedades <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> o <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> del parámetro <paramref name="e" /> deben hacer referencia a los búferes válidos.Se puede establecer una de estas propiedades, pero no ambas al mismo tiempo.</exception>
      <exception cref="T:System.InvalidOperationException">Ya hay una operación de socket en curso que utiliza el objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> especificado en el parámetro <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Se requiere Windows XP o posteriores para este método.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.ReceiveBufferSize">
      <summary>Obtiene o establece un valor que especifica el tamaño del búfer de recepción de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>
        <see cref="T:System.Int32" /> que contiene el tamaño, en bytes, del búfer de recepción.El valor predeterminado es 8192</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor especificado para una operación de establecimiento es menor que 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.ReceiveFromAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Comienza a recibir asincrónicamente los datos de un dispositivo de red especificado.</summary>
      <returns>Devuelve true si la operación de E/S está pendiente.Al completar la operación se provoca el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" />.Devuelve false si la operación de E/S se completó de forma sincrónica.En ese caso, el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" /> no se provoca y el objeto <paramref name="e" /> que se pasa como parámetro puede examinarse inmediatamente después de que se devuelva la llamada al método para recuperar el resultado de la operación.</returns>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para esta operación de socket asincrónica.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> no puede ser null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya hay una operación de socket en curso que utiliza el objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> especificado en el parámetro <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Se requiere Windows XP o posteriores para este método.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket. </exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.RemoteEndPoint">
      <summary>Obtiene el extremo remoto.</summary>
      <returns>
        <see cref="T:System.Net.EndPoint" /> con el que está comunicando el <see cref="T:System.Net.Sockets.Socket" />.</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Envía datos de forma asincrónica a un objeto <see cref="T:System.Net.Sockets.Socket" /> conectado.</summary>
      <returns>Devuelve true si la operación de E/S está pendiente.Al completar la operación se provoca el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" />.Devuelve false si la operación de E/S se completó de forma sincrónica.En ese caso, el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" /> no se provoca y el objeto <paramref name="e" /> que se pasa como parámetro puede examinarse inmediatamente después de que se devuelva la llamada al método para recuperar el resultado de la operación.</returns>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para esta operación de socket asincrónica.</param>
      <exception cref="T:System.ArgumentException">Las propiedades <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> o <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> del parámetro <paramref name="e" /> deben hacer referencia a los búferes válidos.Se puede establecer una de estas propiedades, pero no ambas al mismo tiempo.</exception>
      <exception cref="T:System.InvalidOperationException">Ya hay una operación de socket en curso que utiliza el objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> especificado en el parámetro <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Se requiere Windows XP o posteriores para este método.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">El <see cref="T:System.Net.Sockets.Socket" /> no está conectado todavía o no se obtuvo a través de un método <see cref="M:System.Net.Sockets.Socket.Accept" />, <see cref="M:System.Net.Sockets.Socket.AcceptAsync(System.Net.Sockets.SocketAsyncEventArgs)" /> o <see cref="Overload:System.Net.Sockets.Socket.BeginAccept" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.Socket.SendBufferSize">
      <summary>Obtiene o establece un valor que especifica el tamaño del búfer de envío de <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>
        <see cref="T:System.Int32" /> que contiene el tamaño, en bytes, del búfer de envío.El valor predeterminado es 8192</returns>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor especificado para una operación de establecimiento es menor que 0.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:System.Net.Sockets.Socket.SendToAsync(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Envía datos asincrónicamente a un determinado host remoto.</summary>
      <returns>Devuelve true si la operación de E/S está pendiente.Al completar la operación se provoca el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" />.Devuelve false si la operación de E/S se completó de forma sincrónica.En ese caso, el evento <see cref="E:System.Net.Sockets.SocketAsyncEventArgs.Completed" /> del parámetro <paramref name="e" /> no se provoca y el objeto <paramref name="e" /> que se pasa como parámetro puede examinarse inmediatamente después de que se devuelva la llamada al método para recuperar el resultado de la operación.</returns>
      <param name="e">Objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> que se usa para esta operación de socket asincrónica.</param>
      <exception cref="T:System.ArgumentNullException">
        <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" /> no puede ser null.</exception>
      <exception cref="T:System.InvalidOperationException">Ya hay una operación de socket en curso que utiliza el objeto <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> especificado en el parámetro <paramref name="e" />.</exception>
      <exception cref="T:System.NotSupportedException">Se requiere Windows XP o posteriores para este método.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <exception cref="T:System.Net.Sockets.SocketException">El protocolo especificado está orientado a la conexión, pero el <see cref="T:System.Net.Sockets.Socket" /> no está conectado todavía.</exception>
    </member>
    <member name="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)">
      <summary>Deshabilita los envíos y recepciones en un objeto <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <param name="how">Uno de los valores de <see cref="T:System.Net.Sockets.SocketShutdown" /> que especifica la operación que ya no estará permitida. </param>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.Vea la sección Comentarios para obtener más información.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.Net.Sockets.Socket.Ttl">
      <summary>Obtiene o establece un valor que especifica el valor de período de vida (TTL) de los paquetes de protocolo Internet (IP) enviados por <see cref="T:System.Net.Sockets.Socket" />.</summary>
      <returns>Valor TTL.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">El valor TTL no se puede establecer en un número negativo.</exception>
      <exception cref="T:System.NotSupportedException">Esta propiedad sólo se puede establecer para sockets de las familias de <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" /> o <see cref="F:System.Net.Sockets.AddressFamily.InterNetworkV6" />.</exception>
      <exception cref="T:System.Net.Sockets.SocketException">Se ha producido un error al intentar obtener acceso al socket.También se devuelve este error cuando se ha intentado para establecer TTL en un valor superior a 255.</exception>
      <exception cref="T:System.ObjectDisposedException">Se ha cerrado el objeto <see cref="T:System.Net.Sockets.Socket" />. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.EnvironmentPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncEventArgs">
      <summary>Representa una operación de socket asincrónico.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.#ctor">
      <summary>Crea una instancia de <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> vacía.</summary>
      <exception cref="T:System.NotSupportedException">No se admite la plataforma. </exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.AcceptSocket">
      <summary>Obtiene o establece el socket que se va a usar o el socket creado para aceptar una conexión con un método de socket asincrónico.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.Socket" /> que se va a usar o socket creado para aceptar una conexión con un método de socket asincrónico.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer">
      <summary>Obtiene el búfer de datos que se va a usar con un método de socket asincrónico.</summary>
      <returns>Matriz <see cref="T:System.Byte" /> que representa el búfer de datos que se va a usar con un método de socket asincrónico.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList">
      <summary>Obtiene o establece una matriz de búferes de datos que se va a usar con un método de socket asincrónico.</summary>
      <returns>
        <see cref="T:System.Collections.IList" /> que representa una matriz de búferes de datos que se va a usar con un método de socket asincrónico.</returns>
      <exception cref="T:System.ArgumentException">Se han especificado búferes ambiguos en una operación de establecimiento.Esta excepción se produce si la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> se ha establecido en un valor no nulo y se intenta establecer la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> en un valor no nulo.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.BytesTransferred">
      <summary>Obtiene el número de bytes transferidos en la operación de socket.</summary>
      <returns>
        <see cref="T:System.Int32" /> que contiene el número de bytes transferidos en la operación de socket.</returns>
    </member>
    <member name="E:System.Net.Sockets.SocketAsyncEventArgs.Completed">
      <summary>Evento utilizado para completar una operación asincrónica.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectByNameError">
      <summary>Obtiene la excepción en el caso de un error de conexión cuando se usó <see cref="T:System.Net.DnsEndPoint" />.</summary>
      <returns>Objeto <see cref="T:System.Exception" /> que indica la causa del error de conexión que se produce cuando se especifica un objeto <see cref="T:System.Net.DnsEndPoint" /> para la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint" />.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.ConnectSocket">
      <summary>Objeto <see cref="T:System.Net.Sockets.Socket" /> que se ha creado y conectado después de finalizar correctamente el método <see cref="Overload:System.Net.Sockets.Socket.ConnectAsync" />.</summary>
      <returns>Objeto <see cref="T:System.Net.Sockets.Socket" /> conectado.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Count">
      <summary>Obtiene la cantidad máxima de datos, en bytes, que se van a enviar o recibir en una operación asincrónica.</summary>
      <returns>
        <see cref="T:System.Int32" /> que contiene la cantidad máxima de datos, en bytes, que se van a enviar o recibir.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Dispose">
      <summary>Libera los recursos no administrados utilizados por la instancia de <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" /> y, de forma opcional, elimina los recursos administrados.</summary>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.Finalize">
      <summary>Libera los recursos utilizados por la clase <see cref="T:System.Net.Sockets.SocketAsyncEventArgs" />.</summary>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.LastOperation">
      <summary>Obtiene el tipo de operación de socket más reciente realizada con este objeto de contexto.</summary>
      <returns>Instancia de <see cref="T:System.Net.Sockets.SocketAsyncOperation" /> que indica el tipo de operación de socket más reciente realizada con este objeto de contexto.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.Offset">
      <summary>Obtiene el desplazamiento, en bytes, en el búfer de datos al que hace referencia la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.</summary>
      <returns>
        <see cref="T:System.Int32" /> que contiene el desplazamiento, en bytes, en el búfer de datos al que hace referencia la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.OnCompleted(System.Net.Sockets.SocketAsyncEventArgs)">
      <summary>Representa un método al que se llama cuando se completa una operación asincrónica.</summary>
      <param name="e">Evento que se señala.</param>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.RemoteEndPoint">
      <summary>Obtiene o establece el extremo IP remoto de una operación asincrónica.</summary>
      <returns>
        <see cref="T:System.Net.EndPoint" /> que representa el extremo IP remoto para una operación asincrónica.</returns>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Byte[],System.Int32,System.Int32)">
      <summary>Establece el búfer de datos que se va a usar con un método de socket asincrónico.</summary>
      <param name="buffer">Búfer de datos que se va a usar con un método de socket asincrónico.</param>
      <param name="offset">Desplazamiento, en bytes, en el búfer de datos donde se inicia la operación.</param>
      <param name="count">Cantidad máxima de datos, en bytes, que se van a enviar o recibir en el búfer.</param>
      <exception cref="T:System.ArgumentException">Se especificaron búferes ambiguos.Esta excepción se produce si las propiedades <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> y <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.BufferList" /> tampoco son null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Un argumento estaba fuera de intervalo.Esta excepción se produce si el parámetro <paramref name="offset" /> es menor que cero o mayor que la longitud de la matriz en la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.Esta excepción también se produce si el parámetro <paramref name="count" /> es menor que cero o mayor que la longitud de la matriz en la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> menos el parámetro <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Net.Sockets.SocketAsyncEventArgs.SetBuffer(System.Int32,System.Int32)">
      <summary>Establece el búfer de datos que se va a usar con un método de socket asincrónico.</summary>
      <param name="offset">Desplazamiento, en bytes, en el búfer de datos donde se inicia la operación.</param>
      <param name="count">Cantidad máxima de datos, en bytes, que se van a enviar o recibir en el búfer.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">Un argumento estaba fuera de intervalo.Esta excepción se produce si el parámetro <paramref name="offset" /> es menor que cero o mayor que la longitud de la matriz en la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" />.Esta excepción también se produce si el parámetro <paramref name="count" /> es menor que cero o mayor que la longitud de la matriz en la propiedad <see cref="P:System.Net.Sockets.SocketAsyncEventArgs.Buffer" /> menos el parámetro <paramref name="offset" />.</exception>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.SocketError">
      <summary>Obtiene o establece el resultado de la operación de socket asincrónico.</summary>
      <returns>
        <see cref="T:System.Net.Sockets.SocketError" /> que representa el resultado de la operación de socket asincrónico.</returns>
    </member>
    <member name="P:System.Net.Sockets.SocketAsyncEventArgs.UserToken">
      <summary>Obtiene o establece a un objeto de usuario o de aplicación asociado a esta operación de socket asincrónico.</summary>
      <returns>Objeto que representa al objeto de usuario o de aplicación asociado a esta operación de socket asincrónico.</returns>
    </member>
    <member name="T:System.Net.Sockets.SocketAsyncOperation">
      <summary>El tipo de operación del socket asincrónica más reciente realizada con este objeto de contexto.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Accept">
      <summary>Un operación Accept del socket. </summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Connect">
      <summary>Una operación Connect del socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.None">
      <summary>Ninguna de las operaciones del socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Receive">
      <summary>Una operación Receive del socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.ReceiveFrom">
      <summary>Una operación ReceiveFrom del socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.Send">
      <summary>Una operación Send del socket.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketAsyncOperation.SendTo">
      <summary>Operación SendTo del socket.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketShutdown">
      <summary>Define las constantes utilizadas por el método <see cref="M:System.Net.Sockets.Socket.Shutdown(System.Net.Sockets.SocketShutdown)" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Both">
      <summary>Deshabilita un objeto <see cref="T:System.Net.Sockets.Socket" /> tanto para el envío como para la recepción.Este campo es constante.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Receive">
      <summary>Deshabilita un objeto <see cref="T:System.Net.Sockets.Socket" /> para la recepción.Este campo es constante.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketShutdown.Send">
      <summary>Deshabilita un objeto <see cref="T:System.Net.Sockets.Socket" /> para el envío.Este campo es constante.</summary>
    </member>
    <member name="T:System.Net.Sockets.SocketType">
      <summary>Especifica el tipo de socket que representa una instancia de la clase <see cref="T:System.Net.Sockets.Socket" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Dgram">
      <summary>Admite datagramas, que son mensajes no confiables sin conexión con una longitud máxima fija (normalmente corta).Los mensajes pueden perderse o duplicarse y llegar desordenados.Un objeto <see cref="T:System.Net.Sockets.Socket" /> de tipo <see cref="F:System.Net.Sockets.SocketType.Dgram" /> no necesita conexión antes de enviar y recibir datos, y puede comunicarse con varios elementos del mismo nivel.<see cref="F:System.Net.Sockets.SocketType.Dgram" /> usa el protocolo de datagramas (<see cref="F:System.Net.Sockets.ProtocolType.Udp" />) y <see cref="T:System.Net.Sockets.AddressFamily" /> de <see cref="F:System.Net.Sockets.AddressFamily.InterNetwork" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Stream">
      <summary>Admite secuencias de bytes bidireccionales confiables, basadas en conexión, sin duplicidad de datos ni conservación de límites.Un objeto Socket de este tipo se comunica con un solo elemento del mismo nivel y requiere una conexión con el host remoto para poder iniciar la comunicación.<see cref="F:System.Net.Sockets.SocketType.Stream" /> usa el protocolo TCP (Protocolo de control de transporte, <see cref="F:System.Net.Sockets.ProtocolType.Tcp" />) <see cref="T:System.Net.Sockets.ProtocolType" /> y la familia de direcciones InterNetwork<see cref="T:System.Net.Sockets.AddressFamily" />.</summary>
    </member>
    <member name="F:System.Net.Sockets.SocketType.Unknown">
      <summary>Especifica un tipo de Socket desconocido.</summary>
    </member>
  </members>
</doc>