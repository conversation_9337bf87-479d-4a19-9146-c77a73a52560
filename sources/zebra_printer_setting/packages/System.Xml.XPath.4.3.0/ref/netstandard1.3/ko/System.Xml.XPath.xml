<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Xml.XPath</name>
  </assembly>
  <members>
    <member name="T:System.Xml.XmlNodeOrder">
      <summary>두 번째 노드와 비교하여 노드의 문서 순서를 설명합니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeOrder.After">
      <summary>이 검색기의 현재 노드는 제공된 검색기의 현재 노드 다음에 있습니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeOrder.Before">
      <summary>이 검색기의 현재 노드는 제공된 검색기의 현재 노드 전에 있습니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeOrder.Same">
      <summary>두 검색기가 같은 노드에 있습니다.</summary>
    </member>
    <member name="F:System.Xml.XmlNodeOrder.Unknown">
      <summary>다른 노드에 상대적인 문서 순서로 노드 위치를 확인할 수 없습니다.두 노드가 서로 다른 트리에 상주할 경우에 이런 일이 발생할 수 있습니다.</summary>
    </member>
    <member name="T:System.Xml.XPath.IXPathNavigable">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" /> 클래스에 대한 접근자를 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.IXPathNavigable.CreateNavigator">
      <summary>새 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체를 반환합니다. </summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체</returns>
    </member>
    <member name="T:System.Xml.XPath.XmlCaseOrder">
      <summary>대문자와 소문자에 대한 정렬 순서를 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XmlCaseOrder.LowerFirst">
      <summary>소문자가 대문자보다 앞에 정렬됩니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XmlCaseOrder.None">
      <summary>대/소문자를 무시합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XmlCaseOrder.UpperFirst">
      <summary>대문자가 소문자보다 앞에 정렬됩니다.</summary>
    </member>
    <member name="T:System.Xml.XPath.XmlDataType">
      <summary>정렬 순서를 결정하는 데 사용되는 데이터 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XmlDataType.Number">
      <summary>값은 숫자 순으로 정렬됩니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XmlDataType.Text">
      <summary>값은 알파벳 순으로 정렬됩니다.</summary>
    </member>
    <member name="T:System.Xml.XPath.XmlSortOrder">
      <summary>정렬 순서를 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XmlSortOrder.Ascending">
      <summary>노드가 오름차순으로 정렬됩니다.예를 들어, 숫자 1, 2, 3, 4를 오름차순으로 정렬하면 1, 2, 3, 4가 됩니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XmlSortOrder.Descending">
      <summary>노드가 내림차순으로 정렬됩니다.예를 들어, 숫자 1, 2, 3, 4를 내림차순으로 정렬하면 4, 3, 2, 1이 됩니다.</summary>
    </member>
    <member name="T:System.Xml.XPath.XPathDocument">
      <summary>XPath 데이터 모델을 사용하여 XML 문서에 대한 읽기 전용의 빠른 메모리 내 표현을 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.IO.Stream)">
      <summary>지정된 <see cref="T:System.IO.Stream" /> 개체의 XML 데이터를 사용하여 <see cref="T:System.Xml.XPath.XPathDocument" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="stream">XML 데이터가 포함된 <see cref="T:System.IO.Stream" /> 개체입니다.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data.The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.IO.Stream" /> object passed as a parameter is null.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.IO.TextReader)">
      <summary>지정된 <see cref="T:System.IO.TextReader" /> 개체에 포함된 XML 데이터를 사용하여 <see cref="T:System.Xml.XPath.XPathDocument" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="textReader">XML 데이터가 포함된 <see cref="T:System.IO.TextReader" /> 개체입니다.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data.The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.IO.TextReader" /> object passed as a parameter is null.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.String)">
      <summary>지정된 파일의 XML 데이터를 사용하여 <see cref="T:System.Xml.XPath.XPathDocument" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="uri">XML 데이터가 포함된 파일의 경로입니다.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data.The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The file path parameter is null.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.String,System.Xml.XmlSpace)">
      <summary>공백 처리 옵션이 지정된 상태로, 지정된 파일의 XML 데이터를 사용하여 <see cref="T:System.Xml.XPath.XPathDocument" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="uri">XML 데이터가 포함된 파일의 경로입니다.</param>
      <param name="space">
        <see cref="T:System.Xml.XmlSpace" /> 개체</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data.The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The file path parameter or <see cref="T:System.Xml.XmlSpace" /> object parameter is null.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.Xml.XmlReader)">
      <summary>지정된 <see cref="T:System.Xml.XmlReader" /> 개체에 포함된 XML 데이터를 사용하여 <see cref="T:System.Xml.XPath.XPathDocument" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="reader">XML 데이터가 포함된 <see cref="T:System.Xml.XmlReader" /> 개체입니다. </param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data.The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object passed as a parameter is null.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.#ctor(System.Xml.XmlReader,System.Xml.XmlSpace)">
      <summary>공백 처리 옵션이 지정된 상태로 <see cref="T:System.Xml.XmlReader" /> 개체의 XML 데이터를 사용하여 <see cref="T:System.Xml.XPath.XPathDocument" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="reader">XML 데이터가 포함된 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</param>
      <param name="space">
        <see cref="T:System.Xml.XmlSpace" /> 개체입니다.</param>
      <exception cref="T:System.Xml.XmlException">An error was encountered in the XML data.The <see cref="T:System.Xml.XPath.XPathDocument" /> remains empty.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object parameter or <see cref="T:System.Xml.XmlSpace" /> object parameter is null.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathDocument.CreateNavigator">
      <summary>이 <see cref="T:System.Xml.XPath.XPathDocument" />의 노드를 탐색하기 위한 읽기 전용의 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체를 초기화합니다.</summary>
      <returns>읽기 전용 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</returns>
    </member>
    <member name="T:System.Xml.XPath.XPathException">
      <summary>XPath 식을 처리하는 동안 오류가 발생할 경우 throw되는 예외를 제공합니다. </summary>
    </member>
    <member name="M:System.Xml.XPath.XPathException.#ctor">
      <summary>
        <see cref="T:System.Xml.XPath.XPathException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathException.#ctor(System.String)">
      <summary>지정된 예외 메시지를 사용하여 <see cref="T:System.Xml.XPath.XPathException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류 조건에 대한 설명입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathException.#ctor(System.String,System.Exception)">
      <summary>지정된 예외 메시지와 <see cref="T:System.Exception" /> 개체를 사용하여 <see cref="T:System.Xml.XPath.XPathException" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="message">오류 조건에 대한 설명입니다. </param>
      <param name="innerException">
        <see cref="T:System.Xml.XPath.XPathException" />을 throw한 <see cref="T:System.Exception" />입니다.이 값은 null일 수 있습니다.</param>
    </member>
    <member name="T:System.Xml.XPath.XPathExpression">
      <summary>컴파일된 XPath 식을 나타내는 형식화된 클래스를 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathExpression.AddSort(System.Object,System.Collections.IComparer)">
      <summary>파생 클래스에서 재정의된 경우 XPath 식에서 선택한 노드를 지정된 <see cref="T:System.Collections.IComparer" /> 개체에 따라 정렬합니다.</summary>
      <param name="expr">정렬 키를 나타내는 개체입니다.이 개체는 노드의 string 값 또는 컴파일된 XPath 식을 가진 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체가 될 수 있습니다.</param>
      <param name="comparer">두 개체가 서로 같은지 비교하는 데 사용할 특정 데이터 형식 비교를 제공하는 <see cref="T:System.Collections.IComparer" /> 개체입니다. </param>
      <exception cref="T:System.Xml.XPath.XPathException">
        <see cref="T:System.Xml.XPath.XPathExpression" /> 또는 정렬 키가 접두사를 포함한 상황에서 <see cref="T:System.Xml.XmlNamespaceManager" />가 제공되지 않았거나 제공된 <see cref="T:System.Xml.XmlNamespaceManager" />에서 접두사를 찾을 수 없습니다.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathExpression.AddSort(System.Object,System.Xml.XPath.XmlSortOrder,System.Xml.XPath.XmlCaseOrder,System.String,System.Xml.XPath.XmlDataType)">
      <summary>파생 클래스에서 재정의된 경우 XPath 식에서 선택한 노드를 전달된 매개 변수에 따라 정렬합니다.</summary>
      <param name="expr">정렬 키를 나타내는 개체입니다.이 개체는 노드의 string 값 또는 컴파일된 XPath 식을 가진 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체가 될 수 있습니다.</param>
      <param name="order">정렬 순서를 나타내는 <see cref="T:System.Xml.XPath.XmlSortOrder" /> 값입니다. </param>
      <param name="caseOrder">대/소문자 정렬 방법을 나타내는 <see cref="T:System.Xml.XPath.XmlCaseOrder" /> 값입니다.</param>
      <param name="lang">비교에 사용되는 언어입니다.언어 형식(미국 영어의 경우 "us-en")에 대한 <see cref="Overload:System.String.Compare" /> 메서드에 전달할 수 있는 <see cref="T:System.Globalization.CultureInfo" /> 클래스를 사용합니다.빈 문자열을 지정하면 시스템 환경을 사용하여 <see cref="T:System.Globalization.CultureInfo" />를 결정합니다.</param>
      <param name="dataType">데이터 형식에 대한 정렬 순서를 나타내는 <see cref="T:System.Xml.XPath.XmlDataType" /> 값입니다. </param>
      <exception cref="T:System.Xml.XPath.XPathException">
        <see cref="T:System.Xml.XPath.XPathExpression" /> 또는 정렬 키가 접두사를 포함한 상황에서 <see cref="T:System.Xml.XmlNamespaceManager" />가 제공되지 않았거나 제공된 <see cref="T:System.Xml.XmlNamespaceManager" />에서 접두사를 찾을 수 없습니다. </exception>
    </member>
    <member name="M:System.Xml.XPath.XPathExpression.Clone">
      <summary>파생 클래스에서 재정의된 경우 이 <see cref="T:System.Xml.XPath.XPathExpression" />의 복제를 반환합니다.</summary>
      <returns>새 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathExpression.Compile(System.String)">
      <summary>지정된 XPath 식을 컴파일하고 XPath 식을 나타내는 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathExpression" /> 개체</returns>
      <param name="xpath">XPath 식입니다.</param>
      <exception cref="T:System.ArgumentException">XPath 식 매개 변수가 올바른 XPath 식이 아닙니다.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">XPath 식이 올바르지 않은 경우</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathExpression.Compile(System.String,System.Xml.IXmlNamespaceResolver)">
      <summary>네임스페이스 확인을 위해 지정된 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체를 사용하여 XPath 식을 컴파일하고 XPath 식을 나타내는 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathExpression" /> 개체</returns>
      <param name="xpath">XPath 식입니다.</param>
      <param name="nsResolver">네임스페이스 확인을 위한 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 인터페이스를 구현하는 개체입니다.</param>
      <exception cref="T:System.ArgumentException">XPath 식 매개 변수가 올바른 XPath 식이 아닙니다.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">XPath 식이 올바르지 않은 경우</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathExpression.Expression">
      <summary>파생 클래스에서 재정의된 경우 <see cref="T:System.Xml.XPath.XPathExpression" />의 string 표현을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathExpression" />의 string 표현입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathExpression.ReturnType">
      <summary>파생 클래스에서 재정의된 경우 XPath 식의 결과 형식을 가져옵니다.</summary>
      <returns>XPath 식의 결과 형식을 나타내는 <see cref="T:System.Xml.XPath.XPathResultType" /> 값입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.IXmlNamespaceResolver)">
      <summary>파생 클래스에서 재정의된 경우 네임스페이스 확인에 사용할 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체를 지정합니다.</summary>
      <param name="nsResolver">네임스페이스 확인에 사용할 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 인터페이스를 구현하는 개체입니다.</param>
      <exception cref="T:System.Xml.XPath.XPathException">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체 매개 변수가 <see cref="T:System.Xml.IXmlNamespaceResolver" />에서 파생되지 않았습니다. </exception>
    </member>
    <member name="M:System.Xml.XPath.XPathExpression.SetContext(System.Xml.XmlNamespaceManager)">
      <summary>파생 클래스에서 재정의된 경우 네임스페이스 확인에 사용할 <see cref="T:System.Xml.XmlNamespaceManager" /> 개체를 지정합니다.</summary>
      <param name="nsManager">네임스페이스 확인에 사용할 <see cref="T:System.Xml.XmlNamespaceManager" /> 개체입니다. </param>
      <exception cref="T:System.Xml.XPath.XPathException">
        <see cref="T:System.Xml.XmlNamespaceManager" /> 개체 매개 변수가 <see cref="T:System.Xml.XmlNamespaceManager" />에서 파생되지 않았습니다. </exception>
    </member>
    <member name="T:System.Xml.XPath.XPathItem">
      <summary>XQuery 1.0 및 XPath 2.0 Data Model의 항목을 나타냅니다.</summary>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.IsNode">
      <summary>파생 클래스에서 재정의된 경우 항목이 XPath 노드를 나타내는지 또는 atomic 값을 나타내는지를 가리키는 값을 가져옵니다.</summary>
      <returns>항목이 XPath 노드를 나타내면 true이고 atomic 값을 나타내면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.TypedValue">
      <summary>파생 클래스에서 재정의된 경우 현재 항목을 스키마 형식에 따라 가장 적합한 .NET Framework 2.0 형식의 boxed 개체로 가져옵니다.</summary>
      <returns>가장 적합한 .NET Framework 형식의 boxed 개체로서의 현재 항목입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.Value">
      <summary>파생 클래스에서 재정의된 경우 항목의 string 값을 가져옵니다.</summary>
      <returns>항목의 string 값입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathItem.ValueAs(System.Type)">
      <summary>항목의 값을 지정된 형식으로 반환합니다.</summary>
      <returns>요청한 형식으로 나타낸 항목의 값입니다.</returns>
      <param name="returnType">항목 값을 반환할 때 적용되는 형식입니다.</param>
      <exception cref="T:System.FormatException">항목의 값 형식이 대상 형식에 맞지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">시도된 캐스팅에서 오버플로가 발생했습니다.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathItem.ValueAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>파생 클래스에서 재정의된 경우 네임스페이스 접두사 확인을 위해 지정된 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체를 사용하여 항목의 값을 지정된 형식으로 반환합니다.</summary>
      <returns>요청한 형식으로 나타낸 항목의 값입니다.</returns>
      <param name="returnType">항목 값을 반환하는 데 사용할 형식입니다.</param>
      <param name="nsResolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체는 네임스페이스 접두사를 확인하는 데 사용됩니다.</param>
      <exception cref="T:System.FormatException">항목의 값 형식이 대상 형식에 맞지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">시도된 캐스팅에서 오버플로가 발생했습니다.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.ValueAsBoolean">
      <summary>파생 클래스에서 재정의된 경우 항목의 값을 <see cref="T:System.Boolean" /> 형식으로 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Boolean" /> 형식의 항목 값입니다.</returns>
      <exception cref="T:System.FormatException">항목의 값 형식이 <see cref="T:System.Boolean" /> 형식과 맞지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <see cref="T:System.Boolean" />에 대해 시도된 캐스팅이 잘못된 경우</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.ValueAsDateTime">
      <summary>파생 클래스에서 재정의된 경우 항목의 값을 <see cref="T:System.DateTime" /> 형식으로 가져옵니다.</summary>
      <returns>
        <see cref="T:System.DateTime" /> 형식의 항목 값입니다.</returns>
      <exception cref="T:System.FormatException">항목의 값 형식이 <see cref="T:System.DateTime" /> 형식과 맞지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <see cref="T:System.DateTime" />에 대해 시도된 캐스팅이 잘못된 경우</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.ValueAsDouble">
      <summary>파생 클래스에서 재정의된 경우 항목의 값을 <see cref="T:System.Double" /> 형식으로 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Double" /> 형식의 항목 값입니다.</returns>
      <exception cref="T:System.FormatException">항목의 값 형식이 <see cref="T:System.Double" /> 형식과 맞지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <see cref="T:System.Double" />에 대해 시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">시도된 캐스팅에서 오버플로가 발생했습니다.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.ValueAsInt">
      <summary>파생 클래스에서 재정의된 경우 항목의 값을 <see cref="T:System.Int32" /> 형식으로 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int32" /> 형식의 항목 값입니다.</returns>
      <exception cref="T:System.FormatException">항목의 값 형식이 <see cref="T:System.Int32" /> 형식과 맞지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <see cref="T:System.Int32" />에 대해 시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">시도된 캐스팅에서 오버플로가 발생했습니다.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.ValueAsLong">
      <summary>파생 클래스에서 재정의된 경우 항목의 값을 <see cref="T:System.Int64" /> 형식으로 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Int64" /> 형식의 항목 값입니다.</returns>
      <exception cref="T:System.FormatException">항목의 값 형식이 <see cref="T:System.Int64" /> 형식과 맞지 않습니다.</exception>
      <exception cref="T:System.InvalidCastException">
        <see cref="T:System.Int64" />에 대해 시도된 캐스팅이 잘못된 경우</exception>
      <exception cref="T:System.OverflowException">시도된 캐스팅에서 오버플로가 발생했습니다.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathItem.ValueType">
      <summary>파생된 클래스에서 재정의되는 경우 항목의 .NET Framework 2.0 형식을 가져옵니다.</summary>
      <returns>항목의 .NET Framework형식입니다.기본값은 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="T:System.Xml.XPath.XPathNamespaceScope">
      <summary>네임스페이스 범위를 정의합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNamespaceScope.All">
      <summary>현재 노드의 범위에서 정의된 모든 네임스페이스를 반환합니다.여기에는 항상 암시적으로 선언되는 xmlns:xml 네임스페이스가 포함됩니다.반환되는 네임스페이스의 순서는 정의되지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNamespaceScope.ExcludeXml">
      <summary>xmlns:xml 네임스페이스를 제외하고 현재 노드의 범위에 정의된 모든 네임스페이스를 반환합니다.xmlns:xml 네임스페이스는 항상 암시적으로 선언됩니다.반환되는 네임스페이스의 순서는 정의되지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNamespaceScope.Local">
      <summary>현재 노드에서 로컬로 정의된 네임스페이스를 모두 반환합니다. </summary>
    </member>
    <member name="T:System.Xml.XPath.XPathNavigator">
      <summary>XML 데이터를 탐색하고 편집하기 위한 커서 모델을 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.#ctor">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.AppendChild">
      <summary>현재 노드의 자식 노드 목록 끝에 새 자식 노드를 하나 이상 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체를 반환합니다. </summary>
      <returns>현재 노드의 자식 노드 목록 끝에 새 자식 노드를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on is not the root node or an element node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.AppendChild(System.String)">
      <summary>지정된 XML 데이터 문자열을 사용하여 현재 노드의 자식 노드 목록 끝에 새 자식 노드를 만듭니다.</summary>
      <param name="newChild">새 자식 노드에 대한 XML 데이터 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">The XML data string parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on is not the root node or an element node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML data string parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.AppendChild(System.Xml.XmlReader)">
      <summary>지정된 <see cref="T:System.Xml.XmlReader" /> 개체의 XML 콘텐츠를 사용하여 현재 노드의 자식 노드 목록 끝에 새 자식 노드를 만듭니다.</summary>
      <param name="newChild">새 자식 노드에 대한 XML 데이터에 있는 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Xml.XmlReader" /> object is in an error state or closed.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on is not the root node or an element node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML contents of the <see cref="T:System.Xml.XmlReader" /> object parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.AppendChild(System.Xml.XPath.XPathNavigator)">
      <summary>지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />에 있는 노드를 사용하여 현재 노드의 자식 노드 목록 끝에 새 자식 노드를 만듭니다.</summary>
      <param name="newChild">새 자식 노드로 추가할 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on is not the root node or an element node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.AppendChildElement(System.String,System.String,System.String,System.String)">
      <summary>지정된 값과 함께 지정된 네임스페이스 접두사, 로컬 이름 및 네임스페이스 URI를 사용하여 현재 노드의 자식 노드 목록 끝에 새 자식 요소 노드를 만듭니다.</summary>
      <param name="prefix">새 자식 요소 노드의 네임스페이스 접두사입니다(있는 경우).</param>
      <param name="localName">새 자식 요소 노드의 로컬 이름입니다(있는 경우).</param>
      <param name="namespaceURI">새 자식 요소 노드의 네임스페이스 URI입니다(있는 경우).<see cref="F:System.String.Empty" /> 및 null은 같습니다.</param>
      <param name="value">새 자식 요소 노드의 값입니다.<see cref="F:System.String.Empty" /> 또는 null을 전달하면 빈 요소가 만들어집니다.</param>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on is not the root node or an element node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.BaseURI">
      <summary>파생 클래스에서 재정의되면 현재 노드에 대한 기본 URI를 가져옵니다.</summary>
      <returns>노드가 로드된 위치이거나, 값이 없으면 <see cref="F:System.String.Empty" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.CanEdit">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />에서 내부 XML 데이터를 편집할 수 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />에서 내부 XML 데이터를 편집할 수 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Clone">
      <summary>파생 클래스에서 재정의되면 이 <see cref="T:System.Xml.XPath.XPathNavigator" />와 같은 노드에 새 <see cref="T:System.Xml.XPath.XPathNavigator" />를 만듭니다.</summary>
      <returns>이 <see cref="T:System.Xml.XPath.XPathNavigator" />와 같은 노드에 있는 새 <see cref="T:System.Xml.XPath.XPathNavigator" />입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ComparePosition(System.Xml.XPath.XPathNavigator)">
      <summary>현재 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치를 지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치와 비교합니다.</summary>
      <returns>두 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체의 비교 위치를 나타내는 <see cref="T:System.Xml.XmlNodeOrder" /> 값입니다.</returns>
      <param name="nav">비교할 대상인 <see cref="T:System.Xml.XPath.XPathNavigator" />입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Compile(System.String)">
      <summary>XPath 식을 나타내는 문자열을 컴파일하고 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체를 반환합니다.</summary>
      <returns>XPath 식을 나타내는 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체입니다.</returns>
      <param name="xpath">XPath 식을 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentException">The <paramref name="xpath" /> parameter contains an XPath expression that is not valid.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.CreateAttribute(System.String,System.String,System.String,System.String)">
      <summary>지정된 값과 함께 지정된 네임스페이스 접두사, 로컬 이름 및 네임스페이스 URI를 사용하여 현재 요소 노드에 특성 노드를 만듭니다.</summary>
      <param name="prefix">새 특성 노드의 네임스페이스 접두사입니다(있는 경우).</param>
      <param name="localName">새 특성 노드의 로컬 이름으로, <see cref="F:System.String.Empty" /> 또는 null일 수 없습니다.</param>
      <param name="namespaceURI">새 특성 노드의 네임스페이스 URI입니다(있는 경우).</param>
      <param name="value">새 특성 노드의 값입니다.<see cref="F:System.String.Empty" /> 또는 null을 전달하면 빈 특성 노드가 만들어집니다.</param>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is not positioned on an element node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.CreateAttributes">
      <summary>현재 요소에 새 특성을 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체를 반환합니다.</summary>
      <returns>현재 요소에 새 특성을 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is not positioned on an element node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.CreateNavigator">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />의 복사본을 반환합니다.</summary>
      <returns>이 <see cref="T:System.Xml.XPath.XPathNavigator" />의 <see cref="T:System.Xml.XPath.XPathNavigator" /> 복사본입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.DeleteRange(System.Xml.XPath.XPathNavigator)">
      <summary>현재 노드부터 지정된 노드까지의 형제 노드 범위를 삭제합니다.</summary>
      <param name="lastSiblingToDelete">삭제할 범위의 마지막 형제 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> specified is null.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.InvalidOperationException">The last node to delete specified is not a valid sibling node of the current node.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.DeleteSelf">
      <summary>현재 노드와 해당 자식 노드를 삭제합니다.</summary>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on a node that cannot be deleted such as the root node or a namespace node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Evaluate(System.String)">
      <summary>지정된 XPath 식을 계산하고 형식화된 결과를 반환합니다.</summary>
      <returns>식의 결과(부울, 숫자, 문자열 또는 노드 집합)입니다.형식화된 결과는 각각 <see cref="T:System.Boolean" />, <see cref="T:System.Double" />, <see cref="T:System.String" /> 또는 <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 개체에 매핑됩니다.</returns>
      <param name="xpath">계산할 수 있는 XPath 식을 나타내는 문자열입니다.</param>
      <exception cref="T:System.ArgumentException">The return type of the XPath expression is a node set.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Evaluate(System.String,System.Xml.IXmlNamespaceResolver)">
      <summary>XPath 식에 있는 네임스페이스 접두사를 확인하기 위해 지정된 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체를 사용하여 XPath 식을 계산한 후 형식화된 결과를 반환합니다.</summary>
      <returns>식의 결과(부울, 숫자, 문자열 또는 노드 집합)입니다.형식화된 결과는 각각 <see cref="T:System.Boolean" />, <see cref="T:System.Double" />, <see cref="T:System.String" /> 또는 <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 개체에 매핑됩니다.</returns>
      <param name="xpath">계산할 수 있는 XPath 식을 나타내는 문자열입니다.</param>
      <param name="resolver">XPath 식에 있는 네임스페이스 접두사를 확인하는 데 사용되는 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The return type of the XPath expression is a node set.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Evaluate(System.Xml.XPath.XPathExpression)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathExpression" />을 계산하고 형식화된 결과를 반환합니다.</summary>
      <returns>식의 결과(부울, 숫자, 문자열 또는 노드 집합)입니다.형식화된 결과는 각각 <see cref="T:System.Boolean" />, <see cref="T:System.Double" />, <see cref="T:System.String" /> 또는 <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 개체에 매핑됩니다.</returns>
      <param name="expr">계산할 수 있는 <see cref="T:System.Xml.XPath.XPathExpression" />입니다.</param>
      <exception cref="T:System.ArgumentException">The return type of the XPath expression is a node set.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Evaluate(System.Xml.XPath.XPathExpression,System.Xml.XPath.XPathNodeIterator)">
      <summary>제공된 컨텍스트를 사용하여 <see cref="T:System.Xml.XPath.XPathExpression" />을 계산한 후 형식화된 결과를 반환합니다.</summary>
      <returns>식의 결과(부울, 숫자, 문자열 또는 노드 집합)입니다.형식화된 결과는 각각 <see cref="T:System.Boolean" />, <see cref="T:System.Double" />, <see cref="T:System.String" /> 또는 <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 개체에 매핑됩니다.</returns>
      <param name="expr">계산할 수 있는 <see cref="T:System.Xml.XPath.XPathExpression" />입니다.</param>
      <param name="context">계산이 수행될 선택된 노드 집합을 가리키는 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</param>
      <exception cref="T:System.ArgumentException">The return type of the XPath expression is a node set.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.GetAttribute(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI가 있는 특성의 값을 가져옵니다.</summary>
      <returns>지정된 특성의 값을 포함하는 <see cref="T:System.String" />입니다. 일치하는 특성이 없거나 <see cref="T:System.Xml.XPath.XPathNavigator" />가 요소 노드에 없으면 <see cref="F:System.String.Empty" />입니다.</returns>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="namespaceURI">특성의 네임스페이스 URI입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.GetNamespace(System.String)">
      <summary>지정된 로컬 이름에 해당하는 네임스페이스 노드의 값을 반환합니다.</summary>
      <returns>네임스페이스 노드의 값을 포함하는 <see cref="T:System.String" />입니다. 일치하는 네임스페이스 노드가 없거나 <see cref="T:System.Xml.XPath.XPathNavigator" />가 요소 노드에 없으면 <see cref="F:System.String.Empty" />입니다.</returns>
      <param name="name">네임스페이스 노드의 로컬 이름입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.GetNamespacesInScope(System.Xml.XmlNamespaceScope)">
      <summary>현재 노드의 범위 내 네임스페이스를 반환합니다.</summary>
      <returns>접두사를 키로 사용하는 네임스페이스 이름의 <see cref="T:System.Collections.Generic.IDictionary`2" /> 컬렉션입니다.</returns>
      <param name="scope">반환할 네임스페이스를 지정하는 <see cref="T:System.Xml.XmlNamespaceScope" /> 값입니다.</param>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.HasAttributes">
      <summary>현재 노드에 특성이 있는지를 나타내는 값을 얻습니다.</summary>
      <returns>현재 노드에 특성이 있으면 true를 반환하고, 현재 노드에 특성이 없거나 <see cref="T:System.Xml.XPath.XPathNavigator" />가 요소 노드에 없으면 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.HasChildren">
      <summary>현재 노드에 자식 노드가 있는지를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 노드에 자식 노드가 있으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.InnerXml">
      <summary>현재 노드의 자식 노드를 나타내는 태그를 가져오거나 설정합니다.</summary>
      <returns>현재 노드에 있는 자식 노드의 태그를 포함하는 <see cref="T:System.String" />입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="P:System.Xml.XPath.XPathNavigator.InnerXml" /> property cannot be set.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertAfter">
      <summary>현재 선택된 노드 뒤에 새 형제 노드를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체를 반환합니다.</summary>
      <returns>현재 선택된 노드 뒤에 새 형제 노드를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted after the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertAfter(System.String)">
      <summary>지정된 XML 문자열을 사용하여 현재 선택된 노드 뒤에 새 형제 노드를 만듭니다.</summary>
      <param name="newSibling">새 형제 노드에 대한 XML 데이터 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">The XML string parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted after the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML string parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertAfter(System.Xml.XmlReader)">
      <summary>지정된 <see cref="T:System.Xml.XmlReader" /> 개체의 XML 콘텐츠를 사용하여 현재 선택된 노드 뒤에 새 형제 노드를 만듭니다.</summary>
      <param name="newSibling">새 형제 노드에 대한 XML 데이터에 있는 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Xml.XmlReader" /> object is in an error state or closed.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted after the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML contents of the <see cref="T:System.Xml.XmlReader" /> object parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertAfter(System.Xml.XPath.XPathNavigator)">
      <summary>지정된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체의 노드를 사용하여 현재 선택된 노드 뒤에 새 형제 노드를 만듭니다.</summary>
      <param name="newSibling">새 형제 노드로 추가할 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted after the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertBefore">
      <summary>현재 선택된 노드 앞에 새 형제 노드를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체를 반환합니다.</summary>
      <returns>현재 선택된 노드 앞에 새 형제 노드를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted before the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertBefore(System.String)">
      <summary>지정된 XML 문자열을 사용하여 현재 선택된 노드 앞에 새 형제 노드를 만듭니다.</summary>
      <param name="newSibling">새 형제 노드에 대한 XML 데이터 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">The XML string parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted before the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML string parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertBefore(System.Xml.XmlReader)">
      <summary>지정된 <see cref="T:System.Xml.XmlReader" /> 개체의 XML 콘텐츠를 사용하여 현재 선택된 노드 앞에 새 형제 노드를 만듭니다.</summary>
      <param name="newSibling">새 형제 노드에 대한 XML 데이터에 있는 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Xml.XmlReader" /> object is in an error state or closed.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted before the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML contents of the <see cref="T:System.Xml.XmlReader" /> object parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertBefore(System.Xml.XPath.XPathNavigator)">
      <summary>지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />의 노드를 사용하여 현재 선택된 노드 앞에 새 형제 노드를 만듭니다.</summary>
      <param name="newSibling">새 형제 노드로 추가할 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted before the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertElementAfter(System.String,System.String,System.String,System.String)">
      <summary>지정된 값과 함께 네임스페이스 접두사, 로컬 이름 및 네임스페이스 URI를 사용하여 현재 노드 뒤에 새 형제 요소를 만듭니다.</summary>
      <param name="prefix">새 자식 요소의 네임스페이스 접두사입니다(있는 경우).</param>
      <param name="localName">새 자식 요소의 로컬 이름입니다(있는 경우).</param>
      <param name="namespaceURI">새 자식 요소 노드의 네임스페이스 URI입니다(있는 경우).<see cref="F:System.String.Empty" /> 및 null은 같습니다.</param>
      <param name="value">새 자식 요소의 값입니다.<see cref="F:System.String.Empty" /> 또는 null을 전달하면 빈 요소가 만들어집니다.</param>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted after the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.InsertElementBefore(System.String,System.String,System.String,System.String)">
      <summary>지정된 값과 함께 네임스페이스 접두사, 로컬 이름 및 네임스페이스 URI를 사용하여 현재 노드 앞에 새 형제 요소를 만듭니다.</summary>
      <param name="prefix">새 자식 요소의 네임스페이스 접두사입니다(있는 경우).</param>
      <param name="localName">새 자식 요소의 로컬 이름입니다(있는 경우).</param>
      <param name="namespaceURI">새 자식 요소 노드의 네임스페이스 URI입니다(있는 경우).<see cref="F:System.String.Empty" /> 및 null은 같습니다.</param>
      <param name="value">새 자식 요소의 값입니다.<see cref="F:System.String.Empty" /> 또는 null을 전달하면 빈 요소가 만들어집니다.</param>
      <exception cref="T:System.InvalidOperationException">The position of the <see cref="T:System.Xml.XPath.XPathNavigator" /> does not allow a new sibling node to be inserted before the current node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.IsDescendant(System.Xml.XPath.XPathNavigator)">
      <summary>지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />의 하위 항목인지를 확인합니다.</summary>
      <returns>지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />의 하위 항목이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="nav">이 <see cref="T:System.Xml.XPath.XPathNavigator" />와 비교할 <see cref="T:System.Xml.XPath.XPathNavigator" />입니다.</param>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.IsEmptyElement">
      <summary>파생 클래스에서 재정의되면 현재 노드가 끝 요소 태그가 없는 빈 요소인지를 나타내는 값을 가져옵니다.</summary>
      <returns>현재 노드가 빈 요소이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.IsNode">
      <summary>현재 노드가 XPath 노드를 나타내는지를 표시하는 값을 가져옵니다.</summary>
      <returns>항상 true를 반환합니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.IsSamePosition(System.Xml.XPath.XPathNavigator)">
      <summary>파생 클래스에서 재정의되면 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />가 지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />와 같은 위치에 있는지 확인합니다.</summary>
      <returns>두 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체의 위치가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">이 <see cref="T:System.Xml.XPath.XPathNavigator" />와 비교할 <see cref="T:System.Xml.XPath.XPathNavigator" />입니다.</param>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.LocalName">
      <summary>파생 클래스에서 재정의되면 현재 노드의 <see cref="P:System.Xml.XPath.XPathNavigator.Name" />을 네임스페이스 접두사 없이 가져옵니다.</summary>
      <returns>현재 노드의 로컬 이름을 포함하는 <see cref="T:System.String" />이거나, 현재 노드에 이름이 없으면(예: 텍스트 또는 주석 노드) <see cref="F:System.String.Empty" />입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.LookupNamespace(System.String)">
      <summary>지정된 접두사의 네임스페이스 URI를 가져옵니다.</summary>
      <returns>지정된 네임스페이스 접두사에 할당된 네임스페이스 URI를 포함하는 <see cref="T:System.String" />이거나, 지정된 접두사에 할당된 네임스페이스 URI가 없으면 null입니다.<see cref="T:System.String" />은 원자화됩니다.</returns>
      <param name="prefix">확인할 네임스페이스 URI의 접두사입니다.기본 네임스페이스를 일치시키려면 <see cref="F:System.String.Empty" />를 전달합니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.LookupPrefix(System.String)">
      <summary>지정된 네임스페이스 URI에 대해 선언된 접두사를 가져옵니다.</summary>
      <returns>지정된 네임스페이스 URI에 할당된 네임스페이스 접두사를 포함하는 <see cref="T:System.String" />이거나, 지정된 네임스페이스 URI에 할당된 접두사가 없으면 <see cref="F:System.String.Empty" />입니다.<see cref="T:System.String" />은 원자화됩니다.</returns>
      <param name="namespaceURI">접두사를 확인할 네임스페이스 URI입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Matches(System.String)">
      <summary>현재 노드가 지정된 XPath 식과 일치하는지를 확인합니다.</summary>
      <returns>현재 노드가 지정된 XPath 식과 일치하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="xpath">XPath 식입니다.</param>
      <exception cref="T:System.ArgumentException">The XPath expression cannot be evaluated.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Matches(System.Xml.XPath.XPathExpression)">
      <summary>현재 노드가 지정된 <see cref="T:System.Xml.XPath.XPathExpression" />과 일치하는지를 확인합니다.</summary>
      <returns>현재 노드가 <see cref="T:System.Xml.XPath.XPathExpression" />과 일치하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="expr">컴파일된 XPath 식을 포함하는 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The XPath expression cannot be evaluated.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveTo(System.Xml.XPath.XPathNavigator)">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />와 같은 위치로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 지정된 <see cref="T:System.Xml.XPath.XPathNavigator" />와 같은 위치로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="other">이동하려는 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" />입니다. </param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToAttribute(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 로컬 이름 및 네임스페이스 URI가 일치하는 특성으로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 특성으로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="localName">특성의 로컬 이름입니다.</param>
      <param name="namespaceURI">특성의 네임스페이스 URI입니다. 빈 네임스페이스인 경우에는 null입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToChild(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 로컬 이름 및 네임스페이스 URI를 사용하는 자식 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 자식 노드로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="localName">이동하려는 대상 자식 노드의 로컬 이름입니다.</param>
      <param name="namespaceURI">이동하려는 대상 자식 노드의 네임스페이스 URI입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToChild(System.Xml.XPath.XPathNodeType)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 <see cref="T:System.Xml.XPath.XPathNodeType" />의 자식 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 자식 노드로 성공적으로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="type">이동하려는 대상 자식 노드의 <see cref="T:System.Xml.XPath.XPathNodeType" />입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFirst">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드의 첫 번째 형제 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 노드의 첫 번째 형제 노드로 이동하면 true이고, 첫 번째 형제 노드가 없거나 <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 특성 노드에 있으면 false입니다.<see cref="T:System.Xml.XPath.XPathNavigator" />가 이미 첫 번째 형제에 배치되어 있으면, <see cref="T:System.Xml.XPath.XPathNavigator" />는 true를 반환하며 해당 위치를 이동하지 않습니다.첫 번째 형제가 없어 <see cref="M:System.Xml.XPath.XPathNavigator.MoveToFirst" />가 false를 반환하거나 <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 특성에 배치되어 있는 경우 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치는 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFirstAttribute">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드의 첫 번째 특성으로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 노드의 첫 번째 특성으로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFirstChild">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드의 첫 번째 자식 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 노드의 첫 번째 자식 노드로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFirstNamespace">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드의 첫 번째 네임스페이스 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 첫 번째 네임스페이스 노드로 성공적으로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFirstNamespace(System.Xml.XPath.XPathNamespaceScope)">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 <see cref="T:System.Xml.XPath.XPathNamespaceScope" />와 일치하는 첫 번째 네임스페이스 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 첫 번째 네임스페이스 노드로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="namespaceScope">네임스페이스 범위를 설명하는 <see cref="T:System.Xml.XPath.XPathNamespaceScope" /> 값입니다. </param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFollowing(System.String,System.String)">
      <summary>문서 순서에 따라 <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 로컬 이름 및 네임스페이스 URI를 사용하는 요소로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 이동하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFollowing(System.String,System.String,System.Xml.XPath.XPathNavigator)">
      <summary>지정된 범위 내에서 문서 순서에 따라 <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 로컬 이름 및 네임스페이스 URI를 사용하는 요소로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 이동하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="localName">요소의 로컬 이름입니다.</param>
      <param name="namespaceURI">요소의 네임스페이스 URI입니다.</param>
      <param name="end">다음 요소를 검색하는 동안 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />가 벗어나지 않을 요소 경계에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFollowing(System.Xml.XPath.XPathNodeType)">
      <summary>문서 순서에 따라 <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 <see cref="T:System.Xml.XPath.XPathNodeType" />의 다음 요소로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 성공적으로 이동하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="type">요소의 <see cref="T:System.Xml.XPath.XPathNodeType" />입니다.<see cref="T:System.Xml.XPath.XPathNodeType" />은 <see cref="F:System.Xml.XPath.XPathNodeType.Attribute" /> 또는 <see cref="F:System.Xml.XPath.XPathNodeType.Namespace" />일 수 없습니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToFollowing(System.Xml.XPath.XPathNodeType,System.Xml.XPath.XPathNavigator)">
      <summary>지정된 범위 내에서 문서 순서에 따라 <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 <see cref="T:System.Xml.XPath.XPathNodeType" />의 다음 요소로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 이동하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="type">요소의 <see cref="T:System.Xml.XPath.XPathNodeType" />입니다.<see cref="T:System.Xml.XPath.XPathNodeType" />은 <see cref="F:System.Xml.XPath.XPathNodeType.Attribute" /> 또는 <see cref="F:System.Xml.XPath.XPathNodeType.Namespace" />일 수 없습니다.</param>
      <param name="end">다음 요소를 검색하는 동안 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />가 벗어나지 않을 요소 경계에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToId(System.String)">
      <summary>파생 클래스에서 재정의되면 지정된 <see cref="T:System.String" />과 ID 형식의 값이 일치하는 특성을 포함하는 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 이동하면 true이고, 그렇지 않으면 false입니다.false이면 탐색기의 위치는 변경되지 않습니다.</returns>
      <param name="id">이동하려는 대상 노드의 ID 값을 나타내는 <see cref="T:System.String" />입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToNamespace(System.String)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 네임스페이스 접두사를 사용하는 네임스페이스 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 지정된 네임스페이스로 이동하면 true이고, 일치하는 네임스페이스 노드가 없거나 <see cref="T:System.Xml.XPath.XPathNavigator" />가 요소 노드에 없으면 false입니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="name">네임스페이스 노드의 네임스페이스 접두사입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToNext">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드의 다음 형제 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 다음 형제 노드로 성공적으로 이동하면 true이고, 더 이상 형제 노드가 없거나 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />가 특성 노드에 있으면 false입니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToNext(System.String,System.String)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 로컬 이름 및 네임스페이스 URI를 사용하는 다음 형제 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 다음 형제 노드로 이동하면 true를 반환하고, 더 이상 형제 노드가 없거나 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />가 특성 노드에 있으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="localName">이동하려는 다음 형제 노드의 로컬 이름입니다.</param>
      <param name="namespaceURI">이동하려는 다음 형제 노드의 네임스페이스 URI입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToNext(System.Xml.XPath.XPathNodeType)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드에서 지정된 <see cref="T:System.Xml.XPath.XPathNodeType" />과 일치하는 다음 형제 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 다음 형제 노드로 이동하면 true이고, 더 이상 형제 노드가 없거나 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />가 특성 노드에 있으면 false입니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="type">이동하려는 대상 형제 노드의 <see cref="T:System.Xml.XPath.XPathNodeType" />입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToNextAttribute">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 다음 특성으로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 다음 특성으로 이동하면 true를 반환하고, 더 이상 특성이 없으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToNextNamespace">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 다음 네임스페이스 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 다음 네임스페이스 노드로 성공적으로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToNextNamespace(System.Xml.XPath.XPathNamespaceScope)">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 지정된 <see cref="T:System.Xml.XPath.XPathNamespaceScope" />와 일치하는 다음 네임스페이스 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 다음 네임스페이스 노드로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
      <param name="namespaceScope">네임스페이스 범위를 설명하는 <see cref="T:System.Xml.XPath.XPathNamespaceScope" /> 값입니다. </param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToParent">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드의 부모 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 현재 노드의 부모 노드로 이동하면 true를 반환하고, 그렇지 않으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToPrevious">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드의 이전 형제 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" />가 이전 형제 노드로 이동하면 true를 반환하고, 이전 형제 노드가 없거나 현재 <see cref="T:System.Xml.XPath.XPathNavigator" />가 특성 노드에 있으면 false를 반환합니다.값이 false이면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 위치가 변경되지 않습니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.MoveToRoot">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" />를 현재 노드가 속해 있는 루트 노드로 이동합니다.</summary>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.Name">
      <summary>파생 클래스에서 재정의되면 현재 노드의 정규화된 이름을 가져옵니다.</summary>
      <returns>현재 노드의 정규화된 <see cref="P:System.Xml.XPath.XPathNavigator.Name" />을 포함하는 <see cref="T:System.String" />이거나, 현재 노드에 이름이 없으면(예: 텍스트 또는 주석 노드) <see cref="F:System.String.Empty" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.NamespaceURI">
      <summary>파생 클래스에서 재정의되면 현재 노드의 네임스페이스 URI를 가져옵니다.</summary>
      <returns>현재 노드의 네임스페이스 URI를 포함하는 <see cref="T:System.String" />이거나, 현재 노드에 네임스페이스 URI가 없으면 <see cref="F:System.String.Empty" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.NameTable">
      <summary>파생 클래스에서 재정의되면 <see cref="T:System.Xml.XPath.XPathNavigator" />의 <see cref="T:System.Xml.XmlNameTable" />을 가져옵니다.</summary>
      <returns>XML 문서 내에서 원자화된 버전의 <see cref="T:System.String" />을 가져올 수 있게 해주는 <see cref="T:System.Xml.XmlNameTable" /> 개체입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.NavigatorComparer">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체의 같음 비교에 사용되는 <see cref="T:System.Collections.IEqualityComparer" />를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체의 같음 비교에 사용되는 <see cref="T:System.Collections.IEqualityComparer" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.NodeType">
      <summary>파생 클래스에서 재정의되면 현재 노드의 <see cref="T:System.Xml.XPath.XPathNodeType" />을 가져옵니다.</summary>
      <returns>현재 노드를 나타내는 <see cref="T:System.Xml.XPath.XPathNodeType" /> 값 중 하나입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.OuterXml">
      <summary>현재 노드와 해당 자식 노드의 여는 태그 및 닫는 태그를 나타내는 태그를 가져오거나 설정합니다.</summary>
      <returns>현재 노드와 해당 자식 노드의 여는 태그 및 닫는 태그를 나타내는 태그가 포함된 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.Prefix">
      <summary>파생 클래스에서 재정의되면 현재 노드와 관련된 네임스페이스 접두사를 가져옵니다.</summary>
      <returns>현재 노드에 연결된 네임스페이스 접두사를 포함하는 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.PrependChild">
      <summary>현재 노드의 자식 노드 목록 맨 앞에 새 자식 노드를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체를 반환합니다.</summary>
      <returns>현재 노드의 자식 노드 목록 맨 앞에 새 자식 노드를 만드는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체를 반환합니다.</returns>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on does not allow a new child node to be prepended.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.PrependChild(System.String)">
      <summary>지정된 XML 문자열을 사용하여 현재 노드의 자식 노드 목록 맨 앞에 새 자식 노드를 만듭니다.</summary>
      <param name="newChild">새 자식 노드에 대한 XML 데이터 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">The XML string parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on does not allow a new child node to be prepended.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML string parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.PrependChild(System.Xml.XmlReader)">
      <summary>지정된 <see cref="T:System.Xml.XmlReader" /> 개체의 XML 콘텐츠를 사용하여 현재 노드의 자식 노드 목록 맨 앞에 새 자식 노드를 만듭니다.</summary>
      <param name="newChild">새 자식 노드에 대한 XML 데이터에 있는 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Xml.XmlReader" /> object is in an error state or closed.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on does not allow a new child node to be prepended.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML contents of the <see cref="T:System.Xml.XmlReader" /> object parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.PrependChild(System.Xml.XPath.XPathNavigator)">
      <summary>지정된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체에 있는 노드를 사용하여 현재 노드의 자식 노드 목록 맨 앞에 새 자식 노드를 만듭니다.</summary>
      <param name="newChild">새 자식 노드로 추가할 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on does not allow a new child node to be prepended.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.PrependChildElement(System.String,System.String,System.String,System.String)">
      <summary>지정된 값과 함께 네임스페이스 접두사, 로컬 이름 및 네임스페이스 URI를 사용하여 현재 노드의 자식 노드 목록 맨 앞에 새 자식 요소 노드를 만듭니다.</summary>
      <param name="prefix">새 자식 요소의 네임스페이스 접두사입니다(있는 경우).</param>
      <param name="localName">새 자식 요소의 로컬 이름입니다(있는 경우).</param>
      <param name="namespaceURI">새 자식 요소 노드의 네임스페이스 URI입니다(있는 경우).<see cref="F:System.String.Empty" /> 및 null은 같습니다.</param>
      <param name="value">새 자식 요소의 값입니다.<see cref="F:System.String.Empty" /> 또는 null을 전달하면 빈 요소가 만들어집니다.</param>
      <exception cref="T:System.InvalidOperationException">The current node the <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on does not allow a new child node to be prepended.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ReadSubtree">
      <summary>현재 노드와 해당 자식 노드를 포함하는 <see cref="T:System.Xml.XmlReader" /> 개체를 반환합니다.</summary>
      <returns>현재 노드와 해당 자식 노드를 포함하는 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is not positioned on an element node or the root node.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ReplaceRange(System.Xml.XPath.XPathNavigator)">
      <summary>현재 노드부터 지정된 노드까지의 형제 노드 범위를 바꿉니다.</summary>
      <returns>바꾸기 범위를 지정하는 데 사용되는 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</returns>
      <param name="lastSiblingToReplace">바꿀 범위의 마지막 형제 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> specified is null.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.InvalidOperationException">The last node to replace specified is not a valid sibling node of the current node.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ReplaceSelf(System.String)">
      <summary>현재 노드를 지정된 문자열의 콘텐츠로 바꿉니다.</summary>
      <param name="newNode">새 노드에 대한 XML 데이터 문자열입니다.</param>
      <exception cref="T:System.ArgumentNullException">The XML string parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is not positioned on an element, text, processing instruction, or comment node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML string parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ReplaceSelf(System.Xml.XmlReader)">
      <summary>현재 노드를 지정된 <see cref="T:System.Xml.XmlReader" /> 개체의 콘텐츠로 바꿉니다.</summary>
      <param name="newNode">새 노드에 대한 XML 데이터에 있는 <see cref="T:System.Xml.XmlReader" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Xml.XmlReader" /> object is in an error state or closed.</exception>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XmlReader" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is not positioned on an element, text, processing instruction, or comment node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML contents of the <see cref="T:System.Xml.XmlReader" /> object parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ReplaceSelf(System.Xml.XPath.XPathNavigator)">
      <summary>현재 노드를 지정된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체의 콘텐츠로 바꿉니다.</summary>
      <param name="newNode">새 노드에 있는 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> object parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is not positioned on an element, text, processing instruction, or comment node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
      <exception cref="T:System.Xml.XmlException">The XML contents of the <see cref="T:System.Xml.XPath.XPathNavigator" /> object parameter is not well-formed.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Select(System.String)">
      <summary>지정된 XPath 식을 사용하여 노드 집합을 선택합니다.</summary>
      <returns>선택된 노드 집합을 가리키는 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</returns>
      <param name="xpath">XPath 식을 나타내는 <see cref="T:System.String" />입니다.</param>
      <exception cref="T:System.ArgumentException">The XPath expression contains an error or its return type is not a node set.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Select(System.String,System.Xml.IXmlNamespaceResolver)">
      <summary>네임스페이스 접두사를 확인하기 위해 지정된 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체와 함께 XPath 식을 사용하여 노드 집합을 선택합니다.</summary>
      <returns>선택된 노드 집합을 가리키는 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</returns>
      <param name="xpath">XPath 식을 나타내는 <see cref="T:System.String" />입니다.</param>
      <param name="resolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체는 네임스페이스 접두사를 확인하는 데 사용됩니다.</param>
      <exception cref="T:System.ArgumentException">The XPath expression contains an error or its return type is not a node set.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.Select(System.Xml.XPath.XPathExpression)">
      <summary>지정된 <see cref="T:System.Xml.XPath.XPathExpression" />을 사용하여 노드 집합을 선택합니다.</summary>
      <returns>선택된 노드 집합을 가리키는 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</returns>
      <param name="expr">컴파일된 XPath 쿼리를 포함하는 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">The XPath expression contains an error or its return type is not a node set.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath expression is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectAncestors(System.String,System.String,System.Boolean)">
      <summary>지정된 로컬 이름과 네임스페이스 URI를 사용하는 현재 노드의 상위 노드를 모두 선택합니다.</summary>
      <returns>선택된 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.반환되는 노드는 문서 순서와 반대로 배치됩니다.</returns>
      <param name="name">상위 노드의 로컬 이름입니다.</param>
      <param name="namespaceURI">상위 노드의 네임스페이스 URI입니다.</param>
      <param name="matchSelf">선택할 때 컨텍스트 노드를 포함하려면 true이고, 그렇지 않으면 false입니다. </param>
      <exception cref="T:System.ArgumentNullException">null cannot be passed as a parameter.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectAncestors(System.Xml.XPath.XPathNodeType,System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNodeType" />이 일치하는 현재 노드의 상위 노드를 모두 선택합니다.</summary>
      <returns>선택된 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.반환되는 노드는 문서 순서와 반대로 배치됩니다.</returns>
      <param name="type">상위 노드의 <see cref="T:System.Xml.XPath.XPathNodeType" />입니다.</param>
      <param name="matchSelf">선택할 때 컨텍스트 노드를 포함하려면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectChildren(System.String,System.String)">
      <summary>지정된 로컬 이름과 네임스페이스 URI를 사용하는 현재 노드의 자식 노드를 모두 선택합니다.</summary>
      <returns>선택된 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</returns>
      <param name="name">자식 노드의 로컬 이름입니다. </param>
      <param name="namespaceURI">자식 노드의 네임스페이스 URI입니다. </param>
      <exception cref="T:System.ArgumentNullException">null cannot be passed as a parameter.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectChildren(System.Xml.XPath.XPathNodeType)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNodeType" />이 일치하는 현재 노드의 자식 노드를 모두 선택합니다.</summary>
      <returns>선택된 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</returns>
      <param name="type">자식 노드의 <see cref="T:System.Xml.XPath.XPathNodeType" />입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectDescendants(System.String,System.String,System.Boolean)">
      <summary>지정된 로컬 이름과 네임스페이스 URI를 사용하는 현재 노드의 하위 노드를 모두 선택합니다.</summary>
      <returns>선택된 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</returns>
      <param name="name">하위 노드의 로컬 이름입니다. </param>
      <param name="namespaceURI">하위 노드의 네임스페이스 URI입니다. </param>
      <param name="matchSelf">선택할 때 컨텍스트 노드를 포함하려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">null cannot be passed as a parameter.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectDescendants(System.Xml.XPath.XPathNodeType,System.Boolean)">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNodeType" />이 일치하는 현재 노드의 하위 노드를 모두 선택합니다.</summary>
      <returns>선택된 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNodeIterator" />입니다.</returns>
      <param name="type">하위 노드의 <see cref="T:System.Xml.XPath.XPathNodeType" />입니다.</param>
      <param name="matchSelf">선택할 때 컨텍스트 노드를 포함하려면 true이고, 그렇지 않으면 false입니다.</param>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectSingleNode(System.String)">
      <summary>지정된 XPath 쿼리를 사용하여 <see cref="T:System.Xml.XPath.XPathNavigator" />에서 단일 노드를 선택합니다.</summary>
      <returns>지정된 XPath 쿼리와 일치하는 첫 번째 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체이거나, 쿼리 결과가 없으면 null입니다.</returns>
      <param name="xpath">XPath 식을 나타내는 <see cref="T:System.String" />입니다.</param>
      <exception cref="T:System.ArgumentException">An error was encountered in the XPath query or the return type of the XPath expression is not a node.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath query is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectSingleNode(System.String,System.Xml.IXmlNamespaceResolver)">
      <summary>네임스페이스 접두사를 확인하기 위해 지정된 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체와 함께 지정된 XPath 쿼리를 사용하여 <see cref="T:System.Xml.XPath.XPathNavigator" />에서 단일 노드를 선택합니다.</summary>
      <returns>지정된 XPath 쿼리와 일치하는 첫 번째 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체이거나, 쿼리 결과가 없으면 null입니다.</returns>
      <param name="xpath">XPath 식을 나타내는 <see cref="T:System.String" />입니다.</param>
      <param name="resolver">XPath 쿼리에 있는 네임스페이스 접두사를 확인하는 데 사용되는 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">An error was encountered in the XPath query or the return type of the XPath expression is not a node.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath query is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SelectSingleNode(System.Xml.XPath.XPathExpression)">
      <summary>지정된 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체를 사용하여 <see cref="T:System.Xml.XPath.XPathNavigator" />에서 단일 노드를 선택합니다.</summary>
      <returns>지정된 XPath 쿼리와 일치하는 첫 번째 노드가 포함된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체이거나, 쿼리 결과가 없으면 null입니다.</returns>
      <param name="expression">컴파일된 XPath 쿼리를 포함하는 <see cref="T:System.Xml.XPath.XPathExpression" /> 개체입니다.</param>
      <exception cref="T:System.ArgumentException">An error was encountered in the XPath query or the return type of the XPath expression is not a node.</exception>
      <exception cref="T:System.Xml.XPath.XPathException">The XPath query is not valid.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SetTypedValue(System.Object)">
      <summary>현재 노드의 형식화된 값을 설정합니다.</summary>
      <param name="typedValue">노드의 형식화된 새 값입니다.</param>
      <exception cref="T:System.ArgumentException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support the type of the object specified.</exception>
      <exception cref="T:System.ArgumentNullException">The value specified cannot be null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is not positioned on an element or attribute node.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.SetValue(System.String)">
      <summary>현재 노드의 값을 설정합니다.</summary>
      <param name="value">노드의 새 값</param>
      <exception cref="T:System.ArgumentNullException">The value parameter is null.</exception>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> is positioned on the root node, a namespace node, or the specified value is invalid.</exception>
      <exception cref="T:System.NotSupportedException">The <see cref="T:System.Xml.XPath.XPathNavigator" /> does not support editing.</exception>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ToString">
      <summary>현재 노드의 텍스트 값을 가져옵니다.</summary>
      <returns>현재 노드의 텍스트 값을 포함하는 string입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.TypedValue">
      <summary>가장 적합한 .NET Framework 형식 중 현재 노드에 해당하는 boxed 개체를 가져옵니다.</summary>
      <returns>가장 적합한 .NET Framework 형식 중 현재 노드에 해당하는 boxed 개체입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.UnderlyingObject">
      <summary>내부 개체에 액세스할 수 있도록 저장소에 대한 "가상" XML 뷰를 제공하기 위해 구현된 <see cref="T:System.Xml.XPath.XPathNavigator" />에 사용됩니다.</summary>
      <returns>기본값은 null입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.ValueAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>네임스페이스 접두사를 확인하기 위해 지정된 <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체를 사용하여 현재 노드의 값을 지정된 <see cref="T:System.Type" />으로 가져옵니다.</summary>
      <returns>현재 노드의 값에 해당하는 요청된 <see cref="T:System.Type" />입니다.</returns>
      <param name="returnType">현재 노드의 값을 반환할 <see cref="T:System.Type" />입니다.</param>
      <param name="nsResolver">
        <see cref="T:System.Xml.IXmlNamespaceResolver" /> 개체는 네임스페이스 접두사를 확인하는 데 사용됩니다.</param>
      <exception cref="T:System.FormatException">The current node's value is not in the correct format for the target type.</exception>
      <exception cref="T:System.InvalidCastException">The attempted cast is not valid.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.ValueAsBoolean">
      <summary>현재 노드의 값을 <see cref="T:System.Boolean" />으로 가져옵니다.</summary>
      <returns>현재 노드의 값에 해당하는 <see cref="T:System.Boolean" /> 값입니다.</returns>
      <exception cref="T:System.FormatException">The current node's string value cannot be converted to a <see cref="T:System.Boolean" />.</exception>
      <exception cref="T:System.InvalidCastException">The attempted cast to <see cref="T:System.Boolean" /> is not valid.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.ValueAsDateTime">
      <summary>현재 노드의 값을 <see cref="T:System.DateTime" />으로 가져옵니다.</summary>
      <returns>현재 노드의 값에 해당하는 <see cref="T:System.DateTime" /> 값입니다.</returns>
      <exception cref="T:System.FormatException">The current node's string value cannot be converted to a <see cref="T:System.DateTime" />.</exception>
      <exception cref="T:System.InvalidCastException">The attempted cast to <see cref="T:System.DateTime" /> is not valid.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.ValueAsDouble">
      <summary>현재 노드의 값을 <see cref="T:System.Double" />으로 가져옵니다.</summary>
      <returns>현재 노드의 값에 해당하는 <see cref="T:System.Double" /> 값입니다.</returns>
      <exception cref="T:System.FormatException">The current node's string value cannot be converted to a <see cref="T:System.Double" />.</exception>
      <exception cref="T:System.InvalidCastException">The attempted cast to <see cref="T:System.Double" /> is not valid.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.ValueAsInt">
      <summary>현재 노드의 값을 <see cref="T:System.Int32" />로 가져옵니다.</summary>
      <returns>현재 노드의 값에 해당하는 <see cref="T:System.Int32" /> 값입니다.</returns>
      <exception cref="T:System.FormatException">The current node's string value cannot be converted to a <see cref="T:System.Int32" />.</exception>
      <exception cref="T:System.InvalidCastException">The attempted cast to <see cref="T:System.Int32" /> is not valid.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.ValueAsLong">
      <summary>현재 노드의 값을 <see cref="T:System.Int64" />로 가져옵니다.</summary>
      <returns>현재 노드의 값에 해당하는 <see cref="T:System.Int64" /> 값입니다.</returns>
      <exception cref="T:System.FormatException">The current node's string value cannot be converted to a <see cref="T:System.Int64" />.</exception>
      <exception cref="T:System.InvalidCastException">The attempted cast to <see cref="T:System.Int64" /> is not valid.</exception>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.ValueType">
      <summary>현재 노드의 .NET Framework <see cref="T:System.Type" />을 가져옵니다.</summary>
      <returns>현재 노드의 .NET Framework <see cref="T:System.Type" />입니다.기본값은 <see cref="T:System.String" />입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNavigator.WriteSubtree(System.Xml.XmlWriter)">
      <summary>현재 노드와 해당 자식 노드를 지정된 <see cref="T:System.Xml.XmlWriter" /> 개체로 스트리밍합니다.</summary>
      <param name="writer">스트리밍할 대상 <see cref="T:System.Xml.XmlWriter" /> 개체입니다.</param>
    </member>
    <member name="P:System.Xml.XPath.XPathNavigator.XmlLang">
      <summary>현재 노드에 대한 xml:lang 범위를 가져옵니다.</summary>
      <returns>xml:lang 범위의 값을 포함하는 <see cref="T:System.String" />이거나, 현재 노드에 반환할 xml:lang 범위 값이 없으면 <see cref="F:System.String.Empty" />입니다.</returns>
    </member>
    <member name="T:System.Xml.XPath.XPathNodeIterator">
      <summary>선택된 노드 집합에 대한 반복기를 제공합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathNodeIterator.#ctor">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Xml.XPath.XPathNodeIterator.Clone">
      <summary>파생 클래스에서 재정의된 경우 이 <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 개체의 복제를 반환합니다.</summary>
      <returns>이 <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 개체의 새로운 <see cref="T:System.Xml.XPath.XPathNodeIterator" /> 개체 복제입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNodeIterator.Count">
      <summary>선택된 노드 집합에 포함된 마지막 노드의 인덱스를 가져옵니다.</summary>
      <returns>선택된 노드 집합에 포함된 마지막 노드의 인덱스이며, 선택된 노드가 없는 경우에는 0입니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNodeIterator.Current">
      <summary>파생 클래스에서 재정의된 경우 현재 컨텍스트 노드에 위치한 이 <see cref="T:System.Xml.XPath.XPathNodeIterator" />에 대한 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체를 가져옵니다.</summary>
      <returns>노드 집합을 선택한 컨텍스트 노드로 위치가 지정된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체입니다.<see cref="M:System.Xml.XPath.XPathNodeIterator.MoveNext" /> 메서드를 호출하여 <see cref="T:System.Xml.XPath.XPathNodeIterator" />를 선택된 집합의 첫 번째 노드로 이동해야 합니다.</returns>
    </member>
    <member name="P:System.Xml.XPath.XPathNodeIterator.CurrentPosition">
      <summary>파생 클래스에서 재정의할 때 선택된 노드 집합에서 현재 위치의 인덱스를 가져옵니다.</summary>
      <returns>현재 위치의 인덱스입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNodeIterator.GetEnumerator">
      <summary>선택된 노드 집합을 반복하는 <see cref="T:System.Collections.IEnumerator" /> 개체를 반환합니다.</summary>
      <returns>선택된 노드 집합을 반복하는 <see cref="T:System.Collections.IEnumerator" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Xml.XPath.XPathNodeIterator.MoveNext">
      <summary>파생 클래스에서 재정의하는 경우 <see cref="P:System.Xml.XPath.XPathNodeIterator.Current" /> 속성에서 반환된 <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체를 선택된 노드 집합의 다음 노드로 이동합니다.</summary>
      <returns>
        <see cref="T:System.Xml.XPath.XPathNavigator" /> 개체가 다음 노드로 이동되었으면 true이고 선택된 노드가 더 이상 없으면 false입니다.</returns>
    </member>
    <member name="T:System.Xml.XPath.XPathNodeType">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNavigator" /> 클래스에서 반환할 수 있는 XPath 노드 형식을 정의합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.All">
      <summary>
        <see cref="T:System.Xml.XPath.XPathNodeType" /> 노드 형식입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.Attribute">
      <summary>id='123'과 같은 특성입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.Comment">
      <summary>&lt;!-- my comment --&gt;와 같은 주석입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.Element">
      <summary>&lt;element&gt;와 같은 요소입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.Namespace">
      <summary>xmlns="namespace"와 같은 네임스페이스입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.ProcessingInstruction">
      <summary>&lt;?pi test?&gt;와 같은 처리 명령입니다.<see cref="T:System.Xml.XPath.XPathNavigator" /> 클래스에게 보이지 않는 XML 선언은 여기에 포함되지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.Root">
      <summary>XML 문서 또는 노드 트리의 루트 노드입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.SignificantWhitespace">
      <summary>공백 문자를 포함하며 xml:space가 preserve로 설정된 노드입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.Text">
      <summary>노드의 텍스트 내용입니다.DOM(문서 개체 모델) Text 및 CDATA 노드 형식에 해당합니다.문자가 하나 이상 있어야 합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathNodeType.Whitespace">
      <summary>공백 문자만 포함하고 중요 공백은 포함하지 않는 노드입니다.공백 문자는 #x20, #x9, #xD 또는 #xA 입니다.</summary>
    </member>
    <member name="T:System.Xml.XPath.XPathResultType">
      <summary>XPath 식의 반환 형식을 지정합니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathResultType.Any">
      <summary>XPath 노드 형식입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathResultType.Boolean">
      <summary>
        <see cref="T:System.Boolean" />true 또는 false 값입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathResultType.Error">
      <summary>식이 올바른 XPath 형식으로 계산되지 않습니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathResultType.Navigator">
      <summary>트리 단편입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathResultType.NodeSet">
      <summary>노드 컬렉션입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathResultType.Number">
      <summary>숫자 값입니다.</summary>
    </member>
    <member name="F:System.Xml.XPath.XPathResultType.String">
      <summary>
        <see cref="T:System.String" /> 값입니다.</summary>
    </member>
  </members>
</doc>