package com.phonecheck.client.customization;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.dao.service.LocalCustomizationsDBService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.CustomTestPlansResponse;
import com.phonecheck.model.cloudapi.MicThresholdResponse;
import com.phonecheck.model.cloudapi.SoftwareVersionsResponse;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.device.DeviceType;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.phonecheck.model.constants.FileConstants.*;

@Service
@AllArgsConstructor
public class ClientCustomizationService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClientCustomizationService.class);
    private static final String COLOR = "Color";

    private final CloudApiRestClient cloudApiRestClient;
    private final PhonecheckApiRestClient phonecheckApiRestClient;
    private final SupportFilePath supportFilePath;
    private final FileUtil fileUtil;
    private final ObjectMapper mapper;
    private final LocalCustomizationsDBService customizationsDbService;
    public static final String CLIENT_CUSTOMIZATION_TOKEN =
            "bG9naW5BcHBTdG9yZUFwcEF1dG9tYXRpY2FsbHlXaGF0ZXZlckRldmljZUlzQ29ubmVjdGVkT3JOb3Q=";
    public static final String REQUIRED_ESN = "esn";
    public static final String REQUIRED_COLOR = "color";
    public static final String REQUIRED_GRADE = "grade";
    public static final String REQUIRED_CARRIER = "carrier";
    public static final String REQUIRED_UNLOCK = "unlock";
    public static final String REQUIRED_LPN = "lpn";
    public static final String REQUIRED_CUSTOM1 = "custom1";
    public static final String REQUIRED_COSMETICS = "cosmetics";

    public static final String HEADSET_VOLUME_LEVEL_ENABLED = "headsetVolumeLevelEnabled";
    public static final String HEADSET_VOLUME_LEVEL = "HeadsetVolumeLevel";
    public static final String AUDIO_QUALITY_LEVEL_ENABLED = "AudioQualityVolumeLevelEnabled";
    public static final String AUDIO_QUALITY_LEVEL = "AudioQualityVolumeLevel";
    public static final String MANUAL_RINGTONE_QUALITY_UPLIFT = "ManualRingtoneQuality_Uplift";
    public static final String MANUAL_RINGTONE_QUALITY_DEFAULT = "ManualRingtoneQuality_Default";
    public static final String EARPIECE_QUALITY_VOLUME_LEVEL = "EarpieceQualityVolumeLevel";
    public static final String NUMBER_TO_DIAL = "NumberToDial";
    public static final String WIFI = "WIFI";
    public static final String WIFI_ONLY = "WIFI Only";

    // These keys are placed in client customization File for battery drain test
    public static final String AUTO_START_BATTERY_TEST = "AutoStartBatteryTest";
    public static final String AUTO_START_BATTERY_ON_CONNECTION = "AutoStartBatteryOnConnection";
    public static final String ALLOW_DURATION_CHANGE = "AllowDurationChange";
    public static final String BATTERY_DRAIN_DURATION = "BatteryDrainDuration";
    private static final String LCD_COLOR_BLUE = "LCD_ColorBlue";
    private static final String LCD_COLOR_BLACK = "LCD_ColorBlack";
    private static final String LCD_COLOR_WHITE = "LCD_ColorWhite";
    private static final String LCD_COLOR_GRAY = "LCD_ColorGray";
    private static final String LCD_COLOR_GREEN = "LCD_ColorGreen";
    private static final String LCD_COLOR_RED = "LCD_ColorRed";

    public void setupClientCustomization(final int licenseId, final String masterId,
                                         final CloudCustomizationResponse customization) throws Exception {
        LOGGER.info("Setting up client customization file for licenseId: {} and masterId: {}", licenseId, masterId);

        // maintain separate jsons for android and ios
        ObjectNode clientCustomizationJsonIos = mapper.createObjectNode();
        ObjectNode clientCustomizationJsonAndroid = mapper.createObjectNode();

        // populate some test app customizations from customization 3.0 in to the jsons
        populateClientCustomizationFromAssignedCustomization(
                customization, clientCustomizationJsonIos, clientCustomizationJsonAndroid);

        if (customization.getCustomTestList() != null && !customization.getCustomTestList().isEmpty()) {
            try {
                List<CustomTestPlansResponse> customTestPlansResponse = customization.getCustomTestList()
                        .stream().map(customTest -> new CustomTestPlansResponse(
                                String.valueOf(customTest.getId()),
                                StringUtils.isNotEmpty(customTest.getTestName())
                                        ? customTest.getTestName() : StringUtils.EMPTY,
                                CloudCustomizationResponse.Platform.getReformKey(customTest.getPlatform()),
                                StringUtils.isNotEmpty(customTest.getModelNumber())
                                        ? customTest.getModelNumber() : StringUtils.EMPTY,
                                StringUtils.isNotEmpty(customTest.getInstructions())
                                        ? customTest.getInstructions() : StringUtils.EMPTY,
                                StringUtils.isNotEmpty(customTest.getModifiedDate())
                                        ? customTest.getModifiedDate() : StringUtils.EMPTY,
                                "1"
                        )).collect(Collectors.toList());
                ArrayNode arrayNode = mapper.convertValue(customTestPlansResponse, ArrayNode.class);
                clientCustomizationJsonIos.putArray("CustomizeTests").addAll(arrayNode);
                clientCustomizationJsonAndroid.putArray("CustomizeTests").addAll(arrayNode);
            } catch (final Exception e) {
                LOGGER.error("Error while parsing custom plans from cloud customization", e);
            }
        } else {
            try {
                //add custom test plan array to client customization json
                CustomTestPlansResponse[] customTestPlansResponse = cloudApiRestClient.getCustomTestPlans(masterId);
                if (customTestPlansResponse != null) {
                    ArrayNode arrayNode = mapper.convertValue(customTestPlansResponse, ArrayNode.class);
                    clientCustomizationJsonIos.putArray("CustomizeTests").addAll(arrayNode);
                    clientCustomizationJsonAndroid.putArray("CustomizeTests").addAll(arrayNode);
                }
            } catch (final RestClientException e) {
                LOGGER.error("Error while getting custom plans from cloud", e);
            }
        }

        try {
            //add software version array to client customization json
            SoftwareVersionsResponse[] softwareVersionsResponse = cloudApiRestClient.getSoftwareVersions(masterId);
            if (softwareVersionsResponse != null) {
                ArrayNode arrayNode = mapper.convertValue(softwareVersionsResponse, ArrayNode.class);
                clientCustomizationJsonIos.putArray("SWVersionList").addAll(arrayNode);
                clientCustomizationJsonAndroid.putArray("SWVersionList").addAll(arrayNode);
            }
        } catch (final RestClientException e) {
            LOGGER.error("Error while getting software versions from cloud", e);
        }

        //add ios mic threshold to client customization json
        try {
            MicThresholdResponse micThreshold = cloudApiRestClient.getMicThreshold(licenseId);
            if (micThreshold != null && micThreshold.getDetails() != null
                    && micThreshold.getDetails().getPlans() != null
                    && micThreshold.getDetails().getPlans().getIos() != null) {
                clientCustomizationJsonIos.set("MicThresHoldIOS",
                        mapper.convertValue(micThreshold.getDetails().getPlans().getIos(), JsonNode.class));
                clientCustomizationJsonAndroid.set("MicThresHoldIOS",
                        mapper.convertValue(micThreshold.getDetails().getPlans().getIos(), JsonNode.class));
            }
        } catch (final RestClientException e) {
            LOGGER.error("Error while getting mic thresholds from cloud", e);
        }

        //add token to client customization json
        clientCustomizationJsonIos.set("Token",
                mapper.convertValue(CLIENT_CUSTOMIZATION_TOKEN, JsonNode.class));
        clientCustomizationJsonAndroid.set("Token",
                mapper.convertValue(CLIENT_CUSTOMIZATION_TOKEN, JsonNode.class));


        final File clientCustomizationFileIos =
                fileUtil.createFile(supportFilePath.getPaths().getRootFolderPath() +
                        File.separator + IOS_FILES_FOLDER + File.separator + CUSTOMIZATION_FILE_NAME);

        final File clientCustomizationFileAndroid =
                fileUtil.createFile(supportFilePath.getPaths().getRootFolderPath() +
                        File.separator + ANDROID_FILES_FOLDER + File.separator + CUSTOMIZATION_FILE_NAME);

        fileUtil.writeStringToFileIfDifferent(clientCustomizationFileIos,
                mapper.writeValueAsString(clientCustomizationJsonIos));
        fileUtil.writeStringToFileIfDifferent(clientCustomizationFileAndroid,
                mapper.writeValueAsString(clientCustomizationJsonAndroid));
    }

    /**
     * Based on assigned customization, update the client customization json values
     *
     * @param customization
     * @param clientCustomizationJsonIos
     * @param clientCustomizationJsonAndroid
     */
    private void populateClientCustomizationFromAssignedCustomization(final CloudCustomizationResponse customization,
                                                                      final ObjectNode clientCustomizationJsonIos,
                                                                      final ObjectNode clientCustomizationJsonAndroid) {
        if (customization != null && customization.getTestPlan() != null
                && customization.getTestPlan().getBoth() != null) {

            Map<String, Boolean> colorMap = initializeLcdColorMap();

            // set map values based on if a color key is present in the test plan
            for (CloudCustomizationResponse.DeviceTest test : customization.getTestPlan().getBoth()) {
                if (colorMap.containsKey(test.getKey())) {
                    colorMap.put(test.getKey(), true);
                }
            }

            // add LCD color keys to the customization
            if (colorMap.containsValue(true)) {
                for (Map.Entry<String, Boolean> entry : colorMap.entrySet()) {
                    clientCustomizationJsonIos.put(entry.getKey(), entry.getValue());
                    clientCustomizationJsonAndroid.put(entry.getKey(), entry.getValue());
                }
            }

            for (CloudCustomizationResponse.DeviceTest deviceTest : customization.getTestPlan().getBoth()) {
                if (deviceTest.getKey().equalsIgnoreCase("Headset Port")) {
                    clientCustomizationJsonIos.put(HEADSET_VOLUME_LEVEL_ENABLED, 1);
                    clientCustomizationJsonIos
                            .put(HEADSET_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    clientCustomizationJsonAndroid.put(HEADSET_VOLUME_LEVEL_ENABLED, 1);
                    clientCustomizationJsonAndroid
                            .put(HEADSET_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                }
                if (deviceTest.getKey().equalsIgnoreCase("Bottom Mic Quality")) {
                    clientCustomizationJsonIos.put(AUDIO_QUALITY_LEVEL_ENABLED, 1);
                    clientCustomizationJsonIos
                            .put(AUDIO_QUALITY_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    clientCustomizationJsonAndroid.put(AUDIO_QUALITY_LEVEL_ENABLED, 1);
                    clientCustomizationJsonAndroid
                            .put(AUDIO_QUALITY_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                }
                if (deviceTest.getKey().equalsIgnoreCase("Manual Ringtone Quality")) {
                    if ("ManualRingtoneQuality_Uplift".equalsIgnoreCase(deviceTest.getInputValue())) {
                        clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_UPLIFT, true);
                        clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_DEFAULT, false);
                        clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_UPLIFT, true);
                        clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_DEFAULT, false);
                    } else if ("ManualRingtoneQuality_Default".equalsIgnoreCase(deviceTest.getInputValue())) {
                        clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_UPLIFT, false);
                        clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_DEFAULT, true);
                        clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_UPLIFT, false);
                        clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_DEFAULT, true);
                    }
                }
                if (deviceTest.getKey().equalsIgnoreCase("Call Test")) {
                    clientCustomizationJsonIos.put(NUMBER_TO_DIAL, deviceTest.getInputValue());
                    clientCustomizationJsonAndroid.put(NUMBER_TO_DIAL, deviceTest.getInputValue());
                }
                if (deviceTest.getKey().equalsIgnoreCase("Earpiece Quality")
                        && StringUtils.isNotEmpty(deviceTest.getInputValue())) {
                    clientCustomizationJsonIos.
                            put(EARPIECE_QUALITY_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    clientCustomizationJsonAndroid.
                            put(EARPIECE_QUALITY_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                }
            }
            if (customization.getBatterySettings() != null &&
                    customization.getBatterySettings().isDrain()) {
                putBatteryDrainSettingsJson(customization, clientCustomizationJsonIos);
                putBatteryDrainSettingsJson(customization, clientCustomizationJsonAndroid);
            }

        } else if (customization != null && customization.getTestPlan() != null) {
            if (customization.getTestPlan().getApple() != null) {

                Map<String, Boolean> colorMap = initializeLcdColorMap();

                // set map values based on if a color key is present in the test plan
                for (CloudCustomizationResponse.DeviceTest test : customization.getTestPlan().getApple()) {
                    if (colorMap.containsKey(test.getKey())) {
                        colorMap.put(test.getKey(), true);
                    }
                }

                // add LCD color keys to the customization
                if (colorMap.containsValue(true)) {
                    for (Map.Entry<String, Boolean> entry : colorMap.entrySet()) {
                        clientCustomizationJsonIos.put(entry.getKey(), entry.getValue());
                    }
                }


                for (CloudCustomizationResponse.DeviceTest deviceTest : customization.getTestPlan().getApple()) {
                    if (deviceTest.getKey().equalsIgnoreCase("Headset Port")) {
                        clientCustomizationJsonIos.put(HEADSET_VOLUME_LEVEL_ENABLED, 1);
                        clientCustomizationJsonIos
                                .put(HEADSET_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Bottom Mic Quality")) {
                        clientCustomizationJsonIos.put(AUDIO_QUALITY_LEVEL_ENABLED, 1);
                        clientCustomizationJsonIos
                                .put(AUDIO_QUALITY_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Manual Ringtone Quality")) {
                        if ("ManualRingtoneQuality_Uplift".equalsIgnoreCase(deviceTest.getInputValue())) {
                            clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_UPLIFT, true);
                            clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_DEFAULT, false);
                        } else if ("ManualRingtoneQuality_Default".equalsIgnoreCase(deviceTest.getInputValue())) {
                            clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_UPLIFT, false);
                            clientCustomizationJsonIos.put(MANUAL_RINGTONE_QUALITY_DEFAULT, true);
                        }
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Call Test")) {
                        clientCustomizationJsonIos.put(NUMBER_TO_DIAL, deviceTest.getInputValue());
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Earpiece Quality")
                            && StringUtils.isNotEmpty(deviceTest.getInputValue())) {
                        clientCustomizationJsonIos.
                                put(EARPIECE_QUALITY_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    }
                }
            }
            if (customization.getTestPlan().getAndroid() != null) {

                Map<String, Boolean> colorMap = initializeLcdColorMap();

                // set map values based on if a color key is present in the test plan
                for (CloudCustomizationResponse.DeviceTest test : customization.getTestPlan().getAndroid()) {
                    if (colorMap.containsKey(test.getKey())) {
                        colorMap.put(test.getKey(), true);
                    }
                }

                // add LCD color keys to the customization
                if (colorMap.containsValue(true)) {
                    for (Map.Entry<String, Boolean> entry : colorMap.entrySet()) {
                        clientCustomizationJsonAndroid.put(entry.getKey(), entry.getValue());
                    }
                }

                for (CloudCustomizationResponse.DeviceTest deviceTest : customization.getTestPlan().getAndroid()) {
                    if (deviceTest.getKey().equalsIgnoreCase("Headset Port")) {
                        clientCustomizationJsonAndroid.put(HEADSET_VOLUME_LEVEL_ENABLED, 1);
                        clientCustomizationJsonAndroid
                                .put(HEADSET_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Bottom Mic Quality")) {
                        clientCustomizationJsonAndroid.put(AUDIO_QUALITY_LEVEL_ENABLED, 1);
                        clientCustomizationJsonAndroid
                                .put(AUDIO_QUALITY_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Manual Ringtone Quality")) {
                        if ("ManualRingtoneQuality_Uplift".equalsIgnoreCase(deviceTest.getInputValue())) {
                            clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_UPLIFT, true);
                            clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_DEFAULT, false);
                        } else if ("ManualRingtoneQuality_Default".equalsIgnoreCase(deviceTest.getInputValue())) {
                            clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_UPLIFT, false);
                            clientCustomizationJsonAndroid.put(MANUAL_RINGTONE_QUALITY_DEFAULT, true);
                        }
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Call Test")) {
                        clientCustomizationJsonAndroid.put(NUMBER_TO_DIAL, deviceTest.getInputValue());
                    }
                    if (deviceTest.getKey().equalsIgnoreCase("Earpiece Quality")
                            && StringUtils.isNotEmpty(deviceTest.getInputValue())) {
                        clientCustomizationJsonAndroid.
                                put(EARPIECE_QUALITY_VOLUME_LEVEL, Integer.parseInt(deviceTest.getInputValue()));
                    }
                }
            }
            if (customization.getBatterySettings().isDrain()) {
                putBatteryDrainSettingsJson(customization, clientCustomizationJsonIos);
                putBatteryDrainSettingsJson(customization, clientCustomizationJsonAndroid);
            }
        }
    }

    /**
     * Initialize the LCD colors map to help populate the client customization file
     *
     * @return map of lcd colors
     */
    private static Map<String, Boolean> initializeLcdColorMap() {
        Map<String, Boolean> colorMap = new HashMap<>();
        colorMap.put(LCD_COLOR_BLUE, false);
        colorMap.put(LCD_COLOR_WHITE, false);
        colorMap.put(LCD_COLOR_BLACK, false);
        colorMap.put(LCD_COLOR_GRAY, false);
        colorMap.put(LCD_COLOR_GREEN, false);
        colorMap.put(LCD_COLOR_RED, false);
        return colorMap;
    }

    /**
     * Get customizations for a user
     *
     * @param userId user id
     * @return customizations
     */
    public LocalCustomizations getCustomizations(final String userId) {
        return customizationsDbService.getCustomizations(userId);
    }

    /**
     * Save customizations for a user
     *
     * @param customizations local customization
     */
    public void saveCustomizations(final LocalCustomizations customizations) {
        customizationsDbService.createOrUpdateCustomization(customizations);
    }

    /**
     * checks if any fo the enabled required fields are empty and
     * returns the list of fields that are empty and enabled
     *
     * @param device         target device
     * @param customizations client customizations
     * @return list of fields that are empty and enabled
     */
    public List<String> getUnpopulatedRequiredFields(final Device device,
                                                     final CloudCustomizationResponse customizations) {
        final List<String> requiredFieldsList = new ArrayList<>();
        final boolean isWifiOnlyDevice = WIFI.equalsIgnoreCase(device.getCarrier())
                || WIFI_ONLY.equalsIgnoreCase(device.getCarrier())
                || (StringUtils.isNotBlank(device.getImei()) && device.getImei().equals(device.getSerial()));

        if (customizations.getRequiredFields().isEnabled()) {
            if (customizations.getRequiredFields().isColor() &&
                    (StringUtils.isBlank(device.getColor()) || StringUtils.contains(device.getColor(), COLOR))) {
                LOGGER.info("Color is a required field and is set to : {}", device.getColor());
                requiredFieldsList.add(REQUIRED_COLOR);
            }
            if (customizations.getRequiredFields().isESN() && StringUtils.isBlank(device.getEsnStatus())
                    && !isWifiOnlyDevice) {
                LOGGER.info("ESN is a required field is set to : {}", device.getEsnStatus());
                requiredFieldsList.add(REQUIRED_ESN);
            }
            if (customizations.getRequiredFields().isGrade() && (StringUtils.isBlank(device.getGrade())
                    || REQUIRED_GRADE.equalsIgnoreCase(device.getGrade()))) {
                LOGGER.info("Grade is a required field and is set to : {}", device.getGrade());
                requiredFieldsList.add(REQUIRED_GRADE);
            }
            if (customizations.getRequiredFields().isCarrier() && !isWifiOnlyDevice &&
                    (StringUtils.isBlank(device.getCarrier()) ||
                            REQUIRED_CARRIER.equalsIgnoreCase(device.getCarrier()))) {
                LOGGER.info("Carrier is a required field and is set to : {}", device.getCarrier());
                requiredFieldsList.add(REQUIRED_CARRIER);
            }

            // device unlock status doesn't get set, so set it here before checking for required fields
            String simLockStatus = Boolean.TRUE.equals(device.getSimLock()) ? "Locked" :
                    (Boolean.FALSE.equals(device.getSimLock()) ? "Unlocked" : StringUtils.EMPTY);
            String unlockStatus = StringUtils.isNotBlank(device.getUnlockStatus()) ?
                    device.getUnlockStatus() : ("Locked".equalsIgnoreCase(simLockStatus) ?
                    "LK" : "Unlocked".equalsIgnoreCase(simLockStatus) ? "UNLK" : StringUtils.EMPTY);
            device.setUnlockStatus(unlockStatus);

            if (customizations.getRequiredFields().isUnlockStatus() && !isWifiOnlyDevice) {
                if ((Boolean.TRUE.equals(device.getSimLock()) && StringUtils.equals(device.getUnlockStatus(), "LK") &&
                            DeviceType.ANDROID.equals(device.getDeviceType()))
                    ||  (device.getSimLock() == null &&

                    (StringUtils.isBlank(device.getUnlockStatus()) || (StringUtils.equalsIgnoreCase(device.
                            getUnlockStatus(), "LK") && DeviceType.ANDROID.equals(device.getDeviceType()))))) {

                    LOGGER.info("UnlockStatus is a required field and is set to: NULL");
                    requiredFieldsList.add(REQUIRED_UNLOCK);
                }
            }
            if (customizations.getRequiredFields().isLPN() && StringUtils.isBlank(device.getLpn())) {
                LOGGER.info("LPN is a required field and is set to: {}", device.getLpn());
                requiredFieldsList.add(REQUIRED_LPN);
            }
            if (customizations.getRequiredFields().isCustom() && StringUtils.isBlank(device.getCustom1())) {
                LOGGER.info("Custom1 is a required field and is set to: {}", device.getCustom1());
                requiredFieldsList.add(REQUIRED_CUSTOM1);
            }
            if (customizations.getRequiredFields().isCosmetics() &&
                    (device.getDeviceTestResult() == null ||
                            device.getDeviceTestResult().getCosmeticResults() == null ||
                            device.getDeviceTestResult().getCosmeticResults().getFailed() == null)) {
                LOGGER.info("Cosmetics is a required field and is set to: NULL");
                requiredFieldsList.add(REQUIRED_COSMETICS);
            }
        }
        return requiredFieldsList;
    }

    /**
     * get the list of all cloud customizations assigned to the master
     *
     * @param masterToken master token
     * @return CloudCustomizationsResponse[]
     */
    public List<CloudCustomizationResponse> getCloudCustomizations(final String masterToken) {
        try {
            return phonecheckApiRestClient.getCloudCustomizations(masterToken);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get cloud customizations", e);
            return null;
        }
    }

    /**
     * Get assigned cloud customization for the user
     *
     * @param licenseId   license id
     * @param masterToken master token
     * @return CloudCustomizationResponse
     */
    public CloudCustomizationResponse getAssignedCloudCustomization(final String licenseId,
                                                                    final String masterToken) {
        try {
            return phonecheckApiRestClient
                    .getAssignedCloudCustomization(licenseId, masterToken);
        } catch (final RestClientException e) {
            LOGGER.error("Error while calling get cloud customizations", e);
            // Return empty customizations
            return CloudCustomizationResponse
                    .builder()
                    .requiredFields(CloudCustomizationResponse.RequiredFields.builder().build())
                    .restrictiveActions(CloudCustomizationResponse.RestrictiveActions.builder().build())
                    .wifiSettings(CloudCustomizationResponse.WifiSettings.builder().build())
                    .batterySettings(CloudCustomizationResponse.BatterySettings.builder().build())
                    .printerSettings(CloudCustomizationResponse.PrinterSettings.builder().build())
                    .imeiCheck(CloudCustomizationResponse.ImeiCheck.builder().build())
                    .cosmeticSettings(CloudCustomizationResponse.CosmeticSettings.builder().build())
                    .eraseSettings(CloudCustomizationResponse.EraseSettings.builder().build())
                    .workflow(CloudCustomizationResponse.WorkflowSettings.builder().build())
                    .advancedSettings(CloudCustomizationResponse.AdvancedSettings.builder().build())
                    .build();
        }
    }

    /**
     * Update client customizations file with relevant keys for battery drain test
     *
     * @param customization           Cloud Customizations
     * @param clientCustomizationJson Client Customizations JSON
     **/
    private void putBatteryDrainSettingsJson(final CloudCustomizationResponse customization,
                                             final ObjectNode clientCustomizationJson) {
        if (customization.getBatterySettings().isDrainTestAfterTestResults()) {
            clientCustomizationJson.put(AUTO_START_BATTERY_TEST, true);
        } else {
            clientCustomizationJson.put(AUTO_START_BATTERY_ON_CONNECTION, true);
        }
        if (customization.getBatterySettings().isAllowDrainDurationChange()) {
            clientCustomizationJson.put(ALLOW_DURATION_CHANGE, true);
        } else {
            clientCustomizationJson.put(ALLOW_DURATION_CHANGE, false);
        }
        if (customization.getBatterySettings().getDrainDuration() > 0) {
            clientCustomizationJson.put(BATTERY_DRAIN_DURATION, customization.getBatterySettings().getDrainDuration());
        } else {
            clientCustomizationJson.put(BATTERY_DRAIN_DURATION, 5);
        }
    }

    public void updateFirmwarePath(final String userId, final String firmwarePath) {
        LocalCustomizations customizations = customizationsDbService.getCustomizations(userId);
        customizations.setFirmwarePath(firmwarePath);
        LOGGER.info("Updating customizations for the path : {}", firmwarePath);
        customizationsDbService.createOrUpdateCustomization(customizations);
    }
}
