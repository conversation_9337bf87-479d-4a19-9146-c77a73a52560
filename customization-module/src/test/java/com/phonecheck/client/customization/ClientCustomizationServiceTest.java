package com.phonecheck.client.customization;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phonecheck.api.client.CloudApiRestClient;
import com.phonecheck.api.client.PhonecheckApiRestClient;
import com.phonecheck.dao.service.LocalCustomizationsDBService;
import com.phonecheck.model.cloudapi.CloudCustomizationResponse;
import com.phonecheck.model.cloudapi.CustomTestPlansResponse;
import com.phonecheck.model.cloudapi.MicThresholdResponse;
import com.phonecheck.model.cloudapi.SoftwareVersionsResponse;
import com.phonecheck.model.customization.LocalCustomizations;
import com.phonecheck.model.device.Device;
import com.phonecheck.model.util.FileUtil;
import com.phonecheck.model.util.SupportFilePath;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class ClientCustomizationServiceTest {

    @Mock
    private CloudApiRestClient cloudApiRestClient;
    @Mock
    private SupportFilePath supportFilePath;
    @Mock
    private FileUtil fileUtil;
    @Mock
    private PhonecheckApiRestClient phonecheckApiRestClient;
    private final ObjectMapper mapper = new ObjectMapper();
    @Mock
    private LocalCustomizationsDBService localCustomizationsDbService;
    @Mock
    private CloudCustomizationResponse customizationResponse;
    @Mock
    private ClientCustomizationService clientCustomizationService;
    private Device device;

    @BeforeEach
    public void setup() {
        clientCustomizationService = new ClientCustomizationService(cloudApiRestClient, phonecheckApiRestClient,
                supportFilePath, fileUtil, mapper, localCustomizationsDbService);

        device = new Device() {
        };
    }

    @Test
    public void testSaveCustomizations() {
        LocalCustomizations customizations = LocalCustomizations.builder()
                .userId("user").build();

        clientCustomizationService.saveCustomizations(customizations);

        verify(localCustomizationsDbService).createOrUpdateCustomization(eq(customizations));
    }

    private CustomTestPlansResponse[] getCustomTestPlansResponseArray() {
        CustomTestPlansResponse customTestPlansResponse = CustomTestPlansResponse.builder()
                .ctpId("262").ctpTitle("Testing").ctpPlatform("IOS").ctpModelNo("").ctpDescription("Testingggggggggg")
                .ctpStatus("1").build();
        return new CustomTestPlansResponse[]{customTestPlansResponse};
    }

    private SoftwareVersionsResponse[] getSoftwareVersionsResponseArray() {
        SoftwareVersionsResponse softwareVersionsResponse1 = SoftwareVersionsResponse.builder()
                .swvId("382").swvModelNo("MX9R2B/A").swvVersionList("17").swvMasterId("1361")
                .swvStatus("1").swvAddedOn("2023-01-03 08:27:27").build();
        SoftwareVersionsResponse softwareVersionsResponse2 = SoftwareVersionsResponse.builder()
                .swvId("383").swvModelNo("MKR22LL/A").swvVersionList("14.7.1").swvMasterId("1361")
                .swvStatus("1").swvAddedOn("2021-09-10 11:04:56").build();
        return new SoftwareVersionsResponse[]{softwareVersionsResponse1, softwareVersionsResponse2};
    }

    private MicThresholdResponse getMicThresholdResponse() {
        MicThresholdResponse.PlanDetail ios1 = MicThresholdResponse.PlanDetail.builder()
                .testName("iPhone Default").deviceType("ios")
                .deviceModel("iPhone 11 Pro,iPod Touch 7th Gen,iPhone SE (2nd Gen)")
                .thresholdValueBM("0.29").thresholdValueFM("0.29").thresholdValueVM("0.29").build();
        MicThresholdResponse.PlanDetail ios2 = MicThresholdResponse.PlanDetail.builder()
                .testName("Custom iPad Default").deviceType("ios")
                .deviceModel("iPad Mini,iPad Mini 2,iPad Mini 3,iPad Mini 4,iPad 5,iPad Air,)")
                .thresholdValueBM("0.12").thresholdValueFM("0.12").thresholdValueVM("0.12").build();
        MicThresholdResponse.Plans plans = MicThresholdResponse.Plans.builder()
                .ios(List.of(ios1, ios2)).android(new ArrayList<>()).build();
        MicThresholdResponse.Details details = MicThresholdResponse.Details.builder()
                .inherited("yes").plans(plans).build();
        return MicThresholdResponse.builder().status("success").details(details).build();
    }

    @Test
    public void testRequiredFieldIsEnableAndDeviceValueIsNull() throws JsonProcessingException {
        String expectedJsonResponse = """
                {
                    "id": "31d03c29-d3c7-4760-91e3-1ab3aa5058d0",
                    "name": "Test Apple android",
                    "labels": "",
                    "masterId": "27473e7c-89de-4ad9-87f6-db706631de12",
                    "requiredFields": {
                        "ESN": true,
                        "LPN": true,
                        "color": true,
                        "grade": true,
                        "custom": true,
                        "carrier": true,
                        "unlockStatus": true,
                        "enabled": true
                    },
                    "restrictiveActions": {
                        "erase": false,
                        "print": false
                    }
                }
                """;

        customizationResponse = mapper.readValue(expectedJsonResponse,
                CloudCustomizationResponse.class);
        List<String> requiredFields = clientCustomizationService.getUnpopulatedRequiredFields(device,
                customizationResponse);

        assertTrue(requiredFields.contains("color"));
        assertTrue(requiredFields.contains("carrier"));
        assertTrue(requiredFields.contains("esn"));
        assertTrue(requiredFields.contains("lpn"));
        assertTrue(requiredFields.contains("custom1"));
        assertTrue(requiredFields.contains("grade"));
        assertTrue(requiredFields.contains("unlock"));
        assertEquals(7, requiredFields.size());
    }

    @Test
    public void testNoRequiredFieldAreEnabled() throws JsonProcessingException {
        String expectedJsonResponse = """
                {
                    "id": "31d03c29-d3c7-4760-91e3-1ab3aa5058d0",
                    "name": "Test Apple android",
                    "labels": "",
                    "masterId": "27473e7c-89de-4ad9-87f6-db706631de12",
                    "requiredFields": {
                        "ESN": false,
                        "LPN": false,
                        "color": false,
                        "grade": false,
                        "custom": false,
                        "carrier": false,
                        "unlockStatus": false,
                        "enabled": true
                    },
                    "restrictiveActions": {
                        "erase": false,
                        "print": false
                    }
                }
                """;
        customizationResponse = mapper.readValue(expectedJsonResponse,
                CloudCustomizationResponse.class);
        List<String> requiredFields = clientCustomizationService.getUnpopulatedRequiredFields(device,
                customizationResponse);

        assertTrue(requiredFields.isEmpty());
    }

    @Test
    public void testRequiredFieldIsEnableAndDeviceValueIsNotNull() throws JsonProcessingException {
        String expectedJsonResponse = """
                {
                    "id": "31d03c29-d3c7-4760-91e3-1ab3aa5058d0",
                    "name": "Test Apple android",
                    "labels": "",
                    "masterId": "27473e7c-89de-4ad9-87f6-db706631de12",
                    "requiredFields": {
                        "ESN": true,
                        "LPN": true,
                        "color": true,
                        "grade": true,
                        "custom": true,
                        "carrier": true,
                        "unlockStatus": true,
                        "enable": true
                    },
                    "restrictiveActions": {
                        "erase": false,
                        "print": false
                    }
                }
                """;
        device.setEsnStatus("good");
        device.setLpn("lpn");
        device.setColor("black");
        device.setGrade("A");
        device.setCustom1("custom");
        device.setCarrier("carrier");
        device.setUnlockStatus("unlock");
        customizationResponse = mapper.readValue(expectedJsonResponse,
                CloudCustomizationResponse.class);
        List<String> requiredFields = clientCustomizationService.getUnpopulatedRequiredFields(device,
                customizationResponse);

        assertTrue(requiredFields.isEmpty());
    }

    @Test
    public void testBatteryDrainSettingsForAutoStartBatteryTestAndAllowDurationChange() throws
            NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        CloudCustomizationResponse customization = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .drainTestAfterTestResults(true)
                        .allowDrainDurationChange(true)
                        .drainDuration(10)
                        .build())
                .build();

        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode clientCustomizationJson =
                objectMapper.convertValue(customization.getBatterySettings(), ObjectNode.class);
        // Calling the method to be tested
        Method privateMethod = ClientCustomizationService.class
                .getDeclaredMethod("putBatteryDrainSettingsJson", CloudCustomizationResponse.class, ObjectNode.class);
        privateMethod.setAccessible(true);
        privateMethod.invoke(clientCustomizationService, customization, clientCustomizationJson);

        // Verifying the expected behavior
        assertTrue(clientCustomizationJson.get(ClientCustomizationService.AUTO_START_BATTERY_TEST).asBoolean());
        assertTrue(clientCustomizationJson.get(ClientCustomizationService.ALLOW_DURATION_CHANGE).asBoolean());
        assertEquals(10, clientCustomizationJson.get(ClientCustomizationService.BATTERY_DRAIN_DURATION).asInt());
    }

    @Test
    public void testBatteryDrainSettingsForAutoStartBatteryOnConnectionAndAllowDurationChange() throws
            NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        CloudCustomizationResponse customization = CloudCustomizationResponse.builder()
                .batterySettings(CloudCustomizationResponse.BatterySettings.builder()
                        .drainTestAfterTestResults(false)
                        .allowDrainDurationChange(false)
                        .drainDuration(-1)
                        .build())
                .build();

        ObjectMapper objectMapper = new ObjectMapper();
        ObjectNode clientCustomizationJson =
                objectMapper.convertValue(customization.getBatterySettings(), ObjectNode.class);
        // Calling the method to be tested
        Method privateMethod = ClientCustomizationService.class
                .getDeclaredMethod("putBatteryDrainSettingsJson", CloudCustomizationResponse.class, ObjectNode.class);
        privateMethod.setAccessible(true);
        privateMethod.invoke(clientCustomizationService, customization, clientCustomizationJson);

        // Verifying the expected behavior
        assertTrue(clientCustomizationJson.get(
                ClientCustomizationService.AUTO_START_BATTERY_ON_CONNECTION).asBoolean());
        assertFalse(clientCustomizationJson.get(ClientCustomizationService.ALLOW_DURATION_CHANGE).asBoolean());
        assertEquals(5, clientCustomizationJson.get(ClientCustomizationService.BATTERY_DRAIN_DURATION).asInt());
    }
}
