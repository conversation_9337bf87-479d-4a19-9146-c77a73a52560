package com.phonecheck.license;

import com.phonecheck.dao.model.LicenseCharge;
import com.phonecheck.dao.service.LicenseDBService;
import com.phonecheck.model.constants.LicenseChargeStatus;
import com.phonecheck.model.ios.LicenseType;
import lombok.Setter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * This class contains all the schedulers to periodically charge device licenses
 * and also fetch remaining license counts from DB
 */
@Service
public class LicenseSchedulerService {

    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseSchedulerService.class);
    private static final Logger LICENSE_LOGGER = LoggerFactory.getLogger("LicenseLogger");
    private static final int INITIAL_DELAY_BEFORE_CHARGING_PENDING_LICENSE_REQUESTS = 60 * 1000; // 3 Minutes
    private static final int LOAD_PENDING_LICENSE_REQUESTS_FROM_DB_INTERVAL = 3 * 60 * 1000; // 3 Minutes
    private static final int FETCH_REMAINING_LICENSE_COUNT_INTERVAL = 10 * 60 * 1000; // 10 Minutes
    private static final int INITIAL_DELAY_BEFORE_FETCHING_REMAINING_LICENSE_COUNT = 10 * 60 * 1000; // 10 Minutes

    private final LicenseService licenseService;
    private final LicenseDBService licenseDBService;

    public LicenseSchedulerService(final LicenseService licenseService,
                                   final LicenseDBService licenseDBService) {
        this.licenseService = licenseService;
        this.licenseDBService = licenseDBService;
    }

    @Setter
    private boolean enableLicenseServiceSchedulers;

    /**
     * Scheduler to load all pending licenses from local DB to charged
     */
    @Scheduled(fixedRate = LOAD_PENDING_LICENSE_REQUESTS_FROM_DB_INTERVAL,
            initialDelay = INITIAL_DELAY_BEFORE_CHARGING_PENDING_LICENSE_REQUESTS)
    public void getAndChargeLicensesFromDBScheduler() {
        if (enableLicenseServiceSchedulers) {
            LOGGER.info("Ran scheduler to fetch pending license requests from DB");
            LICENSE_LOGGER.info("Ran scheduler to fetch pending license requests from DB");
            List<LicenseCharge> pendingLicenseCharges = licenseDBService.getPendingLicenseCharges();
            if (pendingLicenseCharges != null && pendingLicenseCharges.size() > 0) {
                LOGGER.info("Found {} pending license requests in DB. Going to charge those one by one",
                        pendingLicenseCharges.size());
                LICENSE_LOGGER.info("Found {} pending license requests in DB. Going to charge those one by one",
                        pendingLicenseCharges.size());

                for (LicenseCharge pendingLicenseCharge : pendingLicenseCharges) {
                    final boolean shouldContinueWithLoop = handlePendingLicenseCharge(pendingLicenseCharge);
                    if (!shouldContinueWithLoop) {
                        break;
                    }
                }

            } else {
                LOGGER.info("No license requests are found in the DB that are still pending to be charged");
                LICENSE_LOGGER.info("No license requests are found in the DB that are still pending to be charged");
            }
        } else {
            LOGGER.info("Skip running scheduler to fetch pending license requests from DB");
            LICENSE_LOGGER.info("Skip running scheduler to fetch pending license requests from DB");
        }
    }

    /**
     * Scheduler to fetch all remaining license count from cloud and set in inMemory object
     */
    @Scheduled(fixedRate = FETCH_REMAINING_LICENSE_COUNT_INTERVAL,
            initialDelay = INITIAL_DELAY_BEFORE_FETCHING_REMAINING_LICENSE_COUNT)
    public void getRemainingLicenseCountFromCloudScheduler() {
        if (enableLicenseServiceSchedulers) {
            LOGGER.info("Ran scheduler to fetch remaining license count from cloud");
            LICENSE_LOGGER.info("Ran scheduler to fetch remaining license count from cloud");
            final boolean isLicenseFetchSuccessful = licenseService.fetchAndSetRemainingLicensesFromCloud();
            if (isLicenseFetchSuccessful) {
                LOGGER.info("Scheduler successfully fetched license count from cloud");
                LICENSE_LOGGER.info("Scheduler successfully fetched license count from cloud");
            } else {
                LOGGER.error("Scheduler failed to fetch license count from cloud, will try after few minutes");
                LICENSE_LOGGER.error("Scheduler failed to fetch license count from cloud, will try after few minutes");
            }
        } else {
            LOGGER.info("Skip running scheduler to fetch remaining license count from cloud");
            LICENSE_LOGGER.info("Skip running scheduler to fetch remaining license count from cloud");
        }
    }

    /**
     * Handled pending license found in DB that is yet to be charged
     *
     * @param pendingLicenseCharge Pending license
     * @return boolean true: if we should continue charging remaining licenses
     */
    private boolean handlePendingLicenseCharge(final LicenseCharge pendingLicenseCharge) {
        MDC.clear();
        MDC.put("id", pendingLicenseCharge.getDeviceId());
        LicenseChargeStatus licenseChargeStatus = licenseService.checkAndChargeLicense(pendingLicenseCharge);
        switch (licenseChargeStatus) {
            case SUCCESS -> {
                pendingLicenseCharge.setChargeStatus(LicenseChargeStatus.SUCCESS.name());
                licenseDBService.updateLicenseCharge(pendingLicenseCharge);
            }
            case LICENSE_EXPIRED -> {
                pendingLicenseCharge.setChargeStatus(LicenseChargeStatus.LICENSE_EXPIRED.name());
                licenseDBService.updateLicenseCharge(pendingLicenseCharge);

                // If we have received expired licenses while charging a license,
                // then we should fetch licenses again from cloud and reset them in inMemory object
                final boolean isLicenseFetchSuccessful = licenseService.fetchAndSetRemainingLicensesFromCloud();
                if (isLicenseFetchSuccessful) {
                    LOGGER.info("Scheduler successfully fetched licenses from cloud");
                    LICENSE_LOGGER.info("Scheduler successfully fetched licenses from cloud");
                } else {
                    LOGGER.error("Scheduler failed to fetch licenses from cloud, will try after few minutes");
                    LICENSE_LOGGER.error("Scheduler failed to fetch licenses from cloud, will try after few minutes");
                }

                // Break from the loop as there's no point for charging remaining licenses if device licenses have
                // expired. We'll retry to charge licenses in the next scheduled time
                if (LicenseType.DEVICE == LicenseType.licenseTypeFromString(pendingLicenseCharge.getLicenseType())) {
                    LOGGER.info("Stopping processing of remaining license charge requests" +
                            " as device licenses have expired");
                    LICENSE_LOGGER.info("Stopping processing of remaining license charge requests" +
                            " as device licenses have expired");
                    MDC.clear();
                    return false;
                }
            }
            default -> {
                pendingLicenseCharge.setChargeStatus(LicenseChargeStatus.FAILED.name());
                licenseDBService.updateLicenseCharge(pendingLicenseCharge);
            }
        }
        return true;
    }

}
